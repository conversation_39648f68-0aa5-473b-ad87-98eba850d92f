"""
Export and Reports View
=======================

View component for exporting data and generating reports.
"""

import flet as ft
from typing import Dict, Any, Optional, List

from .base_view import BaseView


class ExportView(BaseView):
    """View for export and report generation."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.export_formats = ["Excel", "DOCX", "HTML", "JSON"]
        self.selected_formats = ["Excel", "DOCX"]
        self.export_progress = 0.0
        self.is_exporting = False
        self.generated_files: List[tuple] = []
    
    def build_content(self) -> ft.Control:
        """Build the export view content."""
        
        # Header
        header = self.create_section_header(
            "Export & Reports",
            "Generate and export comprehensive financial reports"
        )
        
        # Export options
        export_options = self._create_export_options()
        
        # Export progress
        export_progress = self._create_export_progress()
        
        # Generated files
        generated_files = self._create_generated_files()
        
        return ft.Column([
            header,
            export_options,
            export_progress,
            generated_files
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_export_options(self) -> ft.Card:
        """Create export options interface."""
        
        # Format selection
        format_checkboxes = []
        for fmt in self.export_formats:
            checkbox = ft.Checkbox(
                label=fmt,
                value=fmt in self.selected_formats,
                on_change=lambda e, f=fmt: self._on_format_selected(f, e.control.value)
            )
            format_checkboxes.append(checkbox)
        
        # Export buttons
        export_buttons = ft.Row([
            self.create_action_button(
                "Quick Export",
                ft.Icons.DOWNLOAD,
                self._on_quick_export,
                ft.Colors.BLUE_600
            ),
            self.create_action_button(
                "Comprehensive Report",
                ft.Icons.DESCRIPTION,
                self._on_comprehensive_export,
                ft.Colors.GREEN_600
            ),
            self.create_action_button(
                "Custom Export",
                ft.Icons.TUNE,
                self._on_custom_export,
                ft.Colors.ORANGE_600
            )
        ], alignment=ft.MainAxisAlignment.CENTER)
        
        options_content = ft.Column([
            ft.Text("Export Formats:", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(format_checkboxes),
            ft.Divider(height=20),
            ft.Text("Export Options:", size=16, weight=ft.FontWeight.BOLD),
            export_buttons
        ])
        
        return self.create_card(
            "Export Configuration",
            options_content,
            icon=ft.Icons.SETTINGS
        )
    
    def _create_export_progress(self) -> ft.Card:
        """Create export progress display."""
        if not self.is_exporting:
            return ft.Card(visible=False)
        
        progress_content = ft.Column([
            ft.Text("Export in Progress...", size=16, weight=ft.FontWeight.BOLD),
            self.create_progress_bar(self.export_progress),
            ft.Text(f"Progress: {self.export_progress:.0f}%", size=12, color=ft.Colors.GREY_600)
        ])
        
        return self.create_card(
            "Export Progress",
            progress_content,
            icon=ft.Icons.HOURGLASS_EMPTY,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_generated_files(self) -> ft.Card:
        """Create generated files display."""
        if not self.generated_files:
            return self.create_card(
                "Generated Files",
                ft.Text("No files generated yet", text_align=ft.TextAlign.CENTER),
                icon=ft.Icons.FOLDER_OPEN
            )
        
        file_list = ft.Column([])
        
        for file_type, file_path in self.generated_files:
            file_row = ft.Row([
                ft.Icon(self._get_file_icon(file_type), color=ft.Colors.BLUE),
                ft.Text(file_type, expand=1),
                ft.Text(str(file_path.name) if hasattr(file_path, 'name') else str(file_path), 
                       size=12, color=ft.Colors.GREY_600, expand=2),
                ft.IconButton(
                    icon=ft.Icons.OPEN_IN_NEW,
                    tooltip="Open file location",
                    on_click=lambda e, path=file_path: self._on_open_file(path)
                )
            ])
            file_list.controls.append(file_row)
        
        return self.create_card(
            "Generated Files",
            file_list,
            icon=ft.Icons.FOLDER_OPEN,
            bgcolor=ft.Colors.GREEN_50
        )
    
    def _get_file_icon(self, file_type: str) -> str:
        """Get appropriate icon for file type."""
        icons = {
            "Excel Report": ft.Icons.TABLE_VIEW,
            "DOCX Report": ft.Icons.DESCRIPTION,
            "HTML Report": ft.Icons.WEB,
            "Raw Data (JSON)": ft.Icons.DATA_OBJECT
        }
        return icons.get(file_type, ft.Icons.INSERT_DRIVE_FILE)
    
    def _on_format_selected(self, format_name: str, selected: bool):
        """Handle format selection change."""
        if selected and format_name not in self.selected_formats:
            self.selected_formats.append(format_name)
        elif not selected and format_name in self.selected_formats:
            self.selected_formats.remove(format_name)
    
    def _on_quick_export(self, e):
        """Handle quick export action."""
        if not self.selected_formats:
            self.show_error("Please select at least one export format")
            return
        
        self.request_action("quick_export", {
            "formats": self.selected_formats
        })
    
    def _on_comprehensive_export(self, e):
        """Handle comprehensive export action."""
        self.request_action("comprehensive_export", {
            "formats": self.selected_formats,
            "include_all_analysis": True
        })
    
    def _on_custom_export(self, e):
        """Handle custom export action."""
        # This would open a dialog for custom export options
        self.show_success("Custom export dialog would open here")
    
    def _on_open_file(self, file_path):
        """Handle open file action."""
        # This would open the file or its location
        self.show_success(f"Would open: {file_path}")
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with export data."""
        if "export_progress" in data:
            self.export_progress = data["export_progress"]
            self.is_exporting = data.get("is_exporting", False)
            self.refresh()
        
        if "generated_files" in data:
            self.generated_files = data["generated_files"]
            self.refresh()
    
    def set_export_progress(self, progress: float, is_exporting: bool = True):
        """Set export progress."""
        self.export_progress = progress
        self.is_exporting = is_exporting
        self.refresh()
    
    def add_generated_file(self, file_type: str, file_path):
        """Add a generated file to the list."""
        self.generated_files.append((file_type, file_path))
        self.refresh()
    
    def clear_generated_files(self):
        """Clear the generated files list."""
        self.generated_files = []
        self.refresh()
