"""
Enhanced Dashboard View
======================

Redesigned dashboard view incorporating all UI/UX improvements:
- Workflow wizard for simplified navigation
- Progressive disclosure dashboard
- Contextual help system
- Enhanced accessibility features
"""

import flet as ft
from typing import Dict, Any, Optional, Callable
import logging

from .base_view import BaseView

# Enhanced UI Components
from components.ui.workflow_wizard import WorkflowWizard, WorkflowStep
from components.ui.enhanced_dashboard_redesign import EnhancedDashboardRedesign, DashboardSection
from components.ui.contextual_help_system import ContextualHelpSystem
from components.ui.modern_theme_system import get_theme, ComponentSize
from components.ui.modern_components import ModernButton, ButtonVariant
from components.ui.accessibility_system import get_accessibility_manager, ScreenReaderSupport

class EnhancedDashboardView(BaseView):
    """Enhanced dashboard view with improved UX and accessibility."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
        self.ml_predictions: Optional[Dict[str, Any]] = None
        self.charts_3d: Optional[Dict[str, str]] = None
        self.logger = logging.getLogger(__name__)
        self.theme = get_theme()
        self.accessibility_manager = get_accessibility_manager()
        
        # UI State
        self.current_mode = "workflow"  # "workflow", "dashboard", "help"
        self.workflow_completed = False
        self.show_onboarding = True  # Would be based on user preferences
        
        # Initialize enhanced components
        self.workflow_wizard = WorkflowWizard(
            on_step_change=self._handle_workflow_step_change,
            on_action=self._handle_workflow_action
        )
        
        self.enhanced_dashboard = EnhancedDashboardRedesign(
            financial_results=self.financial_results,
            ml_predictions=self.ml_predictions,
            charts_3d=self.charts_3d,
            on_action=self._handle_dashboard_action
        )
        
        self.help_system = ContextualHelpSystem(
            on_action=self._handle_help_action
        )
        
        # Track user interaction for UX improvements
        self.interaction_analytics = {
            'sections_visited': set(),
            'help_terms_viewed': [],
            'widgets_expanded': [],
            'time_spent': 0
        }
    
    def build_content(self) -> ft.Control:
        """Build the enhanced dashboard view content."""
        
        # Determine what to show based on state
        if self.current_mode == "help":
            return self._create_help_mode()
        elif self.current_mode == "workflow" and not self.workflow_completed:
            return self._create_workflow_mode()
        else:
            return self._create_dashboard_mode()
    
    def _create_workflow_mode(self) -> ft.Control:
        """Create workflow-guided mode for new or returning users."""
        
        # Header with mode toggle
        header = self._create_mode_header()
        
        # Main workflow wizard
        workflow_content = self.workflow_wizard.build()
        
        # Contextual help sidebar
        help_sidebar = self.help_system.create_contextual_help_sidebar("setup")
        
        # Layout
        main_content = ft.Row([
            ft.Container(
                content=workflow_content,
                expand=True,
                padding=ft.padding.only(right=10)
            ),
            help_sidebar
        ], expand=True) if help_sidebar.content else ft.Container(content=workflow_content, expand=True)
        
        return ft.Column([
            header,
            ft.Container(height=16),
            main_content,
            ft.Container(height=16),
            self._create_workflow_footer()
        ], expand=True)
    
    def _create_dashboard_mode(self) -> ft.Control:
        """Create enhanced dashboard mode with progressive disclosure."""
        
        if not self.financial_results and not self.ml_predictions:
            return self._create_smart_empty_state()
        
        # Header with mode toggle and quick actions
        header = self._create_mode_header()
        
        # Update dashboard with current data
        self.enhanced_dashboard.financial_results = self.financial_results
        self.enhanced_dashboard.ml_predictions = self.ml_predictions
        self.enhanced_dashboard.charts_3d = self.charts_3d
        
        # Main dashboard content
        dashboard_content = self.enhanced_dashboard.build()
        
        # Floating help button
        floating_help = self._create_floating_help_button()
        
        return ft.Stack([
            ft.Column([
                header,
                ft.Container(height=16),
                dashboard_content
            ], expand=True),
            floating_help
        ], expand=True)
    
    def _create_help_mode(self) -> ft.Control:
        """Create dedicated help mode."""
        
        # Header with back button
        header = ft.Row([
            ft.IconButton(
                icon=ft.Icons.ARROW_BACK,
                tooltip="Back to Dashboard",
                on_click=lambda e: self._set_mode("dashboard"),
                icon_color=self.theme.get_semantic_color('primary')
            ),
            ft.Text(
                "Financial Analysis Help Center",
                size=20,
                weight=ft.FontWeight.W_700,
                color=self.theme.get_text_colors()['primary'],
                expand=True
            ),
            ft.IconButton(
                icon=ft.Icons.CLOSE,
                tooltip="Close Help",
                on_click=lambda e: self._set_mode("dashboard"),
                icon_color=self.theme.get_semantic_color('neutral', '600')
            )
        ])
        
        # Full-screen help panel
        help_content = self.help_system.create_help_panel()
        
        return ft.Column([
            header,
            ft.Container(height=16),
            ft.Container(
                content=help_content,
                expand=True,
                alignment=ft.alignment.center
            )
        ], expand=True)
    
    def _create_mode_header(self) -> ft.Control:
        """Create header with mode toggles and quick actions."""
        
        # Mode toggle buttons
        mode_buttons = ft.Row([
            self._create_mode_button("🎯 Guided", "workflow", "Step-by-step workflow"),
            self._create_mode_button("📊 Dashboard", "dashboard", "Interactive dashboard"),
            self._create_mode_button("💡 Help", "help", "Learn and get assistance")
        ], spacing=8)
        
        # Quick actions
        quick_actions = ft.Row([
            ft.IconButton(
                icon=ft.Icons.REFRESH,
                tooltip="Refresh Data",
                on_click=self._refresh_data,
                icon_color=self.theme.get_semantic_color('primary')
            ),
            ft.IconButton(
                icon=ft.Icons.FULLSCREEN,
                tooltip="Toggle Fullscreen",
                on_click=self._toggle_fullscreen,
                icon_color=self.theme.get_semantic_color('primary')
            ),
            ft.IconButton(
                icon=ft.Icons.SETTINGS,
                tooltip="Dashboard Settings",
                on_click=self._show_settings,
                icon_color=self.theme.get_semantic_color('neutral', '600')
            )
        ], spacing=4)
        
        # Progress indicator (if in workflow mode)
        progress_indicator = None
        if self.current_mode == "workflow":
            progress = self.workflow_wizard.get_completion_progress()
            progress_indicator = ft.Container(
                content=ft.Column([
                    ft.Text(f"Progress: {progress:.0f}%", size=12, 
                           color=self.theme.get_text_colors()['secondary']),
                    ft.ProgressBar(value=progress/100, height=4, 
                                 color=self.theme.get_semantic_color('primary'))
                ], spacing=4),
                width=120
            )
        
        return ft.Container(
            content=ft.Row([
                mode_buttons,
                ft.Container(expand=True),
                progress_indicator if progress_indicator else ft.Container(),
                quick_actions
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            bgcolor=self.theme.get_background_colors()['surface'],
            border_radius=12,
            border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200'))
        )
    
    def _create_mode_button(self, text: str, mode: str, tooltip: str) -> ft.Control:
        """Create mode toggle button."""
        is_active = self.current_mode == mode
        
        return ft.Container(
            content=ft.Text(
                text,
                size=12,
                weight=ft.FontWeight.W_500,
                color=ft.Colors.WHITE if is_active else self.theme.get_text_colors()['secondary']
            ),
            padding=ft.padding.symmetric(horizontal=12, vertical=8),
            bgcolor=self.theme.get_semantic_color('primary') if is_active else 'transparent',
            border_radius=6,
            border=ft.border.all(1, self.theme.get_semantic_color('primary', '300')),
            on_click=lambda e, m=mode: self._set_mode(m),
            ink=True,
            tooltip=tooltip
        )
    
    def _create_smart_empty_state(self) -> ft.Control:
        """Create intelligent empty state with contextual guidance."""
        
        # Check user's previous interactions to provide better guidance
        has_used_app = len(self.interaction_analytics['sections_visited']) > 0
        
        if has_used_app:
            # Returning user
            title = "Welcome Back! 👋"
            subtitle = "Ready to run a new financial analysis?"
            primary_action = "🚀 Run New Analysis"
            secondary_actions = [
                ("📁 Load Previous Project", self._load_previous_project),
                ("📖 Review Help Guide", lambda: self._set_mode("help"))
            ]
        else:
            # New user
            title = "Welcome to Enhanced Financial Analysis! 🎉"
            subtitle = "Let's get started with your solar project analysis"
            primary_action = "🎯 Start Guided Setup"
            secondary_actions = [
                ("📖 Learn the Basics", lambda: self._set_mode("help")),
                ("⚡ Quick Demo", self._start_demo)
            ]
        
        # Main empty state content
        main_content = ft.Column([
            ft.Icon(ft.Icons.SOLAR_POWER, size=80, 
                   color=self.theme.get_semantic_color('primary')),
            ft.Container(height=20),
            ft.Text(title, size=28, weight=ft.FontWeight.W_700,
                   color=self.theme.get_text_colors()['primary'],
                   text_align=ft.TextAlign.CENTER),
            ft.Text(subtitle, size=16, 
                   color=self.theme.get_text_colors()['secondary'],
                   text_align=ft.TextAlign.CENTER),
            ft.Container(height=30),
            
            # Primary action
            ModernButton(
                primary_action,
                variant=ButtonVariant.PRIMARY,
                size=ComponentSize.LG,
                on_click=lambda e: self._set_mode("workflow")
            ).build(),
            
            ft.Container(height=20),
            
            # Secondary actions
            ft.Row([
                ModernButton(
                    action[0],
                    variant=ButtonVariant.SECONDARY,
                    size=ComponentSize.MD,
                    on_click=lambda e, action_fn=action[1]: action_fn()
                ).build() for action in secondary_actions
            ], alignment=ft.MainAxisAlignment.CENTER, spacing=12)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        # Feature highlights
        features = [
            ("🤖", "AI-Powered Insights", "Machine learning predictions and recommendations"),
            ("📊", "3D Visualizations", "Interactive risk analysis and scenario modeling"),
            ("⚡", "Real-time Analysis", "Instant results with professional-grade accuracy"),
            ("📱", "Responsive Design", "Optimized for all screen sizes and devices")
        ]
        
        feature_cards = []
        for icon, title, description in features:
            feature_cards.append(
                ft.Container(
                    content=ft.Column([
                        ft.Text(icon, size=32),
                        ft.Text(title, size=14, weight=ft.FontWeight.W_600),
                        ft.Text(description, size=12, 
                               color=self.theme.get_text_colors()['secondary'],
                               text_align=ft.TextAlign.CENTER)
                    ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                    padding=ft.padding.all(20),
                    bgcolor=self.theme.get_background_colors()['surface'],
                    border_radius=12,
                    border=ft.border.all(1, self.theme.get_semantic_color('neutral', '200')),
                    width=200
                )
            )
        
        features_section = ft.Column([
            ft.Text("✨ What's New in 2025", size=20, weight=ft.FontWeight.W_600,
                   color=self.theme.get_text_colors()['primary'],
                   text_align=ft.TextAlign.CENTER),
            ft.Container(height=16),
            ft.Row(feature_cards, alignment=ft.MainAxisAlignment.CENTER, 
                  spacing=16, wrap=True)
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        
        return ft.Container(
            content=ft.Column([
                main_content,
                ft.Container(height=40),
                features_section,
                ft.Container(height=40)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            padding=ft.padding.all(40),
            expand=True,
            alignment=ft.alignment.center
        )
    
    def _create_workflow_footer(self) -> ft.Control:
        """Create footer for workflow mode with tips and shortcuts."""
        
        # Keyboard shortcuts
        shortcuts = [
            ("Ctrl+S", "Save Project"),
            ("Ctrl+H", "Show Help"), 
            ("Ctrl+Enter", "Run Analysis"),
            ("Tab", "Navigate Fields")
        ]
        
        shortcut_chips = []
        for key, action in shortcuts:
            shortcut_chips.append(
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Text(key, size=10, color=ft.Colors.WHITE),
                            padding=ft.padding.symmetric(horizontal=6, vertical=2),
                            bgcolor=self.theme.get_semantic_color('neutral', '700'),
                            border_radius=4
                        ),
                        ft.Text(action, size=11, color=self.theme.get_text_colors()['secondary'])
                    ], spacing=6),
                    padding=ft.padding.symmetric(horizontal=8, vertical=4)
                )
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text("💡 Tip: Use keyboard shortcuts for faster navigation", 
                           size=12, color=self.theme.get_text_colors()['tertiary'],
                           expand=True),
                    ft.TextButton(
                        "View All Shortcuts",
                        style=ft.ButtonStyle(color=self.theme.get_semantic_color('primary')),
                        on_click=self._show_shortcuts_help
                    )
                ]),
                ft.Row(shortcut_chips, spacing=8, scroll=ft.ScrollMode.AUTO)
            ], spacing=8),
            padding=ft.padding.all(16),
            bgcolor=self.theme.get_semantic_color('info', '50'),
            border_radius=8,
            border=ft.border.all(1, self.theme.get_semantic_color('info', '200'))
        )
    
    def _create_floating_help_button(self) -> ft.Control:
        """Create floating help button for dashboard mode."""
        return ft.Positioned(
            content=ft.FloatingActionButton(
                icon=ft.Icons.HELP_OUTLINE,
                tooltip="Get Help",
                on_click=lambda e: self._set_mode("help"),
                bgcolor=self.theme.get_semantic_color('primary'),
                foreground_color=ft.Colors.WHITE,
                mini=True
            ),
            right=20,
            bottom=20
        )
    
    # Event Handlers
    def _handle_workflow_step_change(self, step: WorkflowStep):
        """Handle workflow step changes."""
        self.logger.info(f"Workflow step changed to: {step.value}")
        self.interaction_analytics['sections_visited'].add(step.value)
        
        # Update contextual help
        context = step.value
        if hasattr(self.help_system, 'update_context'):
            self.help_system.update_context(context)
        
        # Announce to screen reader
        self.accessibility_manager.announce_to_screen_reader(
            f"Moved to step {step.value.replace('_', ' ').title()}",
            "polite"
        )
    
    def _handle_workflow_action(self, action: str, params: Dict[str, Any]):
        """Handle workflow actions."""
        self.logger.info(f"Workflow action: {action}, params: {params}")
        
        if action == "navigate_setup":
            if self.on_navigate:
                self.on_navigate("project_setup")
        elif action == "run_analysis":
            if self.on_action_requested:
                self.on_action_requested("run_comprehensive_analysis", {})
        elif action == "view_results":
            self._set_mode("dashboard")
        elif action == "generate_exports":
            if self.on_navigate:
                self.on_navigate("export")
    
    def _handle_dashboard_action(self, action: str, params: Dict[str, Any]):
        """Handle dashboard actions."""
        self.logger.info(f"Dashboard action: {action}, params: {params}")
        
        if action == "start_analysis":
            self._set_mode("workflow")
        elif action == "export_dashboard":
            if self.on_action_requested:
                self.on_action_requested("export_dashboard", params)
    
    def _handle_help_action(self, action: str, params: Dict[str, Any]):
        """Handle help system actions."""
        self.logger.info(f"Help action: {action}, params: {params}")
        
        if action == "watch_tutorial":
            self._watch_tutorial(params.get("term_id"))
        elif action == "start_tutorial":
            self._start_interactive_tutorial()
    
    def _set_mode(self, mode: str):
        """Set the current view mode."""
        self.current_mode = mode
        self.logger.info(f"Dashboard mode changed to: {mode}")
        
        # Update analytics
        self.interaction_analytics['sections_visited'].add(mode)
        
        # Refresh the view
        self.refresh()
    
    def _refresh_data(self, e):
        """Refresh dashboard data."""
        if self.on_action_requested:
            self.on_action_requested("refresh_data", {})
    
    def _toggle_fullscreen(self, e):
        """Toggle fullscreen mode."""
        # Would implement fullscreen toggle
        pass
    
    def _show_settings(self, e):
        """Show dashboard settings."""
        # Would show settings dialog
        pass
    
    def _load_previous_project(self):
        """Load previous project."""
        if self.on_action_requested:
            self.on_action_requested("load_project", {})
    
    def _start_demo(self):
        """Start application demo."""
        if self.on_action_requested:
            self.on_action_requested("start_demo", {})
    
    def _show_shortcuts_help(self, e):
        """Show keyboard shortcuts help."""
        # Would show shortcuts dialog
        pass
    
    def _watch_tutorial(self, term_id: str):
        """Watch tutorial for specific term."""
        # Would open tutorial video/content
        pass
    
    def _start_interactive_tutorial(self):
        """Start interactive application tutorial."""
        # Would start guided tour
        pass
    
    # Data Management
    def set_financial_results(self, results: Dict[str, Any]):
        """Set financial results and update components."""
        self.financial_results = results
        
        # Update enhanced dashboard
        if hasattr(self, 'enhanced_dashboard'):
            self.enhanced_dashboard.financial_results = results
        
        # Mark analysis step as completed in workflow
        if hasattr(self, 'workflow_wizard'):
            self.workflow_wizard.mark_step_completed(WorkflowStep.ANALYSIS)
            self.workflow_wizard.mark_step_completed(WorkflowStep.RESULTS)
        
        # Switch to dashboard mode if we have results and user was in workflow
        if self.current_mode == "workflow" and results:
            self._set_mode("dashboard")
        
        self.logger.info("Enhanced dashboard updated with financial results")
        self.refresh()
    
    def set_ml_predictions(self, predictions: Dict[str, Any]):
        """Set ML predictions."""
        self.ml_predictions = predictions
        
        if hasattr(self, 'enhanced_dashboard'):
            self.enhanced_dashboard.ml_predictions = predictions
        
        self.refresh()
    
    def set_3d_charts(self, charts_3d: Dict[str, str]):
        """Set 3D charts."""
        self.charts_3d = charts_3d
        
        if hasattr(self, 'enhanced_dashboard'):
            self.enhanced_dashboard.charts_3d = charts_3d
        
        self.refresh()
    
    def force_refresh_with_data(self, financial_results: Dict[str, Any], 
                               ml_predictions: Optional[Dict[str, Any]] = None,
                               charts_3d: Optional[Dict[str, str]] = None):
        """Force refresh with all data at once."""
        self.financial_results = financial_results
        if ml_predictions:
            self.ml_predictions = ml_predictions
        if charts_3d:
            self.charts_3d = charts_3d
        
        # Update enhanced dashboard
        if hasattr(self, 'enhanced_dashboard'):
            self.enhanced_dashboard.financial_results = financial_results
            self.enhanced_dashboard.ml_predictions = ml_predictions
            self.enhanced_dashboard.charts_3d = charts_3d
        
        # Update workflow completion
        if hasattr(self, 'workflow_wizard') and financial_results:
            self.workflow_wizard.mark_step_completed(WorkflowStep.ANALYSIS)
            self.workflow_wizard.mark_step_completed(WorkflowStep.RESULTS)
            self.workflow_completed = True
        
        # Switch to dashboard mode
        if financial_results and self.current_mode == "workflow":
            self._set_mode("dashboard")
        
        self.logger.info("Enhanced dashboard force refreshed with comprehensive data")
        self.refresh()
    
    def has_data(self) -> bool:
        """Check if dashboard has data to display."""
        return self.financial_results is not None or self.ml_predictions is not None
    
    def update_data(self, data: Dict[str, Any]):
        """Update dashboard with new data."""
        if "financial_results" in data:
            self.set_financial_results(data["financial_results"])
        if "ml_predictions" in data:
            self.set_ml_predictions(data["ml_predictions"])
        if "charts_3d" in data:
            self.set_3d_charts(data["charts_3d"])
