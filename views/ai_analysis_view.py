"""
AI Analysis View
================

View for displaying AI-powered analysis results and providing interactive chat interface.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from views.base_view import BaseView
from app.app_state import AppState


def create_message_bubble(message: Dict[str, Any]) -> ft.Control:
    """Create a message bubble for chat interface."""
    role = message.get("role", "user")
    text = message.get("text", "")
    timestamp = message.get("timestamp", datetime.now().isoformat())
    
    # Parse timestamp
    try:
        ts = datetime.fromisoformat(timestamp)
        time_str = ts.strftime("%H:%M")
    except:
        time_str = "now"
    
    # Color and alignment based on role
    if role == "user":
        bgcolor = ft.Colors.BLUE_100
        text_color = ft.Colors.BLACK
        alignment = ft.MainAxisAlignment.END
        border_radius = ft.border_radius.only(15, 15, 5, 15)
    else:
        bgcolor = ft.Colors.GREY_100
        text_color = ft.Colors.BLACK
        alignment = ft.MainAxisAlignment.START
        border_radius = ft.border_radius.only(15, 15, 15, 5)
    
    # Message content
    content = ft.Container(
        content=ft.Column([
            ft.Text(
                text,
                size=14,
                color=text_color,
                selectable=True
            ),
            ft.Text(
                time_str,
                size=10,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.RIGHT
            )
        ], spacing=2),
        bgcolor=bgcolor,
        border_radius=border_radius,
        padding=12,
        margin=ft.margin.only(bottom=8),
        width=None
    )
    
    # Wrap in row for alignment
    return ft.Row(
        [content],
        alignment=alignment,
        wrap=False
    )


class AIAnalysisView(BaseView):
    """View for AI analysis results and interactive chat interface."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.logger = logging.getLogger(__name__)
        
        # Get app state for persistence
        self.app_state = AppState()
        
        # Initialize chat components
        self.chat_history: List[Dict[str, Any]] = []
        self.chat_container = ft.ListView(
            expand=True,
            spacing=5,
            padding=ft.padding.all(10),
            auto_scroll=True
        )
        
        # Chat input components
        self.message_input = ft.TextField(
            hint_text="Ask me about your project analysis...",
            expand=True,
            multiline=True,
            max_lines=3,
            min_lines=1,
            on_submit=self._on_send_message,
            border_color=ft.Colors.BLUE_300
        )
        
        self.send_button = ft.IconButton(
            icon=ft.Icons.SEND,
            tooltip="Send message",
            on_click=self._on_send_message,
            bgcolor=ft.Colors.BLUE_600,
            icon_color=ft.Colors.WHITE
        )
        
        # Loading indicator
        self.loading_indicator = ft.Container(
            content=ft.Row([
                ft.ProgressRing(width=20, height=20, color=ft.Colors.BLUE_600),
                ft.Text("AI is thinking...", color=ft.Colors.BLUE_600)
            ], alignment=ft.MainAxisAlignment.CENTER),
            visible=False,
            padding=10
        )
        
        # Load existing chat history from app state
        self._load_chat_history()
        
    def _load_chat_history(self):
        """Load chat history from app state."""
        if hasattr(self.app_state, 'ai_chat_history') and self.app_state.ai_chat_history:
            self.chat_history = self.app_state.ai_chat_history
            self._refresh_chat_display()
        
    def _save_chat_history(self):
        """Save chat history to app state."""
        self.app_state.ai_chat_history = self.chat_history
        
    def build_content(self) -> ft.Control:
        """Build the AI analysis view content."""
        return ft.Container(
            content=ft.Column([
                # Header section
                self._create_header(),
                
                # Main content area
                ft.Container(
                    content=self._create_main_content(),
                    expand=True,
                    padding=20
                )
            ], 
            spacing=0,
            expand=True),
            expand=True
        )
    
    def _create_header(self) -> ft.Container:
        """Create header section."""
        return ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.AUTO_AWESOME, size=32, color=ft.Colors.BLUE_600),
                ft.Text(
                    "AI Analysis & Chat",
                    size=28,
                    weight=ft.FontWeight.BOLD
                ),
                ft.Container(expand=True),
                ft.ElevatedButton(
                    "Run Initial Analysis",
                    icon=ft.Icons.PLAY_ARROW,
                    on_click=self._handle_run_analysis
                ),
                ft.ElevatedButton(
                    "Clear Chat",
                    icon=ft.Icons.CLEAR,
                    on_click=self._clear_chat
                )
            ]),
            padding=20,
            bgcolor=ft.Colors.GREY_100
        )
    
    def _create_main_content(self) -> ft.Control:
        """Create main content area."""
        return ft.Column([
            # Initial analysis display (if available)
            self._create_initial_analysis_display(),
            
            # Chat interface
            self._create_chat_interface()
        ], 
        spacing=20,
        expand=True)
    
    def _create_initial_analysis_display(self) -> ft.Control:
        """Create initial analysis display."""
        if not hasattr(self.app_state, 'ai_initial_analysis') or not self.app_state.ai_initial_analysis:
            return ft.Container()
        
        initial_analysis = self.app_state.ai_initial_analysis
        
        return self.create_card(
            title="Initial AI Analysis",
            icon=ft.Icons.ANALYTICS,
            content=ft.Column([
                ft.Markdown(
                    value=initial_analysis,
                    selectable=True,
                    extension_set=ft.MarkdownExtensionSet.GITHUB_FLAVORED,
                    auto_follow_links=False
                )
            ])
        )
    
    def _create_chat_interface(self) -> ft.Control:
        """Create chat interface."""
        return self.create_card(
            title="AI Chat Assistant",
            icon=ft.Icons.CHAT,
            content=ft.Column([
                # Chat history container
                ft.Container(
                    content=self.chat_container,
                    height=400,
                    border=ft.border.all(1, ft.Colors.GREY_300),
                    border_radius=8,
                    padding=5
                ),
                
                # Loading indicator
                self.loading_indicator,
                
                # Chat input
                ft.Container(
                    content=ft.Row([
                        self.message_input,
                        self.send_button
                    ], spacing=10),
                    padding=ft.padding.only(top=10)
                )
            ], spacing=10)
        )
    
    def _on_send_message(self, e):
        """Handle sending a message."""
        message_text = self.message_input.value.strip()
        if not message_text:
            return
        
        # Add user message to history
        user_message = {
            "role": "user",
            "text": message_text,
            "timestamp": datetime.now().isoformat()
        }
        self.chat_history.append(user_message)
        
        # Clear input
        self.message_input.value = ""
        
        # Show loading indicator
        self.loading_indicator.visible = True
        
        # Update UI
        self._refresh_chat_display()
        self.page.update()
        
        # Send message to AI service
        self._send_message_to_ai(message_text)
    
    def _send_message_to_ai(self, message: str):
        """Send message to AI service."""
        # Request AI chat response from controller
        self.request_action("ai_chat_message", {
            "message": message,
            "chat_history": self.chat_history
        })
    
    def _handle_run_analysis(self, e):
        """Handle run AI analysis button click."""
        self.logger.info("Running initial AI analysis...")
        
        # Request AI analysis from controller
        self.request_action("run_ai_analysis", {
            "analysis_type": "initial"
        })
    
    def _clear_chat(self, e):
        """Clear chat history."""
        self.chat_history = []
        self._refresh_chat_display()
        self._save_chat_history()
        self.page.update()
    
    def _refresh_chat_display(self):
        """Refresh the chat display with current history."""
        self.chat_container.controls = [
            create_message_bubble(message) for message in self.chat_history
        ]
        
        # Save to app state
        self._save_chat_history()
        
        # Scroll to bottom
        if self.chat_container.controls:
            self.chat_container.scroll_to_bottom()
    
    def add_ai_response(self, response: str):
        """Add AI response to chat history."""
        ai_message = {
            "role": "assistant",
            "text": response,
            "timestamp": datetime.now().isoformat()
        }
        self.chat_history.append(ai_message)
        
        # Hide loading indicator
        self.loading_indicator.visible = False
        
        # Update chat display
        self._refresh_chat_display()
        self.page.update()
    
    def set_initial_analysis(self, analysis: str):
        """Set initial analysis results."""
        self.app_state.ai_initial_analysis = analysis
        
        # Refresh the view to show initial analysis
        self.refresh()
        self.page.update()
    
    def set_analysis_results(self, results: Dict[str, Any]):
        """Set comprehensive analysis results."""
        # Store results in app state
        self.app_state.ai_analysis_results = results
        
        # Create a formatted summary for display
        summary = self._format_analysis_summary(results)
        
        # Set as initial analysis for display
        self.app_state.ai_initial_analysis = summary
        
        # Add welcome message to chat
        welcome_message = {
            "role": "assistant",
            "text": "Analysis completed! I've analyzed your project and identified key insights. Feel free to ask me any questions about the results.",
            "timestamp": datetime.now().isoformat()
        }
        self.chat_history.append(welcome_message)
        
        # Refresh the view to show results
        self.refresh()
        self._refresh_chat_display()
        self.page.update()
    
    def _format_analysis_summary(self, results: Dict[str, Any]) -> str:
        """Format analysis results into a readable summary."""
        summary_parts = []
        
        # Add financial analysis summary
        if 'financial_analysis' in results:
            summary_parts.append("## Financial Analysis")
            summary_parts.append(results['financial_analysis'].get('summary', 'No summary available'))
            summary_parts.append("")
        
        # Add insights
        if 'insights' in results:
            insights = results['insights']
            summary_parts.append("## Key Insights")
            if 'viability' in insights:
                summary_parts.append(f"**Viability:** {insights['viability']}")
            if 'viability_score' in insights:
                score = insights['viability_score'] * 100
                summary_parts.append(f"**Viability Score:** {score:.1f}%")
            if 'risk_level' in insights:
                summary_parts.append(f"**Risk Level:** {insights['risk_level']}")
            summary_parts.append("")
        
        # Add risk analysis
        if 'risk_analysis' in results and 'risks' in results['risk_analysis']:
            summary_parts.append("## Risk Analysis")
            for risk in results['risk_analysis']['risks']:
                summary_parts.append(f"**{risk['factor']}**")
                summary_parts.append(f"- Impact: {risk['impact']}")
                summary_parts.append(f"- Probability: {risk['probability']}")
                summary_parts.append(f"- Mitigation: {risk['mitigation']}")
                summary_parts.append("")
        
        # Add recommendations
        if 'recommendations' in results:
            summary_parts.append("## Recommendations")
            for i, rec in enumerate(results['recommendations'], 1):
                summary_parts.append(f"{i}. {rec['title']}")
            summary_parts.append("")
        
        # Add ML enhancement note if available
        if results.get('ml_enhanced'):
            summary_parts.append("---")
            summary_parts.append("*This analysis has been enhanced with machine learning predictions for improved accuracy.*")
        
        return "\n".join(summary_parts)
    
    def handle_ai_error(self, error: str):
        """Handle AI service error."""
        error_message = {
            "role": "assistant",
            "text": f"I apologize, but I encountered an error: {error}. Please try again or contact support if the problem persists.",
            "timestamp": datetime.now().isoformat()
        }
        self.chat_history.append(error_message)
        
        # Hide loading indicator
        self.loading_indicator.visible = False
        
        # Update chat display
        self._refresh_chat_display()
        self.page.update()
