"""
Financial Model View
====================

View component for detailed financial model results.
"""

import flet as ft
import pandas as pd
from typing import Dict, Any, Optional
import logging

from .base_view import BaseView


class FinancialModelView(BaseView):
    """View for detailed financial model results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
        self.logger = logging.getLogger(__name__)
    
    def build_content(self) -> ft.Control:
        """Build the financial model view content."""
        
        if not self.financial_results:
            return self.create_empty_state(
                "No Financial Results",
                "Run the financial model first to see detailed results",
                "Go to Project Setup",
                lambda: self.navigate_to("project_setup")
            )
        
        # Header
        header = self.create_section_header(
            "Financial Model Results",
            "Detailed financial analysis and cashflow projections"
        )
        
        # KPI Summary
        kpi_summary = self._create_kpi_summary()
        
        # Cashflow Table
        cashflow_table = self._create_cashflow_table()
        
        # Additional Metrics
        additional_metrics = self._create_additional_metrics()
        
        return ft.Column([
            header,
            kpi_summary,
            cashflow_table,
            additional_metrics
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_kpi_summary(self) -> ft.Card:
        """Create KPI summary section."""
        kpis = self.financial_results.get('kpis', {})
        
        kpi_data = [
            ("Project IRR", f"{kpis.get('IRR_project', 0):.1%}"),
            ("Equity IRR", f"{kpis.get('IRR_equity', 0):.1%}"),
            ("NPV Project", self.format_currency(kpis.get('NPV_project', 0))),
            ("NPV Equity", self.format_currency(kpis.get('NPV_equity', 0))),
            ("LCOE", f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh"),
            ("Min DSCR", f"{kpis.get('Min_DSCR', 0):.2f}"),
            ("Avg DSCR", f"{kpis.get('Avg_DSCR', 0):.2f}"),
            ("Payback Period", f"{kpis.get('Payback_years', 0):.1f} years")
        ]
        
        kpi_rows = [self.create_info_row(label, value) for label, value in kpi_data]
        
        return self.create_card(
            "Key Performance Indicators",
            ft.Column(kpi_rows),
            icon=ft.Icons.ANALYTICS
        )
    
    def _create_cashflow_table(self) -> ft.Card:
        """Create detailed cashflow table with actual data."""
        if not self.financial_results or 'cashflow' not in self.financial_results:
            # If no cashflow data, show informative placeholder
            placeholder = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.TABLE_VIEW, size=48, color=ft.Colors.GREY_400),
                    ft.Text("No Cashflow Data Available", 
                           size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600),
                    ft.Text("Run the financial model first to generate cashflow projections",
                           size=12, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_50,
                border_radius=8,
                padding=20
            )
            
            return self.create_card(
                "Cashflow Projections",
                placeholder,
                icon=ft.Icons.TABLE_VIEW
            )
        
        # Get cashflow data
        cashflow_data = self.financial_results['cashflow']
        
        # Convert to DataFrame if it's a dictionary
        import pandas as pd
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data
        
        # Define key columns to display
        key_columns = [
            'Year',
            'Revenue',
            'Total_OPEX', 
            'EBITDA',
            'Debt_Service',
            'Free_Cash_Flow',
            'Cumulative_Cash_Flow'
        ]
        
        # Filter available columns
        available_columns = [col for col in key_columns if col in df.columns]
        
        if not available_columns:
            # Fallback if expected columns are not found
            available_columns = list(df.columns)[:7]  # Show first 7 columns
        
        # Create table header
        header_cells = [
            ft.DataColumn(
                label=ft.Text(col.replace('_', ' '), weight=ft.FontWeight.BOLD, size=12)
            ) for col in available_columns
        ]
        
        # Create table rows (limit to first 25 years for readability)
        data_rows = []
        display_rows = min(len(df), 25)
        
        for i in range(display_rows):
            row_cells = []
            for col in available_columns:
                value = df.iloc[i][col] if col in df.columns else 0
                
                # Format the value based on column type
                if col == 'Year':
                    formatted_value = str(int(value)) if pd.notna(value) else ''
                elif 'Revenue' in col or 'OPEX' in col or 'EBITDA' in col or 'Cash' in col or 'Debt' in col:
                    # Financial values - format as currency in thousands
                    if pd.notna(value) and value != 0:
                        formatted_value = f"€{value/1000:.0f}k" if abs(value) < 1e6 else f"€{value/1e6:.1f}M"
                    else:
                        formatted_value = "€0"
                else:
                    formatted_value = f"{value:.2f}" if pd.notna(value) else "0"
                
                # Color coding for positive/negative values
                text_color = ft.Colors.BLACK
                if col != 'Year' and pd.notna(value):
                    if value < 0:
                        text_color = ft.Colors.RED_600
                    elif value > 0 and 'Cash' in col:
                        text_color = ft.Colors.GREEN_600
                
                row_cells.append(
                    ft.DataCell(
                        ft.Text(formatted_value, size=11, color=text_color)
                    )
                )
            
            data_rows.append(ft.DataRow(cells=row_cells))
        
        # Create the data table
        cashflow_table = ft.DataTable(
            columns=header_cells,
            rows=data_rows,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=8,
            heading_row_color=ft.Colors.BLUE_50,
            heading_row_height=40,
            show_checkbox_column=False,
            divider_thickness=1
        )
        
        # Wrap in scrollable container
        scrollable_table = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(f"Showing {display_rows} years of {len(df)} total years", 
                           size=11, color=ft.Colors.GREY_600),
                    ft.Text("Values in EUR (k=thousands, M=millions)", 
                           size=11, color=ft.Colors.GREY_600)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                ft.Container(height=5),
                cashflow_table
            ]),
            height=500,
            padding=10,
            bgcolor=ft.Colors.WHITE,
            border_radius=8
        )
        
        return self.create_card(
            "Cashflow Projections",
            scrollable_table,
            icon=ft.Icons.TABLE_VIEW
        )
    
    def _create_additional_metrics(self) -> ft.Card:
        """Create additional metrics section with comprehensive financial analysis."""
        if not self.financial_results:
            # If no financial data, show informative placeholder
            placeholder = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.CALCULATE, size=48, color=ft.Colors.GREY_400),
                    ft.Text("No Additional Metrics Available", 
                           size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_600),
                    ft.Text("Financial analysis results required to display advanced metrics",
                           size=12, color=ft.Colors.GREY_500, text_align=ft.TextAlign.CENTER),
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                height=200,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_50,
                border_radius=8,
                padding=20
            )
            
            return self.create_card(
                "Additional Metrics",
                placeholder,
                icon=ft.Icons.CALCULATE
            )
        
        # Extract data for calculations
        kpis = self.financial_results.get('kpis', {})
        assumptions = self.financial_results.get('assumptions', {})
        cashflow_data = self.financial_results.get('cashflow')
        
        # Initialize calculated metrics
        metrics = {}
        
        # Calculate additional financial metrics
        try:
            # Basic project metrics
            capacity_mw = assumptions.get('capacity_mw', 0)
            capex_meur = assumptions.get('capex_meur', 0)
            production_year1 = assumptions.get('production_mwh_year1', 0)
            
            # Calculate CAPEX per MW
            if capacity_mw > 0:
                metrics['CAPEX per MW'] = f"€{(capex_meur * 1000 / capacity_mw):.0f}k/MW"
            else:
                metrics['CAPEX per MW'] = "N/A"
            
            # Calculate capacity factor
            if capacity_mw > 0:
                theoretical_production = capacity_mw * 8760  # MWh per year
                capacity_factor = (production_year1 / theoretical_production * 100) if theoretical_production > 0 else 0
                metrics['Capacity Factor'] = f"{capacity_factor:.1f}%"
            else:
                metrics['Capacity Factor'] = "N/A"
            
            # Revenue per MWh (LCOE equivalent)
            ppa_price = assumptions.get('ppa_price_eur_kwh', 0)
            metrics['Revenue per MWh'] = f"€{ppa_price * 1000:.0f}/MWh" if ppa_price > 0 else "N/A"
            
            # Debt metrics
            debt_ratio = assumptions.get('debt_ratio', 0)
            equity_ratio = 1 - debt_ratio
            metrics['Debt-to-Equity Ratio'] = f"{debt_ratio/equity_ratio:.2f}" if equity_ratio > 0 else "N/A"
            metrics['Equity Percentage'] = f"{equity_ratio * 100:.1f}%"
            
            # Interest coverage ratio (EBITDA / Interest)
            if cashflow_data is not None:
                import pandas as pd
                if isinstance(cashflow_data, dict):
                    df = pd.DataFrame(cashflow_data)
                else:
                    df = cashflow_data
                
                if 'EBITDA' in df.columns and 'Interest_Expense' in df.columns:
                    avg_ebitda = df['EBITDA'].mean()
                    avg_interest = df['Interest_Expense'].mean()
                    if avg_interest > 0:
                        interest_coverage = avg_ebitda / avg_interest
                        metrics['Interest Coverage Ratio'] = f"{interest_coverage:.2f}x"
                    else:
                        metrics['Interest Coverage Ratio'] = "N/A"
                else:
                    metrics['Interest Coverage Ratio'] = "N/A"
            else:
                metrics['Interest Coverage Ratio'] = "N/A"
            
            # Profitability metrics from KPIs
            metrics['Project ROI'] = f"{kpis.get('IRR_project', 0):.1%}" if kpis.get('IRR_project') else "N/A"
            metrics['Equity ROI'] = f"{kpis.get('IRR_equity', 0):.1%}" if kpis.get('IRR_equity') else "N/A"
            
            # NPV per MW
            npv_project = kpis.get('NPV_project', 0)
            if capacity_mw > 0 and npv_project:
                npv_per_mw = npv_project / capacity_mw / 1e6  # Convert to M EUR per MW
                metrics['NPV per MW'] = f"€{npv_per_mw:.2f}M/MW"
            else:
                metrics['NPV per MW'] = "N/A"
            
            # Risk metrics
            min_dscr = kpis.get('Min_DSCR', 0)
            if min_dscr > 1.5:
                risk_level = "Low Risk"
                risk_color = ft.Colors.GREEN_600
            elif min_dscr > 1.2:
                risk_level = "Medium Risk"
                risk_color = ft.Colors.ORANGE_600
            else:
                risk_level = "High Risk"
                risk_color = ft.Colors.RED_600
            
            metrics['Financial Risk Level'] = risk_level
            
            # Grant efficiency
            total_grants = (assumptions.get('grant_meur_italy', 0) + 
                           assumptions.get('grant_meur_masen', 0) + 
                           assumptions.get('grant_meur_connection', 0) + 
                           assumptions.get('grant_meur_simest_africa', 0))
            
            if capex_meur > 0:
                grant_percentage = (total_grants / capex_meur * 100)
                metrics['Grant Coverage'] = f"{grant_percentage:.1f}%"
            else:
                metrics['Grant Coverage'] = "N/A"
            
        except Exception as e:
            self.logger.error(f"Error calculating additional metrics: {e}")
            metrics = {'Error': 'Unable to calculate metrics'}
        
        # Create metrics display in a grid layout
        metric_cards = []
        
        # Group metrics into categories
        financial_metrics = [
            ('Project ROI', metrics.get('Project ROI', 'N/A')),
            ('Equity ROI', metrics.get('Equity ROI', 'N/A')),
            ('NPV per MW', metrics.get('NPV per MW', 'N/A')),
            ('Interest Coverage Ratio', metrics.get('Interest Coverage Ratio', 'N/A'))
        ]
        
        technical_metrics = [
            ('CAPEX per MW', metrics.get('CAPEX per MW', 'N/A')),
            ('Capacity Factor', metrics.get('Capacity Factor', 'N/A')),
            ('Revenue per MWh', metrics.get('Revenue per MWh', 'N/A')),
            ('Grant Coverage', metrics.get('Grant Coverage', 'N/A'))
        ]
        
        risk_metrics = [
            ('Financial Risk Level', metrics.get('Financial Risk Level', 'N/A')),
            ('Debt-to-Equity Ratio', metrics.get('Debt-to-Equity Ratio', 'N/A')),
            ('Equity Percentage', metrics.get('Equity Percentage', 'N/A')),
            ('Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}" if kpis.get('Min_DSCR') else 'N/A')
        ]
        
        # Create metric sections
        sections = [
            ("💰 Financial Performance", financial_metrics, ft.Colors.GREEN_50),
            ("⚡ Technical Efficiency", technical_metrics, ft.Colors.BLUE_50),
            ("🛡️ Risk Assessment", risk_metrics, ft.Colors.ORANGE_50)
        ]
        
        section_containers = []
        for section_title, section_metrics, section_color in sections:
            metric_rows = []
            for metric_name, metric_value in section_metrics:
                # Special formatting for risk level
                text_color = ft.Colors.BLACK
                if metric_name == 'Financial Risk Level':
                    if 'Low' in str(metric_value):
                        text_color = ft.Colors.GREEN_600
                    elif 'Medium' in str(metric_value):
                        text_color = ft.Colors.ORANGE_600
                    elif 'High' in str(metric_value):
                        text_color = ft.Colors.RED_600
                
                metric_rows.append(
                    ft.Row([
                        ft.Text(f"{metric_name}:", size=12, expand=True),
                        ft.Text(str(metric_value), size=12, weight=ft.FontWeight.BOLD, color=text_color)
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                )
            
            section_container = ft.Container(
                content=ft.Column([
                    ft.Text(section_title, size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_700),
                    ft.Container(height=8),
                    ft.Column(metric_rows, spacing=5)
                ]),
                padding=15,
                bgcolor=section_color,
                border_radius=8,
                expand=True
            )
            section_containers.append(section_container)
        
        # Layout sections in a responsive grid
        metrics_content = ft.Column([
            ft.Row([section_containers[0], section_containers[1]], spacing=10),
            ft.Container(height=10),
            section_containers[2]
        ])
        
        return self.create_card(
            "Additional Financial Metrics",
            metrics_content,
            icon=ft.Icons.CALCULATE
        )
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new financial results."""
        if "financial_results" in data:
            self.financial_results = data["financial_results"]
            self.refresh()
    
    def set_financial_results(self, results: Dict[str, Any]):
        """Set financial results."""
        self.financial_results = results
        self.refresh()
