"""
Location Comparison View
========================

View component for comparing different project locations.
"""

import flet as ft
from typing import Dict, Any, Optional, List

from .base_view import BaseView
from components.charts.comparison_charts import ComparisonCharts
from components.ui.enhanced_loading_system import Enhanced<PERSON>oading<PERSON>pinner, LoadingVariant, ComponentSize


class LocationComparisonView(BaseView):
    """View for location comparison and analysis."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.comparison_results: Optional[Dict[str, Any]] = None
        self.selected_locations: List[str] = ["Ouarzazate", "Dakhla"]
        self.available_locations = [
            "Ouarzazate", "Dakhla", "Tarfaya", "Noor Midelt", 
            "Laâyoune", "Tan-Tan", "Boujdour", "Tata", "Zagora"
        ]
        self.comparison_charts = ComparisonCharts()
        self.is_loading = False
        self.loading_spinner = None
    
    def build_content(self) -> ft.Control:
        """Build the location comparison view content."""
        
        # Header
        header = self.create_section_header(
            "Location Comparison",
            "Compare different locations for optimal project placement"
        )
        
        # Location selection
        location_selection = self._create_location_selection()
        
        # Comparison results
        if self.comparison_results:
            comparison_content = self._create_comparison_results()
        else:
            comparison_content = self.create_empty_state(
                "No Comparison Results",
                "Select locations and run comparison to see results",
                "Run Comparison",
                self._on_run_comparison
            )
        
        return ft.Column([
            header,
            location_selection,
            comparison_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_location_selection(self) -> ft.Card:
        """Create location selection interface."""
        
        # Available locations checkboxes
        location_checkboxes = []
        for location in self.available_locations:
            checkbox = ft.Checkbox(
                label=location,
                value=location in self.selected_locations,
                on_change=lambda e, loc=location: self._on_location_selected(loc, e.control.value)
            )
            location_checkboxes.append(checkbox)
        
        # Organize in rows of 3
        checkbox_rows = []
        for i in range(0, len(location_checkboxes), 3):
            row_checkboxes = location_checkboxes[i:i+3]
            checkbox_rows.append(ft.Row(row_checkboxes))
        
        # Contextual helper text with validation feedback
        helper_text = self._create_helper_text()
        
        # Validation status
        validation_status = self._create_validation_status()
        
        selection_content = ft.Column([
            ft.Text("Select Locations to Compare:", size=16, weight=ft.FontWeight.BOLD),
            helper_text,
            ft.Text(f"Currently selected: {len(self.selected_locations)} locations", 
                   size=12, color=ft.Colors.GREY_600),
            ft.Divider(height=10),
            *checkbox_rows,
            validation_status,
            ft.Divider(height=20),
            ft.Row([
                self.create_action_button(
                    "Select All",
                    ft.Icons.SELECT_ALL,
                    self._on_select_all,
                    ft.Colors.BLUE_600
                ),
                self.create_action_button(
                    "Clear All",
                    ft.Icons.CLEAR_ALL,
                    self._on_clear_all,
                    ft.Colors.GREY_600
                ),
                self._create_comparison_button()
            ], alignment=ft.MainAxisAlignment.CENTER)
        ])
        
        return self.create_card(
            "Location Selection",
            selection_content,
            icon=ft.Icons.LOCATION_ON
        )
    
    def _create_comparison_results(self) -> ft.Column:
        """Create comparison results display."""
        if not self.comparison_results:
            return ft.Column()
        
        analysis = self.comparison_results.get('analysis', {})
        locations = self.comparison_results.get('locations', {})
        
        # Summary table
        summary_table = self._create_summary_table()
        
        # Rankings
        rankings = self._create_rankings()
        
        # Recommendations
        recommendations = self._create_recommendations()
        
        # Charts
        charts = self._create_comparison_charts()
        
        return ft.Column([
            summary_table,
            rankings,
            recommendations,
            charts
        ])
    
    def _create_summary_table(self) -> ft.Card:
        """Create comparison summary table."""
        analysis = self.comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return ft.Card()
        
        # Create table headers
        headers = [
            ft.DataColumn(ft.Text("Location")),
            ft.DataColumn(ft.Text("IRR Project")),
            ft.DataColumn(ft.Text("IRR Equity")),
            ft.DataColumn(ft.Text("NPV (M€)")),
            ft.DataColumn(ft.Text("LCOE (€/kWh)")),
            ft.DataColumn(ft.Text("Min DSCR")),
            ft.DataColumn(ft.Text("Cap. Factor"))
        ]
        
        # Create table rows
        rows = []
        for location_data in comparison_matrix:
            row = ft.DataRow(cells=[
                ft.DataCell(ft.Text(location_data['Location'])),
                ft.DataCell(ft.Text(f"{location_data['IRR_Project']:.1%}")),
                ft.DataCell(ft.Text(f"{location_data['IRR_Equity']:.1%}")),
                ft.DataCell(ft.Text(f"{location_data['NPV_Project_MEUR']:.1f}")),
                ft.DataCell(ft.Text(f"{location_data['LCOE_EUR_kWh']:.3f}")),
                ft.DataCell(ft.Text(f"{location_data['Min_DSCR']:.2f}")),
                ft.DataCell(ft.Text(f"{location_data['Capacity_Factor']:.1%}"))
            ])
            rows.append(row)
        
        table = ft.DataTable(
            columns=headers,
            rows=rows,
            border=ft.border.all(1, ft.Colors.GREY_400),
            border_radius=8,
            vertical_lines=ft.border.BorderSide(1, ft.Colors.GREY_300),
            horizontal_lines=ft.border.BorderSide(1, ft.Colors.GREY_300)
        )
        
        return self.create_card(
            "Location Comparison Summary",
            ft.Container(content=table, padding=10),
            icon=ft.Icons.TABLE_CHART
        )
    
    def _create_rankings(self) -> ft.Card:
        """Create rankings display."""
        analysis = self.comparison_results.get('analysis', {})
        rankings = analysis.get('rankings', {})
        
        ranking_content = ft.Column([
            ft.Row([
                self._create_ranking_column("Best Project IRR", rankings.get('best_irr_project', [])),
                self._create_ranking_column("Best Equity IRR", rankings.get('best_irr_equity', [])),
                self._create_ranking_column("Lowest LCOE", rankings.get('lowest_lcoe', []))
            ])
        ])
        
        return self.create_card(
            "Location Rankings",
            ranking_content,
            icon=ft.Icons.LEADERBOARD
        )
    
    def _create_ranking_column(self, title: str, ranking_data: List[Dict]) -> ft.Container:
        """Create a ranking column."""
        ranking_items = []
        
        for item in ranking_data[:5]:  # Top 5
            rank_color = ft.Colors.AMBER if item['rank'] == 1 else ft.Colors.BLUE_GREY_400 if item['rank'] == 2 else ft.Colors.BROWN_700 if item['rank'] == 3 else ft.Colors.GREY
            
            ranking_items.append(
                ft.Row([
                    ft.Container(
                        content=ft.Text(str(item['rank']), color=ft.Colors.WHITE, weight=ft.FontWeight.BOLD),
                        width=30,
                        height=30,
                        bgcolor=rank_color,
                        border_radius=15,
                        alignment=ft.alignment.center
                    ),
                    ft.Text(item['location'], expand=True),
                    ft.Text(f"{item['value']:.3f}" if isinstance(item['value'], float) else str(item['value']))
                ])
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(title, size=14, weight=ft.FontWeight.BOLD),
                ft.Divider(height=5),
                *ranking_items
            ]),
            padding=10,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8,
            expand=True
        )
    
    def _create_recommendations(self) -> ft.Card:
        """Create recommendations display."""
        analysis = self.comparison_results.get('analysis', {})
        recommendations = analysis.get('recommendations', {})
        
        if not recommendations:
            return ft.Card()
        
        rec_content = ft.Column([
            ft.Text("Recommendations", size=18, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10)
        ])
        
        # Best overall location
        if 'best_overall' in recommendations:
            best_overall = recommendations['best_overall']
            rec_content.controls.append(
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            ft.Icon(ft.Icons.STAR, color=ft.Colors.AMBER),
                            ft.Text("Best Overall Location", size=16, weight=ft.FontWeight.BOLD)
                        ]),
                        ft.Text(f"Location: {best_overall['location']}", size=14),
                        ft.Text(f"Score: {best_overall['score']:.3f}", size=12, color=ft.Colors.GREY_600),
                        ft.Text("Strengths:", size=12, weight=ft.FontWeight.BOLD),
                        *[ft.Text(f"• {reason}", size=12) for reason in best_overall.get('reasons', [])]
                    ]),
                    padding=15,
                    bgcolor=ft.Colors.AMBER_50,
                    border_radius=8,
                    border=ft.border.all(1, ft.Colors.AMBER)
                )
            )
        
        # Specific recommendations
        specific_recs = [
            ("Best for Returns", recommendations.get('best_for_returns')),
            ("Best for LCOE", recommendations.get('best_for_lcoe')),
            ("Best for Risk Management", recommendations.get('best_for_risk'))
        ]
        
        for title, location in specific_recs:
            if location:
                rec_content.controls.append(
                    self.create_info_row(title, location, ft.Colors.BLUE_700)
                )
        
        return self.create_card(
            "Analysis Recommendations",
            rec_content,
            icon=ft.Icons.RECOMMEND,
            bgcolor=ft.Colors.BLUE_50
        )
    
    def _create_comparison_charts(self) -> ft.Card:
        """Create comparison charts with loading states."""
        if self.is_loading:
            # Show loading state for charts
            chart_content = ft.Container(
                content=ft.Column([
                    EnhancedLoadingSpinner(
                        variant=LoadingVariant.SPINNER,
                        size=ComponentSize.MD,
                        message="Generating comparison charts..."
                    ).build(),
                    ft.Text(
                        "Please wait while we analyze the location data and generate visualizations.",
                        size=12,
                        color=ft.Colors.GREY_600,
                        text_align=ft.TextAlign.CENTER
                    )
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_50,
                border_radius=8
            )
        elif not self.comparison_results:
            # Show empty state
            chart_content = ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.BAR_CHART, size=48, color=ft.Colors.GREY_400),
                    ft.Text("No chart data available", 
                           size=14, color=ft.Colors.GREY_600,
                           text_align=ft.TextAlign.CENTER),
                    ft.Text("Run a comparison to see location analysis charts", 
                           size=12, color=ft.Colors.GREY_500,
                           text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.GREY_100,
                border_radius=8
            )
        else:
            # Show actual charts
            chart_content = self._create_radar_chart()
        
        return self.create_card(
            "Comparison Charts",
            chart_content,
            icon=ft.Icons.BAR_CHART
        )
    
    def _create_radar_chart(self) -> ft.Container:
        """Create radar chart for location comparison."""
        if not self.comparison_results:
            return ft.Container()
        
        # Use the actual ComparisonCharts component
        try:
            radar_chart = self.comparison_charts.create_radar_chart(
                self.comparison_results,
                self.selected_locations
            )
            return ft.Container(
                content=radar_chart,
                height=300,
                padding=10
            )
        except Exception as e:
            # Fallback to placeholder if chart creation fails
            return ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.ERROR, size=32, color=ft.Colors.RED_400),
                    ft.Text("Error generating charts", 
                           size=14, color=ft.Colors.RED_600,
                           text_align=ft.TextAlign.CENTER),
                    ft.Text(f"Details: {str(e)}", 
                           size=10, color=ft.Colors.GREY_500,
                           text_align=ft.TextAlign.CENTER)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=8),
                height=300,
                alignment=ft.alignment.center,
                bgcolor=ft.Colors.RED_50,
                border_radius=8
            )
    
    def _on_location_selected(self, location: str, selected: bool):
        """Handle location selection change."""
        if selected and location not in self.selected_locations:
            self.selected_locations.append(location)
        elif not selected and location in self.selected_locations:
            self.selected_locations.remove(location)
        
        self.refresh()
    
    def _on_select_all(self, e):
        """Select all locations."""
        self.selected_locations = self.available_locations.copy()
        self.refresh()
    
    def _on_clear_all(self, e):
        """Clear all location selections."""
        self.selected_locations = []
        self.refresh()
    
    def _create_helper_text(self) -> ft.Container:
        """Create contextual helper text under the selector."""
        helper_text = (
            "Choose locations to benchmark your project against. "
            "Select at least 2 locations to run a comprehensive comparison analysis "
            "covering financial performance, risk assessment, and operational factors."
        )
        
        return ft.Container(
            content=ft.Text(
                helper_text,
                size=12,
                color=ft.Colors.BLUE_700,
                text_align=ft.TextAlign.LEFT
            ),
            padding=ft.padding.symmetric(vertical=8, horizontal=12),
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200),
            margin=ft.margin.only(bottom=10)
        )
    
    def _create_validation_status(self) -> ft.Container:
        """Create validation status display."""
        selected_count = len(self.selected_locations)
        
        if selected_count == 0:
            icon = ft.Icons.WARNING
            color = ft.Colors.ORANGE_600
            message = "No locations selected. Please select at least 2 locations."
        elif selected_count == 1:
            icon = ft.Icons.ERROR
            color = ft.Colors.RED_600
            message = "Only 1 location selected. Please select at least 1 more location for comparison."
        else:
            icon = ft.Icons.CHECK_CIRCLE
            color = ft.Colors.GREEN_600
            message = f"✓ {selected_count} locations selected. Ready to run comparison."
        
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=16),
                ft.Text(
                    message,
                    size=12,
                    color=color,
                    weight=ft.FontWeight.BOLD
                )
            ], spacing=8),
            padding=ft.padding.symmetric(vertical=8, horizontal=12),
            bgcolor=ft.Colors.with_opacity(0.1, color),
            border_radius=8,
            border=ft.border.all(1, ft.Colors.with_opacity(0.3, color)),
            margin=ft.margin.only(top=10)
        )
    
    def _create_comparison_button(self) -> ft.Control:
        """Create the comparison button with loading state."""
        selected_count = len(self.selected_locations)
        
        if self.is_loading:
            # Show loading state
            return ft.Container(
                content=ft.Row([
                    EnhancedLoadingSpinner(
                        variant=LoadingVariant.SPINNER,
                        size=ComponentSize.SM
                    ).build(),
                    ft.Text("Running comparison...", size=12)
                ], spacing=8),
                padding=ft.padding.symmetric(vertical=8, horizontal=16),
                bgcolor=ft.Colors.BLUE_50,
                border_radius=8,
                border=ft.border.all(1, ft.Colors.BLUE_200)
            )
        else:
            # Show regular button
            is_disabled = selected_count < 2
            button_color = ft.Colors.GREY_400 if is_disabled else ft.Colors.GREEN_600
            
            return self.create_action_button(
                "Run Comparison",
                ft.Icons.COMPARE_ARROWS,
                self._on_run_comparison,
                button_color,
                disabled=is_disabled
            )
    
    def _on_run_comparison(self, e=None):
        """Run location comparison."""
        if len(self.selected_locations) < 2:
            self.show_error("Please select at least 2 locations for comparison")
            return
        
        # Set loading state
        self.is_loading = True
        self.refresh()
        
        self.request_action("run_location_comparison", {
            "locations": self.selected_locations
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with comparison results."""
        if "comparison_results" in data:
            self.comparison_results = data["comparison_results"]
            self.refresh()
        
        if "selected_locations" in data:
            self.selected_locations = data["selected_locations"]
            self.refresh()
    
    def set_comparison_results(self, results: Dict[str, Any]):
        """Set comparison results and reset loading state."""
        self.comparison_results = results
        self.is_loading = False  # Reset loading state when results are received
        self.refresh()
    
    def start_comparison_loading(self):
        """Start loading state for comparison."""
        self.is_loading = True
        self.refresh()
    
    def stop_comparison_loading(self):
        """Stop loading state for comparison."""
        self.is_loading = False
        self.refresh()
    
    def handle_comparison_error(self, error_message: str):
        """Handle comparison errors."""
        self.is_loading = False
        self.show_error(f"Comparison failed: {error_message}")
        self.refresh()
    
    def handle_websocket_update(self, update_data: Dict[str, Any]):
        """Handle real-time updates via websocket/event-stream."""
        if "status" in update_data:
            status = update_data["status"]
            
            if status == "started":
                self.start_comparison_loading()
            elif status == "completed":
                if "results" in update_data:
                    self.set_comparison_results(update_data["results"])
                else:
                    self.stop_comparison_loading()
            elif status == "failed":
                error_msg = update_data.get("error", "Unknown error occurred")
                self.handle_comparison_error(error_msg)
            elif status == "progress":
                # Handle progress updates if needed
                progress = update_data.get("progress", 0)
                message = update_data.get("message", "Processing...")
                # Could show progress in the loading spinner or status bar
                pass
    
    def get_selected_locations(self) -> List[str]:
        """Get currently selected locations."""
        return self.selected_locations.copy()
    
    def set_selected_locations(self, locations: List[str]):
        """Set selected locations."""
        self.selected_locations = [loc for loc in locations if loc in self.available_locations]
        self.refresh()
    
    def can_navigate_next(self) -> bool:
        """Check if navigation to next step is allowed."""
        # Require at least one comparison location and some results
        return len(self.selected_locations) >= 1 and self.comparison_results is not None
    
    def get_navigation_requirements(self) -> Dict[str, Any]:
        """Get navigation requirements for this view."""
        requirements = {
            "can_proceed": self.can_navigate_next(),
            "requirements": [],
            "recommendations": []
        }
        
        if len(self.selected_locations) == 0:
            requirements["requirements"].append("Select at least one location for comparison")
        
        if self.comparison_results is None:
            requirements["requirements"].append("Run location comparison analysis")
        
        if len(self.selected_locations) == 1:
            requirements["recommendations"].append("Consider selecting additional locations for better comparison insights")
        
        return requirements
