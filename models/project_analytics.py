"""
Project Analytics Model
=======================

Model for storing project analytics data including location comparisons.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
import json


@dataclass
class ProjectAnalytics:
    """Project analytics data container."""
    
    project_id: str
    location_comparisons: Optional[Dict[str, Any]] = None
    comparison_metadata: Optional[Dict[str, Any]] = None
    last_comparison_date: Optional[datetime] = None
    comparison_status: str = "pending"  # pending, running, completed, failed
    
    # Additional analytics fields
    kpi_summary: Optional[Dict[str, Any]] = None
    risk_assessment: Optional[Dict[str, Any]] = None
    recommendation_summary: Optional[Dict[str, Any]] = None
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    version: int = 1
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.comparison_metadata is None:
            self.comparison_metadata = {}
        if self.kpi_summary is None:
            self.kpi_summary = {}
        if self.risk_assessment is None:
            self.risk_assessment = {}
        if self.recommendation_summary is None:
            self.recommendation_summary = {}
    
    def update_location_comparisons(self, comparison_data: Dict[str, Any]):
        """Update location comparison data."""
        self.location_comparisons = comparison_data
        self.last_comparison_date = datetime.now()
        self.updated_at = datetime.now()
        self.comparison_status = "completed"
        
        # Extract summary information
        self._extract_comparison_summary()
    
    def _extract_comparison_summary(self):
        """Extract summary information from comparison data."""
        if not self.location_comparisons:
            return
        
        try:
            # Extract KPI summary
            locations = self.location_comparisons.get('locations', {})
            if locations:
                self.kpi_summary = self._calculate_kpi_summary(locations)
            
            # Extract risk assessment
            analysis = self.location_comparisons.get('analysis', {})
            if analysis:
                self.risk_assessment = analysis.get('risk_assessment', {})
                self.recommendation_summary = analysis.get('recommendations', {})
        
        except Exception as e:
            # Log error but don't fail the update
            print(f"Error extracting comparison summary: {e}")
    
    def _calculate_kpi_summary(self, locations: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate KPI summary from location data."""
        summary = {
            'total_locations': len(locations),
            'successful_analyses': 0,
            'failed_analyses': 0,
            'avg_irr_project': 0.0,
            'avg_irr_equity': 0.0,
            'avg_lcoe': 0.0,
            'best_location': None,
            'worst_location': None
        }
        
        valid_locations = []
        irr_values = []
        lcoe_values = []
        
        for location_name, location_data in locations.items():
            if 'error' in location_data:
                summary['failed_analyses'] += 1
                continue
                
            summary['successful_analyses'] += 1
            valid_locations.append(location_name)
            
            # Extract KPIs
            kpis = location_data.get('kpis', {})
            if kpis:
                irr_project = kpis.get('IRR_project', 0)
                irr_equity = kpis.get('IRR_equity', 0)
                lcoe = kpis.get('LCOE_eur_kwh', 0)
                
                irr_values.append(irr_project)
                lcoe_values.append(lcoe)
        
        # Calculate averages
        if irr_values:
            summary['avg_irr_project'] = sum(irr_values) / len(irr_values)
            summary['avg_irr_equity'] = sum(irr_values) / len(irr_values)  # Using same for simplicity
            
            # Find best location (highest IRR)
            best_idx = irr_values.index(max(irr_values))
            summary['best_location'] = valid_locations[best_idx]
            
            # Find worst location (lowest IRR)
            worst_idx = irr_values.index(min(irr_values))
            summary['worst_location'] = valid_locations[worst_idx]
        
        if lcoe_values:
            summary['avg_lcoe'] = sum(lcoe_values) / len(lcoe_values)
        
        return summary
    
    def set_comparison_status(self, status: str, metadata: Optional[Dict[str, Any]] = None):
        """Set comparison status with optional metadata."""
        self.comparison_status = status
        self.updated_at = datetime.now()
        
        if metadata:
            self.comparison_metadata.update(metadata)
    
    def get_comparison_progress(self) -> Dict[str, Any]:
        """Get comparison progress information."""
        return {
            'status': self.comparison_status,
            'last_updated': self.updated_at.isoformat() if self.updated_at else None,
            'last_comparison': self.last_comparison_date.isoformat() if self.last_comparison_date else None,
            'metadata': self.comparison_metadata,
            'has_results': self.location_comparisons is not None
        }
    
    def get_preliminary_data(self) -> Dict[str, Any]:
        """Get preliminary data for immediate UI feedback."""
        return {
            'project_id': self.project_id,
            'status': self.comparison_status,
            'kpi_summary': self.kpi_summary,
            'last_updated': self.updated_at.isoformat() if self.updated_at else None,
            'has_comparisons': self.location_comparisons is not None,
            'total_locations': self.kpi_summary.get('total_locations', 0) if self.kpi_summary else 0,
            'successful_analyses': self.kpi_summary.get('successful_analyses', 0) if self.kpi_summary else 0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'project_id': self.project_id,
            'location_comparisons': self.location_comparisons,
            'comparison_metadata': self.comparison_metadata,
            'last_comparison_date': self.last_comparison_date.isoformat() if self.last_comparison_date else None,
            'comparison_status': self.comparison_status,
            'kpi_summary': self.kpi_summary,
            'risk_assessment': self.risk_assessment,
            'recommendation_summary': self.recommendation_summary,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'version': self.version
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectAnalytics':
        """Create instance from dictionary."""
        # Parse datetime fields
        for date_field in ['last_comparison_date', 'created_at', 'updated_at']:
            if data.get(date_field) and isinstance(data[date_field], str):
                data[date_field] = datetime.fromisoformat(data[date_field])
        
        return cls(**data)
    
    def is_comparison_stale(self, max_age_hours: int = 24) -> bool:
        """Check if comparison data is stale and needs refresh."""
        if not self.last_comparison_date:
            return True
        
        age = datetime.now() - self.last_comparison_date
        return age.total_seconds() > (max_age_hours * 3600)
    
    def get_comparison_age_minutes(self) -> Optional[int]:
        """Get age of comparison data in minutes."""
        if not self.last_comparison_date:
            return None
        
        age = datetime.now() - self.last_comparison_date
        return int(age.total_seconds() / 60)
