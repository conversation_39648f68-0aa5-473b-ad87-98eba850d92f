/// <reference types="cypress" />

describe('Create Project and Location Comparison', () => {
  before(() => {
    // Start the app before running tests
    cy.exec('python src/new_app/main.py', { timeout: Cypress.env('test_timeout') })
  })

  it('Create a project in Ouarzazate and select Marrakech & Agadir', () => {
    // Assume the app is running on localhost and accessible
    cy.visit('/')

    // Login with the admin password (mocking since actual implementation might differ)
    cy.get('[data-cy=password]').type(Cypress.env('admin_password'))
    cy.get('[data-cy=login-button]').click()

    // Navigate to the Create Project Wizard
    cy.get('[data-cy=setup-tab]').click()

    // Fill project information
    cy.get('[data-cy=project-name-input]').type('Test Project in Ouarzazate')
    cy.get('[data-cy=project-location-input]').select('Ouarzazate')

    // Complete the setup
    cy.get('[data-cy=next-button]').click()

    // Select additional locations for comparison
    cy.get('[data-cy=locations-tab]').click()
    cy.get('[data-cy=location-Marrakech]').click({ force: true })
    cy.get('[data-cy=location-Agadir]').click({ force: true })

    // Run the comparison
    cy.get('[data-cy=run-comparison-button]').click()

    // Assert that the radar chart shows 3 series
    cy.get('[data-cy=radar-chart]').should('have.length', 3)
  })
})

