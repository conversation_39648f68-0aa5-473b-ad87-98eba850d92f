/// <reference types="cypress" />

describe('Location Selector Accessibility Testing', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.login()
    cy.waitForApp()
  })

  it('should have proper ARIA labels and keyboard navigation', () => {
    // Navigate to location comparison tab
    cy.get('[data-cy=locations-tab]').click()
    
    // Test accessibility of location selector container
    cy.checkAccessibility('[data-cy=location-selector]')
    
    // Verify fieldset has proper legend
    cy.get('[data-cy=location-selector]').within(() => {
      cy.get('fieldset').should('have.attr', 'aria-describedby')
      cy.get('legend').should('be.visible').and('contain', 'Select Locations')
    })
    
    // Test each location checkbox for accessibility
    const expectedLocations = ['Ouarzazate', 'Marrakech', 'Agadir', 'Dakhla', 'Tarfaya']
    
    expectedLocations.forEach(location => {
      cy.get(`[data-cy=location-${location}]`).should('have.attr', 'aria-label')
      cy.get(`[data-cy=location-${location}]`).should('have.attr', 'role', 'checkbox')
    })
    
    // Test keyboard navigation
    cy.testLocationSelectorKeyboard()
    
    // Test screen reader content
    cy.testScreenReaderContent('[data-cy=location-selector]')
    
    // Test form validation accessibility
    cy.validateFormAccessibility('[data-cy=location-selector]')
  })

  it('should create project in Ouarzazate and compare with Marrakech & Agadir', () => {
    // Create project in Ouarzazate
    cy.createProject({
      name: 'Solar Project Ouarzazate',
      location: 'Ouarzazate',
      companyName: 'Solar Energy Solutions',
      clientName: 'Morocco Solar Initiative'
    })
    
    // Navigate to location comparison
    cy.selectLocations(['Marrakech', 'Agadir'])
    
    // Verify validation status shows 3 locations selected (including Ouarzazate)
    cy.get('[data-cy=validation-status]').should('contain', '3 locations selected')
    
    // Run comparison
    cy.runLocationComparison()
    
    // Validate radar chart shows 3 series
    cy.validateRadarChart(3)
    
    // Verify each location is represented in the chart
    cy.get('[data-cy=radar-chart-series]').should('contain.attr', 'data-location', 'Ouarzazate')
    cy.get('[data-cy=radar-chart-series]').should('contain.attr', 'data-location', 'Marrakech')
    cy.get('[data-cy=radar-chart-series]').should('contain.attr', 'data-location', 'Agadir')
    
    // Take screenshot for documentation
    cy.takeScreenshot('location-comparison-radar-chart')
  })

  it('should support high contrast mode', () => {
    // Enable high contrast mode (if supported)
    cy.window().then((win) => {
      win.document.body.classList.add('high-contrast')
    })
    
    cy.get('[data-cy=locations-tab]').click()
    
    // Verify high contrast styling is applied
    cy.get('[data-cy=location-selector]').should('have.class', 'high-contrast')
    
    // Test checkboxes are still accessible in high contrast mode
    cy.get('[data-cy=location-Ouarzazate]').should('be.visible')
    cy.get('[data-cy=location-Marrakech]').should('be.visible')
    cy.get('[data-cy=location-Agadir]').should('be.visible')
  })

  it('should handle keyboard-only navigation', () => {
    // Navigate to locations tab using keyboard
    cy.get('body').tab()
    cy.focused().should('have.attr', 'data-cy', 'locations-tab')
    cy.focused().type('{enter}')
    
    // Navigate through location checkboxes using keyboard
    cy.get('[data-cy=location-selector] input[type="checkbox"]').first().focus()
    
    // Test Tab navigation through all checkboxes
    const locations = ['Ouarzazate', 'Marrakech', 'Agadir', 'Dakhla', 'Tarfaya']
    
    locations.forEach((location, index) => {
      cy.focused().should('have.attr', 'data-cy', `location-${location}`)
      
      // Select using Space key
      cy.focused().type(' ')
      cy.focused().should('be.checked')
      
      // Move to next checkbox (except for last item)
      if (index < locations.length - 1) {
        cy.focused().tab()
      }
    })
    
    // Navigate to comparison button and activate
    cy.focused().tab()
    cy.focused().should('have.attr', 'data-cy', 'run-comparison-button')
    cy.focused().type('{enter}')
    
    // Verify comparison starts
    cy.get('[data-cy=comparison-loading]').should('be.visible')
  })

  it('should provide proper error feedback for accessibility', () => {
    cy.get('[data-cy=locations-tab]').click()
    
    // Try to run comparison without selecting locations
    cy.get('[data-cy=run-comparison-button]').click()
    
    // Verify error message is accessible
    cy.get('[data-cy=error-message]').should('be.visible')
    cy.get('[data-cy=error-message]').should('have.attr', 'role', 'alert')
    cy.get('[data-cy=error-message]').should('have.attr', 'aria-live', 'polite')
  })

  it('should provide loading state feedback for screen readers', () => {
    cy.get('[data-cy=locations-tab]').click()
    
    // Select some locations
    cy.selectLocations(['Marrakech', 'Agadir'])
    
    // Start comparison
    cy.get('[data-cy=run-comparison-button]').click()
    
    // Verify loading state has proper ARIA attributes
    cy.get('[data-cy=comparison-loading]').should('be.visible')
    cy.get('[data-cy=comparison-loading]').should('have.attr', 'aria-label', 'Loading comparison results')
    cy.get('[data-cy=comparison-loading]').should('have.attr', 'role', 'status')
  })
})
