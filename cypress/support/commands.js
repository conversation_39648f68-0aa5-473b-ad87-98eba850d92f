// Custom commands for Hiel RnE Modeler testing

// Command to login to the application
Cypress.Commands.add('login', (password = Cypress.env('admin_password')) => {
  cy.get('[data-cy=password-input]').type(password)
  cy.get('[data-cy=login-button]').click()
  cy.url().should('not.include', '/login')
})

// Command to create a new project
Cypress.Commands.add('createProject', (projectData) => {
  const defaultProject = {
    name: 'Test Project',
    location: 'Ouarzazate',
    companyName: 'Test Company',
    clientName: 'Test Client',
    capacity: 100,
    ...projectData
  }
  
  cy.get('[data-cy=setup-tab]').click()
  cy.get('[data-cy=project-name-input]').type(defaultProject.name)
  cy.get('[data-cy=company-name-input]').type(defaultProject.companyName)
  cy.get('[data-cy=client-name-input]').type(defaultProject.clientName)
  cy.get('[data-cy=project-location-select]').select(defaultProject.location)
  cy.get('[data-cy=project-capacity-input]').type(defaultProject.capacity.toString())
})

// Command to select locations for comparison
Cypress.Commands.add('selectLocations', (locations) => {
  cy.get('[data-cy=locations-tab]').click()
  
  locations.forEach(location => {
    cy.get(`[data-cy=location-${location}]`).check({ force: true })
  })
})

// Command to run location comparison
Cypress.Commands.add('runLocationComparison', () => {
  cy.get('[data-cy=run-comparison-button]').click()
  cy.get('[data-cy=comparison-loading]').should('be.visible')
  cy.get('[data-cy=comparison-loading]').should('not.exist', { timeout: 30000 })
})

// Command to validate radar chart
Cypress.Commands.add('validateRadarChart', (expectedSeriesCount) => {
  cy.get('[data-cy=radar-chart]').should('be.visible')
  
  // Check for the expected number of series in the radar chart
  cy.get('[data-cy=radar-chart-series]').should('have.length', expectedSeriesCount)
  
  // Validate that each series has data points
  cy.get('[data-cy=radar-chart-series]').each(($series) => {
    cy.wrap($series).should('have.attr', 'data-location')
    cy.wrap($series).find('[data-cy=data-point]').should('have.length.greaterThan', 0)
  })
})

// Command to check accessibility compliance
Cypress.Commands.add('checkAccessibility', (selector) => {
  cy.get(selector).should('be.visible')
  
  // Check for ARIA labels
  cy.get(selector).should('satisfy', ($el) => {
    return $el.attr('aria-label') || 
           $el.attr('aria-describedby') || 
           $el.attr('title') ||
           $el.attr('role')
  })
})

// Command to test keyboard navigation in location selector
Cypress.Commands.add('testLocationSelectorKeyboard', () => {
  // Focus on the first location checkbox
  cy.get('[data-cy=location-selector] input[type="checkbox"]').first().focus()
  
  // Navigate through checkboxes using arrow keys
  cy.focused().type('{downarrow}')
  cy.focused().should('have.attr', 'data-cy').and('include', 'location-')
  
  // Test space key to toggle selection
  cy.focused().type(' ')
  cy.focused().should('be.checked')
  
  // Test Enter key to activate
  cy.focused().type('{enter}')
})

// Command to validate form accessibility
Cypress.Commands.add('validateFormAccessibility', (formSelector) => {
  cy.get(formSelector).within(() => {
    // Check that all form inputs have labels
    cy.get('input, select, textarea').each(($input) => {
      const id = $input.attr('id')
      const ariaLabel = $input.attr('aria-label')
      const ariaLabelledBy = $input.attr('aria-labelledby')
      
      if (id) {
        cy.get(`label[for="${id}"]`).should('exist')
      } else {
        expect(ariaLabel || ariaLabelledBy).to.exist
      }
    })
    
    // Check for proper form structure
    cy.get('fieldset').should('have.attr', 'aria-describedby')
    cy.get('legend').should('be.visible')
  })
})

// Command to wait for application to be ready
Cypress.Commands.add('waitForApp', () => {
  cy.get('[data-cy=app-container]').should('be.visible')
  cy.get('[data-cy=loading-spinner]').should('not.exist')
})

// Command to take screenshot with custom name
Cypress.Commands.add('takeScreenshot', (name) => {
  cy.screenshot(name, { capture: 'viewport' })
})
