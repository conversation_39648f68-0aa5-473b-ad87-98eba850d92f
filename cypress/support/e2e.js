// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Global before hook to set up the test environment
beforeEach(() => {
  // Clear all sessions and cookies before each test
  cy.clearAllCookies()
  cy.clearAllSessionStorage()
  cy.clearAllLocalStorage()
  
  // Set up viewport for consistent testing
  cy.viewport(1280, 720)
})

// Global exception handler to prevent test failure on uncaught exceptions
Cypress.on('uncaught:exception', (err, runnable) => {
  // Prevent Cypress from failing the test on uncaught exceptions
  // This is useful for applications that might have expected errors
  return false
})

// Commands for accessibility testing
Cypress.Commands.add('checkA11y', (selector, options) => {
  cy.get(selector).should('be.visible')
  // Add actual accessibility checks here when axe-core is available
  // For now, just check basic accessibility attributes
  cy.get(selector).should('have.attr', 'aria-label')
    .or('have.attr', 'aria-describedby')
    .or('have.attr', 'title')
})

// Command to test keyboard navigation
Cypress.Commands.add('testKeyboardNavigation', (startElement, expectedElements) => {
  cy.get(startElement).focus()
  
  expectedElements.forEach((element, index) => {
    cy.focused().should('have.attr', 'data-cy', element)
    if (index < expectedElements.length - 1) {
      cy.focused().tab()
    }
  })
})

// Command to test screen reader content
Cypress.Commands.add('testScreenReaderContent', (selector) => {
  cy.get(selector).then(($el) => {
    // Check for screen reader friendly attributes
    const hasAriaLabel = $el.attr('aria-label')
    const hasAriaDescribedBy = $el.attr('aria-describedby')
    const hasTitle = $el.attr('title')
    const hasRole = $el.attr('role')
    
    expect(hasAriaLabel || hasAriaDescribedBy || hasTitle || hasRole).to.exist
  })
})
