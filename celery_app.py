"""
Celery Application Configuration
===============================

Celery configuration for background task processing in the Hiel RnE Model.
"""

import os
import logging
from celery import Celery
from celery.signals import setup_logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Celery app configuration
def create_celery_app():
    """Create and configure Celery application."""
    
    # Default broker URL (can be overridden by environment variable)
    broker_url = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    
    # Create Celery app
    app = Celery('hiel_rne_model')
    
    # Configuration
    app.conf.update(
        broker_url=broker_url,
        result_backend=result_backend,
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        task_track_started=True,
        task_time_limit=30 * 60,  # 30 minutes
        task_soft_time_limit=25 * 60,  # 25 minutes
        worker_prefetch_multiplier=1,
        worker_max_tasks_per_child=1000,
        result_expires=3600,  # 1 hour
        task_routes={
            'tasks.location_comparison.run_location_comparison': {'queue': 'location_comparison'},
            'tasks.analytics.update_project_analytics': {'queue': 'analytics'},
        },
        beat_schedule={
            'cleanup-expired-results': {
                'task': 'tasks.maintenance.cleanup_expired_results',
                'schedule': 3600.0,  # Run every hour
            },
        },
    )
    
    # Auto-discover tasks
    app.autodiscover_tasks(['tasks'])
    
    return app

# Create the Celery app instance
celery_app = create_celery_app()

@setup_logging.connect
def config_loggers(*args, **kwargs):
    """Configure logging for Celery workers."""
    from logging.config import dictConfig
    dictConfig({
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'default': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            },
        },
        'handlers': {
            'default': {
                'level': 'INFO',
                'class': 'logging.StreamHandler',
                'formatter': 'default',
            },
        },
        'loggers': {
            '': {
                'handlers': ['default'],
                'level': 'INFO',
                'propagate': False,
            },
        }
    })

if __name__ == '__main__':
    celery_app.start()
