{"mode": "enhanced", "include_ai_analysis": true, "include_professional_styling": true, "include_security_features": true, "include_charts": true, "include_validation": true, "parallel_processing": true, "export_formats": ["PDF", "HTML", "DOCX"], "ai_config": {"provider": "openai", "api_url": "https://api.openai.com/v1/chat/completions", "api_key": "", "model_id": "gpt-4", "system_prompt": "You are a professional financial analyst specializing in renewable energy projects.\nAnalyze the provided financial data and generate comprehensive insights.\n\nYour analysis should include:\n1. Executive summary of financial performance\n2. Key strengths and weaknesses\n3. Risk assessment with specific recommendations\n4. Market positioning analysis\n5. Investment recommendation with rationale\n6. Strategic next steps\n\nFormat your response as structured content with clear sections.\nBe precise, professional, and focus on actionable insights.\nAvoid speculation and base all conclusions on the provided data.\nUse industry-standard financial terminology and metrics.", "max_tokens": 4000, "temperature": 0.3, "timeout": 30, "rate_limit": 10, "data_retention_days": 0, "enable_caching": true, "privacy_mode": true}, "pdf_config": {"page_size": "A4", "margins": {"top": 2.5, "bottom": 2.5, "left": 2.0, "right": 2.0}, "header_height": 1.5, "footer_height": 1.0, "font_family": "Helvetica", "font_size": 10, "line_spacing": 1.2, "paragraph_spacing": 0.5, "company_name": "Professional Financial Analysis", "primary_color": "#2E86AB", "secondary_color": "#A23B72", "accent_color": "#F18F01", "footer_text": "Confidential Financial Analysis Report"}, "security_config": {"enable_encryption": true, "enable_data_anonymization": true, "enable_audit_logging": true, "data_retention_policy": "no_retention", "access_control_enabled": false, "allowed_domains": [], "blocked_domains": []}, "performance_config": {"enable_parallel_processing": true, "max_workers": 4, "enable_ai_cache": true, "cache_ttl_minutes": 60, "batch_processing": false, "optimize_for_speed": true}, "template_config": {"default_template": "professional_financial_report", "custom_templates_path": "templates/custom", "enable_template_caching": true, "template_validation": true}, "export_config": {"output_directory": "output/enhanced_reports", "filename_format": "{client_name}_{timestamp}_report", "include_metadata": true, "compress_outputs": false, "auto_cleanup_days": 30}, "validation_config": {"enable_data_validation": true, "validation_level": "comprehensive", "fail_on_critical_errors": true, "show_warnings": true, "validation_timeout": 10}, "logging_config": {"log_level": "INFO", "enable_debug_logging": false, "log_ai_interactions": false, "log_file_path": "logs/enhanced_reporting.log", "max_log_size_mb": 10, "backup_count": 5}, "created_at": "2024-01-15T10:30:00Z", "version": "2.0"}