# Enhanced Financial Model Application Requirements
# =================================================

# Core framework
flet>=0.21.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
numpy_financial>=1.0.0

# Visualization
matplotlib>=3.7.0
plotly>=5.15.0

# Document generation
python-docx>=0.8.11
python-pptx>=0.6.21     # PowerPoint presentation generation
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# Data validation and utilities
pydantic>=2.0.0
python-dateutil>=2.8.0

# Optional: Enhanced chart capabilities
seaborn>=0.12.0
scipy>=1.10.0

# Optional: PDF generation
reportlab>=4.0.0

# === ADVANCED FEATURES DEPENDENCIES ===

# Machine Learning and Predictions
scikit-learn>=1.3.0     # ML models for financial predictions
scipy>=1.10.0           # Scientific computing for optimization

# High-Performance Caching and Task Queue
redis>=4.6.0            # Redis for distributed caching and task queue
celery>=5.3.0           # Distributed task queue for async processing
flower>=2.0.1           # Web-based monitoring for Celery tasks

# Data Persistence and Compression
# (using built-in sqlite3, gzip, pickle)
alembic>=1.12.0           # Database migrations

# Optional ML/AI packages for advanced features
# Uncomment and install separately if advanced ML is needed:
# tensorflow>=2.13.0    # Deep learning for complex predictions
# xgboost>=1.7.0        # Gradient boosting for ensemble models
# lightgbm>=4.0.0       # Fast gradient boosting
# catboost>=1.2.0       # Categorical boosting

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
