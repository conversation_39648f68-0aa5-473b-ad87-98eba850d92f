"""Add location_comparisons column to projects table

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import json

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add location_comparisons column to projects table."""
    # Create a connection
    conn = op.get_bind()
    
    # Check if comparison_locations column exists, if not add it
    # This handles the case where the column might not exist in older databases
    try:
        conn.execute(sa.text("ALTER TABLE projects ADD COLUMN comparison_locations TEXT DEFAULT '[]'"))
        print("Added comparison_locations column")
    except Exception as e:
        if "duplicate column name" in str(e):
            print("comparison_locations column already exists")
        else:
            raise
    
    # Add the new location_comparisons column (JSONB equivalent for SQLite is TEXT)
    try:
        conn.execute(sa.text("ALTER TABLE projects ADD COLUMN location_comparisons TEXT DEFAULT '{}'"))
        print("Added location_comparisons column")
    except Exception as e:
        if "duplicate column name" in str(e):
            print("location_comparisons column already exists")
        else:
            raise
    
    # Backfill existing projects with empty values for compatibility
    try:
        # Update comparison_locations to empty array if NULL
        conn.execute(sa.text("""
            UPDATE projects 
            SET comparison_locations = '[]' 
            WHERE comparison_locations IS NULL
        """))
        
        # Update location_comparisons to empty object if NULL
        conn.execute(sa.text("""
            UPDATE projects 
            SET location_comparisons = '{}' 
            WHERE location_comparisons IS NULL
        """))
        
        print("Backfilled existing projects with empty values")
    except Exception as e:
        print(f"Error during backfill: {e}")
        raise


def downgrade() -> None:
    """Remove location_comparisons column from projects table."""
    # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    # This is a simplified approach - in production, you'd want to preserve all data
    
    conn = op.get_bind()
    
    # Create backup table
    conn.execute(sa.text("""
        CREATE TABLE projects_backup AS 
        SELECT id, name, client_profile, project_assumptions, financial_results,
               comparison_locations, created_at, modified_at, version, tags, 
               description, is_deleted
        FROM projects
    """))
    
    # Drop original table
    conn.execute(sa.text("DROP TABLE projects"))
    
    # Recreate table without location_comparisons
    conn.execute(sa.text("""
        CREATE TABLE projects (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            client_profile TEXT NOT NULL,
            project_assumptions TEXT NOT NULL,
            financial_results TEXT,
            comparison_locations TEXT DEFAULT '[]',
            created_at TIMESTAMP,
            modified_at TIMESTAMP,
            version INTEGER DEFAULT 1,
            tags TEXT DEFAULT '[]',
            description TEXT DEFAULT '',
            is_deleted BOOLEAN DEFAULT FALSE
        )
    """))
    
    # Restore data
    conn.execute(sa.text("""
        INSERT INTO projects 
        SELECT id, name, client_profile, project_assumptions, financial_results,
               comparison_locations, created_at, modified_at, version, tags, 
               description, is_deleted
        FROM projects_backup
    """))
    
    # Drop backup table
    conn.execute(sa.text("DROP TABLE projects_backup"))
    
    print("Removed location_comparisons column")
