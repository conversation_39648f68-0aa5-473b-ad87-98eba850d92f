"""
Enhanced Reporting System Demo
==============================

Demonstration script showing how to use the enhanced reporting system
with AI analysis, professional styling, and advanced features.
"""

import asyncio
import logging
from pathlib import Path
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import enhanced reporting components
from controllers.enhanced_reporting_controller import EnhancedReportingController
from services.enhanced_reporting_integration import ReportingConfig, ReportingMode
from models.client_profile import ClientProfile
from models.enhanced_project_assumptions import EnhancedProjectAssumptions


async def demo_basic_enhanced_report():
    """Demonstrate basic enhanced report generation."""
    
    print("\n" + "="*60)
    print("ENHANCED REPORTING SYSTEM DEMO")
    print("="*60)
    
    # Initialize controller
    controller = EnhancedReportingController()
    
    # Create sample client profile
    client_profile = ClientProfile(
        company_name="Green Energy Solutions Ltd",
        project_name="Alpine Wind Farm Project",
        project_location="Alpine Region, Switzerland",
        consultant="AI-Enhanced Financial Modeling Team",
        contact_email="<EMAIL>",
        contact_phone="+41 44 123 4567"
    )
    
    # Create sample project assumptions
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=150.0,
        technology_type="Onshore Wind",
        project_life_years=25,
        capex_meur=180.0,
        opex_meur_per_year=5.4,
        equity_percentage=0.35,
        debt_interest_rate=0.045,
        discount_rate=0.078,
        power_price_eur_per_mwh=65.0,
        capacity_factor=0.28,
        degradation_rate=0.005,
        inflation_rate=0.025,
        tax_rate=0.21
    )
    
    print(f"\n📊 Client: {client_profile.company_name}")
    print(f"🌟 Project: {client_profile.project_name}")
    print(f"📍 Location: {client_profile.project_location}")
    print(f"⚡ Capacity: {assumptions.capacity_mw} MW")
    print(f"🔧 Technology: {assumptions.technology_type}")
    
    # Validate inputs
    validation_result = controller.validate_report_inputs(client_profile, assumptions)
    if not validation_result['valid']:
        print(f"\n❌ Validation errors: {validation_result['errors']}")
        return
    
    if validation_result['warnings']:
        print(f"\n⚠️  Warnings: {validation_result['warnings']}")
    
    # Get service status
    status = controller.get_service_status()
    print(f"\n🔧 Service Status: {'✅ Ready' if status.get('initialized') else '❌ Not Ready'}")
    
    # Create output directory
    output_dir = Path('output/demo_reports')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate enhanced report
    print(f"\n🚀 Generating enhanced report...")
    start_time = datetime.now()
    
    try:
        result = await controller.generate_enhanced_report(
            client_profile=client_profile,
            assumptions=assumptions,
            output_directory=output_dir
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"\n📈 Report Generation Results:")
        print(f"   Success: {'✅' if result.success else '❌'}")
        print(f"   Report ID: {result.report_id}")
        print(f"   Processing Time: {processing_time:.2f} seconds")
        print(f"   Generated Files: {len(result.generated_files)}")
        
        for format_type, file_path in result.generated_files.items():
            print(f"     - {format_type}: {file_path}")
        
        if result.validation_summary:
            print(f"   Validation Score: {result.validation_summary.overall_valid}")
            print(f"   Data Integrity: {result.validation_summary.data_integrity_score:.1%}")
            print(f"   Business Logic: {result.validation_summary.business_logic_score:.1%}")
            print(f"   Completeness: {result.validation_summary.completeness_score:.1%}")
        
        if result.ai_analysis_summary:
            print(f"   AI Analysis: ✅ Included")
            print(f"   AI Confidence: {result.ai_analysis_summary.get('confidence_score', 0):.1f}%")
        
        if result.errors:
            print(f"   Errors: {result.errors}")
        
        if result.warnings:
            print(f"   Warnings: {result.warnings}")
        
    except Exception as e:
        print(f"\n❌ Report generation failed: {str(e)}")


async def demo_ai_powered_report():
    """Demonstrate AI-powered report generation."""
    
    print("\n" + "="*60)
    print("AI-POWERED REPORT DEMO")
    print("="*60)
    
    controller = EnhancedReportingController()
    
    # Create sample data
    client_profile = ClientProfile(
        company_name="Solar Innovations Inc",
        project_name="Desert Solar Park",
        project_location="Nevada, USA",
        consultant="AI-Enhanced Analysis Team"
    )
    
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=200.0,
        technology_type="Solar PV",
        project_life_years=30,
        capex_meur=160.0,
        opex_meur_per_year=3.2,
        equity_percentage=0.40,
        debt_interest_rate=0.055,
        discount_rate=0.085
    )
    
    # AI configuration (for demo purposes - would normally come from settings)
    ai_config = controller.get_default_ai_config()
    
    print(f"\n🤖 AI Configuration:")
    print(f"   Provider: {ai_config['provider']}")
    print(f"   Model: {ai_config['model_id']}")
    print(f"   Max Tokens: {ai_config['max_tokens']}")
    print(f"   Temperature: {ai_config['temperature']}")
    
    # Test AI connection (simulated)
    print(f"\n🔍 Testing AI connection...")
    connection_test = await controller.test_ai_connection(ai_config)
    print(f"   Connection: {'✅ Success' if connection_test['success'] else '❌ Failed'}")
    
    if connection_test['success']:
        # Generate AI-powered report
        output_dir = Path('output/ai_powered_reports')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n🚀 Generating AI-powered report...")
        
        try:
            result = await controller.generate_ai_powered_report(
                client_profile=client_profile,
                assumptions=assumptions,
                ai_config=ai_config,
                output_directory=output_dir
            )
            
            print(f"\n📈 AI-Powered Report Results:")
            print(f"   Success: {'✅' if result.success else '❌'}")
            print(f"   Report ID: {result.report_id}")
            print(f"   Processing Time: {result.processing_time:.2f} seconds")
            print(f"   Generated Files: {len(result.generated_files)}")
            
            for format_type, file_path in result.generated_files.items():
                print(f"     - {format_type}: {file_path}")
            
        except Exception as e:
            print(f"\n❌ AI-powered report generation failed: {str(e)}")


async def demo_professional_report():
    """Demonstrate professional report generation with custom branding."""
    
    print("\n" + "="*60)
    print("PROFESSIONAL REPORT DEMO")
    print("="*60)
    
    controller = EnhancedReportingController()
    
    # Create sample data
    client_profile = ClientProfile(
        company_name="Renewable Energy Consulting",
        project_name="Offshore Wind Development",
        project_location="North Sea, Denmark",
        consultant="Professional Financial Analysis Team"
    )
    
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=300.0,
        technology_type="Offshore Wind",
        project_life_years=25,
        capex_meur=900.0,
        opex_meur_per_year=27.0,
        equity_percentage=0.30,
        debt_interest_rate=0.065,
        discount_rate=0.095
    )
    
    # Custom branding configuration
    branding_config = {
        'company_name': 'Professional Energy Analytics',
        'primary_color': '#1F4788',
        'secondary_color': '#D4AF37',
        'accent_color': '#228B22',
        'footer_text': 'Confidential Professional Financial Analysis',
        'page_size': 'A4',
        'font_family': 'Helvetica',
        'font_size': 11
    }
    
    print(f"\n🎨 Custom Branding:")
    print(f"   Company: {branding_config['company_name']}")
    print(f"   Primary Color: {branding_config['primary_color']}")
    print(f"   Secondary Color: {branding_config['secondary_color']}")
    print(f"   Font: {branding_config['font_family']}, {branding_config['font_size']}pt")
    
    # Generate professional report
    output_dir = Path('output/professional_reports')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n🚀 Generating professional report...")
    
    try:
        result = await controller.generate_professional_report(
            client_profile=client_profile,
            assumptions=assumptions,
            branding_config=branding_config,
            output_directory=output_dir
        )
        
        print(f"\n📈 Professional Report Results:")
        print(f"   Success: {'✅' if result.success else '❌'}")
        print(f"   Report ID: {result.report_id}")
        print(f"   Processing Time: {result.processing_time:.2f} seconds")
        print(f"   Generated Files: {len(result.generated_files)}")
        
        for format_type, file_path in result.generated_files.items():
            print(f"     - {format_type}: {file_path}")
        
    except Exception as e:
        print(f"\n❌ Professional report generation failed: {str(e)}")


async def demo_configuration_management():
    """Demonstrate configuration management features."""
    
    print("\n" + "="*60)
    print("CONFIGURATION MANAGEMENT DEMO")
    print("="*60)
    
    controller = EnhancedReportingController()
    
    # Show available options
    print(f"\n⚙️  Available Export Formats:")
    for format_type in controller.get_available_export_formats():
        print(f"   - {format_type}")
    
    print(f"\n📊 Available Reporting Modes:")
    for mode in controller.get_available_reporting_modes():
        print(f"   - {mode}")
    
    # Create custom configuration
    custom_config = controller.create_reporting_config(
        mode='professional',
        include_ai=True,
        include_professional_styling=True,
        include_security=True,
        export_formats=['PDF', 'HTML', 'DOCX', 'PPTX']
    )
    
    print(f"\n🔧 Custom Configuration:")
    print(f"   Mode: {custom_config.mode.value}")
    print(f"   AI Analysis: {'✅' if custom_config.include_ai_analysis else '❌'}")
    print(f"   Professional Styling: {'✅' if custom_config.include_professional_styling else '❌'}")
    print(f"   Security Features: {'✅' if custom_config.include_security_features else '❌'}")
    print(f"   Export Formats: {custom_config.export_formats}")
    
    # Save configuration
    controller.save_configuration(custom_config)
    print(f"\n💾 Configuration saved successfully")


async def demo_generation_history():
    """Demonstrate generation history tracking."""
    
    print("\n" + "="*60)
    print("GENERATION HISTORY DEMO")
    print("="*60)
    
    controller = EnhancedReportingController()
    
    # Generate a sample report to have history
    result = await controller.generate_sample_report()
    
    # Get generation history
    history = controller.get_generation_history()
    
    print(f"\n📜 Generation History ({len(history)} reports):")
    
    for i, report in enumerate(history[-5:], 1):  # Show last 5 reports
        print(f"\n   Report {i}:")
        print(f"     ID: {report.report_id}")
        print(f"     Success: {'✅' if report.success else '❌'}")
        print(f"     Processing Time: {report.processing_time:.2f}s")
        print(f"     Generated Files: {len(report.generated_files)}")
        print(f"     Client: {report.metadata.get('client_name', 'Unknown')}")
        print(f"     Timestamp: {report.metadata.get('generation_timestamp', 'Unknown')}")


async def demo_service_status():
    """Demonstrate service status monitoring."""
    
    print("\n" + "="*60)
    print("SERVICE STATUS DEMO")
    print("="*60)
    
    controller = EnhancedReportingController()
    
    # Get comprehensive service status
    status = controller.get_service_status()
    
    print(f"\n🔍 Service Status Overview:")
    print(f"   Initialized: {'✅' if status.get('initialized') else '❌'}")
    print(f"   Controller Version: {status.get('controller_version', 'Unknown')}")
    
    # AI Service Status
    ai_status = status.get('ai_service', {})
    print(f"\n🤖 AI Service:")
    print(f"   Status: {ai_status.get('status', 'Unknown')}")
    print(f"   Provider: {ai_status.get('provider', 'Unknown')}")
    print(f"   Model: {ai_status.get('model', 'Unknown')}")
    
    # Template Engine Status
    template_status = status.get('template_engine', {})
    print(f"\n📄 Template Engine:")
    print(f"   Status: {template_status.get('status', 'Unknown')}")
    print(f"   Templates Loaded: {template_status.get('templates_loaded', 0)}")
    
    # PDF Service Status
    pdf_status = status.get('pdf_service', {})
    print(f"\n📑 PDF Service:")
    print(f"   PDF Generation: {'✅' if pdf_status.get('pdf_generation') else '❌'}")
    print(f"   Digital Signatures: {'✅' if pdf_status.get('digital_signatures') else '❌'}")
    print(f"   Password Protection: {'✅' if pdf_status.get('password_protection') else '❌'}")
    print(f"   Watermarks: {'✅' if pdf_status.get('watermarks') else '❌'}")
    
    # Integration Service Status
    integration_status = status.get('integration_service', {})
    print(f"\n🔗 Integration Service:")
    print(f"   Status: {integration_status.get('status', 'Unknown')}")
    print(f"   Version: {integration_status.get('version', 'Unknown')}")
    print(f"   Reports Generated: {integration_status.get('reports_generated', 0)}")


async def main():
    """Main demo function."""
    
    print("🚀 Starting Enhanced Reporting System Demo")
    print("This demo showcases the advanced features of the enhanced reporting system.")
    
    try:
        # Run all demos
        await demo_basic_enhanced_report()
        await demo_ai_powered_report()
        await demo_professional_report()
        await demo_configuration_management()
        await demo_generation_history()
        await demo_service_status()
        
        print("\n" + "="*60)
        print("✅ DEMO COMPLETED SUCCESSFULLY")
        print("="*60)
        print("All enhanced reporting features have been demonstrated.")
        print("Check the output directories for generated reports.")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        print("Please check the logs for more details.")


if __name__ == "__main__":
    asyncio.run(main())