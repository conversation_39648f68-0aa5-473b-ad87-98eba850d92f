"""
Progress Tracking Integration Example
====================================

This example demonstrates how to integrate comprehensive progress tracking
into your application's long-running operations.
"""

import asyncio
import flet as ft
from services.progress_service import ProgressService, OperationType
from services.financial_service import FinancialModelService
from services.ml_prediction_service import MLPredictionService
from services.export_service import ExportService
from models.project_assumptions import EnhancedProjectAssumptions
from models.client_profile import ClientProfile


class ProgressTrackingExample:
    """Example application demonstrating progress tracking."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.progress_service = ProgressService(page)
        self.financial_service = FinancialModelService()
        self.ml_service = MLPredictionService()
        self.export_service = ExportService()
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the example UI."""
        self.page.title = "Progress Tracking Example"
        self.page.scroll = ft.ScrollMode.AUTO
        
        # Operation buttons
        self.financial_btn = ft.ElevatedButton(
            "Run Financial Analysis",
            on_click=self.run_financial_analysis,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.BLUE
            )
        )
        
        self.ml_training_btn = ft.ElevatedButton(
            "Train ML Models",
            on_click=self.train_ml_models,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.GREEN
            )
        )
        
        self.monte_carlo_btn = ft.ElevatedButton(
            "Run Monte Carlo Simulation",
            on_click=self.run_monte_carlo,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.ORANGE
            )
        )
        
        self.export_btn = ft.ElevatedButton(
            "Export Reports",
            on_click=self.export_reports,
            style=ft.ButtonStyle(
                color=ft.Colors.WHITE,
                bgcolor=ft.Colors.PURPLE
            )
        )
        
        # Status display
        self.status_text = ft.Text(
            "Ready to run operations...",
            size=16,
            color=ft.Colors.GREEN
        )
        
        # Operation status list
        self.operation_list = ft.Column(
            controls=[],
            spacing=10,
            scroll=ft.ScrollMode.AUTO
        )
        
        # Layout
        self.page.add(
            ft.Container(
                content=ft.Column([
                    ft.Text("Progress Tracking Example", size=24, weight=ft.FontWeight.BOLD),
                    ft.Container(height=20),
                    
                    # Operation buttons
                    ft.Row([
                        self.financial_btn,
                        self.ml_training_btn,
                        self.monte_carlo_btn,
                        self.export_btn
                    ], alignment=ft.MainAxisAlignment.CENTER),
                    
                    ft.Container(height=20),
                    
                    # Status
                    self.status_text,
                    
                    ft.Container(height=20),
                    
                    # Operations list
                    ft.Text("Active Operations:", size=18, weight=ft.FontWeight.BOLD),
                    self.operation_list
                    
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=20
            )
        )
        
        # Add progress callbacks
        self.progress_service.add_progress_callback(self.on_progress_update)
    
    def on_progress_update(self, operation_id: str, step_id: str, progress: float, message: str):
        """Handle progress updates."""
        self.status_text.value = f"Operation {operation_id}: {step_id} - {progress:.1f}% - {message}"
        self.page.update()
        
        # Update operation list
        self.update_operation_list()
    
    def update_operation_list(self):
        """Update the operation list display."""
        operations = self.progress_service.get_all_operations()
        
        self.operation_list.controls.clear()
        
        for op_id, op_status in operations.items():
            if op_status and op_status['status'] in ['running', 'pending']:
                # Create progress card
                progress_card = ft.Card(
                    content=ft.Container(
                        content=ft.Column([
                            ft.Text(f"{op_status['name']}", weight=ft.FontWeight.BOLD),
                            ft.Text(f"Status: {op_status['status']}"),
                            ft.ProgressBar(
                                value=op_status['progress'] / 100.0,
                                color=ft.Colors.BLUE,
                                bgcolor=ft.Colors.GREY_300
                            ),
                            ft.Text(f"Progress: {op_status['progress']:.1f}%"),
                            
                            # Steps
                            ft.Column([
                                ft.Text(f"• {step['name']}: {step['status']} ({step['progress']:.1f}%)")
                                for step in op_status.get('steps', [])
                            ], spacing=2)
                        ], spacing=5),
                        padding=10
                    ),
                    elevation=2
                )
                
                self.operation_list.controls.append(progress_card)
        
        self.page.update()
    
    async def run_financial_analysis(self, e):
        """Run financial analysis with progress tracking."""
        try:
            # Create operation
            operation = self.progress_service.create_operation(
                "financial_analysis",
                "Financial Analysis",
                OperationType.FINANCIAL_CALCULATION
            )
            
            # Start operation
            await self.progress_service.start_operation("financial_analysis")
            
            # Create dummy assumptions
            assumptions = self.create_dummy_assumptions()
            
            # Progress callback wrapper
            def progress_callback(operation_id, step_id, progress, message):
                self.progress_service.update_step_progress(operation_id, step_id, progress, message)
            
            # Run financial model
            results = self.financial_service.run_financial_model(
                assumptions,
                detailed_progress_callback=progress_callback
            )
            
            # Complete operation
            await self.progress_service.complete_operation(
                "financial_analysis",
                "Financial analysis completed successfully"
            )
            
        except Exception as ex:
            await self.progress_service.fail_operation(
                "financial_analysis",
                f"Financial analysis failed: {str(ex)}"
            )
    
    async def train_ml_models(self, e):
        """Train ML models with progress tracking."""
        try:
            # Create operation
            operation = self.progress_service.create_operation(
                "ml_training",
                "ML Model Training",
                OperationType.ML_TRAINING
            )
            
            # Start operation
            await self.progress_service.start_operation("ml_training")
            
            # Progress callback wrapper
            def progress_callback(operation_id, step_id, progress, message):
                self.progress_service.update_step_progress(operation_id, step_id, progress, message)
            
            # Train models
            self.ml_service.retrain_models(
                include_historical=True,
                progress_callback=progress_callback
            )
            
            # Complete operation
            await self.progress_service.complete_operation(
                "ml_training",
                "ML model training completed successfully"
            )
            
        except Exception as ex:
            await self.progress_service.fail_operation(
                "ml_training",
                f"ML training failed: {str(ex)}"
            )
    
    async def run_monte_carlo(self, e):
        """Run Monte Carlo simulation with progress tracking."""
        try:
            # Create operation
            operation = self.progress_service.create_operation(
                "monte_carlo",
                "Monte Carlo Simulation",
                OperationType.MONTE_CARLO_SIMULATION
            )
            
            # Start operation
            await self.progress_service.start_operation("monte_carlo")
            
            # Create dummy assumptions
            assumptions = self.create_dummy_assumptions()
            
            # Progress callback wrapper
            def progress_callback(operation_id, step_id, progress, message):
                self.progress_service.update_step_progress(operation_id, step_id, progress, message)
            
            # Run Monte Carlo simulation
            results = self.financial_service.run_monte_carlo_simulation(
                assumptions,
                n_simulations=1000,
                detailed_progress_callback=progress_callback
            )
            
            # Complete operation
            await self.progress_service.complete_operation(
                "monte_carlo",
                "Monte Carlo simulation completed successfully"
            )
            
        except Exception as ex:
            await self.progress_service.fail_operation(
                "monte_carlo",
                f"Monte Carlo simulation failed: {str(ex)}"
            )
    
    async def export_reports(self, e):
        """Export reports with progress tracking."""
        try:
            # Create operation
            operation = self.progress_service.create_operation(
                "export_reports",
                "Export Reports",
                OperationType.DATA_EXPORT
            )
            
            # Start operation
            await self.progress_service.start_operation("export_reports")
            
            # Create dummy data
            client_profile = ClientProfile(
                company_name="Example Company",
                contact_person="John Doe",
                email="<EMAIL>"
            )
            
            assumptions = self.create_dummy_assumptions()
            
            # Simulate financial results
            financial_results = {
                'kpis': {
                    'IRR_equity': 0.15,
                    'IRR_project': 0.12,
                    'NPV_equity': 1500000,
                    'LCOE_eur_kwh': 0.045
                },
                'cashflow': {}
            }
            
            # Progress callback wrapper
            def progress_callback(operation_id, step_id, progress, message):
                self.progress_service.update_step_progress(operation_id, step_id, progress, message)
            
            # Export Excel report
            try:
                filepath = self.export_service.export_excel_report(
                    client_profile,
                    assumptions,
                    financial_results,
                    detailed_progress_callback=progress_callback
                )
                
                # Complete operation
                await self.progress_service.complete_operation(
                    "export_reports",
                    f"Reports exported successfully to {filepath}"
                )
                
            except Exception as export_error:
                # Handle export-specific errors
                await self.progress_service.fail_operation(
                    "export_reports",
                    f"Export failed: {str(export_error)}"
                )
            
        except Exception as ex:
            await self.progress_service.fail_operation(
                "export_reports",
                f"Export operation failed: {str(ex)}"
            )
    
    def create_dummy_assumptions(self) -> EnhancedProjectAssumptions:
        """Create dummy project assumptions for testing."""
        return EnhancedProjectAssumptions(
            capacity_mw=10.0,
            production_mwh_year1=18000,
            project_life_years=25,
            capex_meur=8.5,
            opex_keuros_year1=180,
            ppa_price_eur_kwh=0.045,
            debt_ratio=0.75,
            interest_rate=0.06,
            discount_rate=0.08,
            tax_rate=0.15,
            degradation_rate=0.005,
            land_lease_eur_mw_year=1500,
            location_name="Morocco - Ouarzazate"
        )


async def main(page: ft.Page):
    """Main application entry point."""
    # Initialize progress service
    from services.progress_service import init_progress_service
    init_progress_service(page)
    
    # Create example app
    app = ProgressTrackingExample(page)
    
    # Keep the app running
    await asyncio.sleep(0.1)  # Allow UI to initialize


if __name__ == "__main__":
    ft.app(target=main, view=ft.AppView.WEB_BROWSER)