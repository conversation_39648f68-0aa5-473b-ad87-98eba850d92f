"""
Comprehensive Data Validation Service
=====================================

Advanced data validation framework for financial models and reporting system
with AI-powered validation and professional quality assurance.
"""

import logging
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np
from enum import Enum
import json
import re

# Import for advanced validation
from pydantic import BaseModel, validator, Field, ValidationError
from decimal import Decimal, InvalidOperation
import warnings


class ValidationLevel(Enum):
    """Validation severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ValidationCategory(Enum):
    """Categories of validation checks."""
    DATA_INTEGRITY = "data_integrity"
    BUSINESS_LOGIC = "business_logic"
    FINANCIAL_CONSISTENCY = "financial_consistency"
    TECHNICAL_COMPLIANCE = "technical_compliance"
    REGULATORY_COMPLIANCE = "regulatory_compliance"
    AI_QUALITY = "ai_quality"
    TEMPLATE_VALIDATION = "template_validation"
    EXPORT_VALIDATION = "export_validation"


@dataclass
class ValidationResult:
    """Result of a validation check."""
    rule_id: str
    category: ValidationCategory
    level: ValidationLevel
    message: str
    details: Optional[str] = None
    field_name: Optional[str] = None
    current_value: Optional[Any] = None
    expected_value: Optional[Any] = None
    suggestion: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ValidationSummary:
    """Summary of validation results."""
    total_checks: int
    passed_checks: int
    failed_checks: int
    warnings: int
    errors: int
    critical_errors: int
    overall_status: str
    validation_time: float
    results: List[ValidationResult] = field(default_factory=list)


class FinancialDataValidator(BaseModel):
    """Pydantic model for financial data validation."""
    
    # KPIs validation
    IRR_project: float = Field(ge=0, le=1, description="Project IRR must be between 0 and 100%")
    IRR_equity: float = Field(ge=0, le=1, description="Equity IRR must be between 0 and 100%")
    NPV_project: float = Field(description="Project NPV in currency units")
    NPV_equity: float = Field(description="Equity NPV in currency units")
    LCOE_eur_kwh: float = Field(gt=0, le=1, description="LCOE must be positive and reasonable")
    Min_DSCR: float = Field(ge=0, description="DSCR must be non-negative")
    Payback_Period: Optional[float] = Field(ge=0, le=50, description="Payback period in years")
    
    @validator('IRR_project', 'IRR_equity')
    def validate_irr_range(cls, v):
        if v < 0 or v > 0.5:  # 50% IRR is extremely high
            warnings.warn(f"IRR of {v:.1%} is outside typical range (0-50%)")
        return v
    
    @validator('LCOE_eur_kwh')
    def validate_lcoe_range(cls, v):
        if v < 0.01 or v > 0.30:  # 1 cent to 30 cents per kWh
            warnings.warn(f"LCOE of {v:.3f} €/kWh is outside typical range (0.01-0.30)")
        return v
    
    @validator('Min_DSCR')
    def validate_dscr_range(cls, v):
        if v < 1.0:
            warnings.warn(f"DSCR of {v:.2f} is below 1.0, indicating potential debt service issues")
        return v
    
    @validator('NPV_project', 'NPV_equity')
    def validate_npv_magnitude(cls, v):
        if abs(v) > 1e9:  # 1 billion
            warnings.warn(f"NPV of {v:,.0f} seems extremely large")
        return v


class ProjectAssumptionsValidator(BaseModel):
    """Pydantic model for project assumptions validation."""
    
    capacity_mw: float = Field(gt=0, le=5000, description="Capacity must be positive and reasonable")
    technology_type: str = Field(min_length=1, description="Technology type must be specified")
    capex_meur: float = Field(gt=0, le=10000, description="CAPEX must be positive and reasonable")
    opex_meur_per_year: float = Field(ge=0, le=1000, description="OPEX must be non-negative and reasonable")
    project_life_years: int = Field(ge=1, le=50, description="Project life must be between 1 and 50 years")
    equity_percentage: float = Field(ge=0, le=1, description="Equity percentage must be between 0 and 100%")
    debt_interest_rate: float = Field(ge=0, le=0.25, description="Interest rate must be between 0 and 25%")
    discount_rate: float = Field(ge=0, le=0.30, description="Discount rate must be between 0 and 30%")
    
    @validator('capacity_mw')
    def validate_capacity_range(cls, v):
        if v < 0.1 or v > 1000:  # 100kW to 1GW
            warnings.warn(f"Capacity of {v} MW is outside typical range (0.1-1000 MW)")
        return v
    
    @validator('capex_meur')
    def validate_capex_per_mw(cls, v, values):
        if 'capacity_mw' in values:
            capex_per_mw = v * 1000 / values['capacity_mw']  # k€/MW
            if capex_per_mw < 500 or capex_per_mw > 2000:
                warnings.warn(f"CAPEX of {capex_per_mw:.0f} k€/MW is outside typical range (500-2000)")
        return v
    
    @validator('equity_percentage')
    def validate_equity_percentage(cls, v):
        if v < 0.1 or v > 0.9:
            warnings.warn(f"Equity percentage of {v:.1%} is outside typical range (10-90%)")
        return v


class AIAnalysisValidator(BaseModel):
    """Pydantic model for AI analysis validation."""
    
    confidence_score: float = Field(ge=0, le=100, description="Confidence score must be between 0 and 100")
    processing_time: float = Field(ge=0, le=300, description="Processing time must be reasonable")
    model_used: str = Field(min_length=1, description="Model identifier must be specified")
    narrative: str = Field(min_length=10, description="Narrative must be meaningful")
    recommendations: List[str] = Field(min_items=1, description="At least one recommendation required")
    
    @validator('confidence_score')
    def validate_confidence_range(cls, v):
        if v < 50:
            warnings.warn(f"Low confidence score of {v:.1f}% may indicate unreliable analysis")
        return v
    
    @validator('recommendations')
    def validate_recommendations_quality(cls, v):
        for rec in v:
            if len(rec) < 10:
                warnings.warn(f"Recommendation '{rec}' is too short")
        return v


class DataValidationService:
    """Comprehensive data validation service."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Validation rules registry
        self.validation_rules = {
            ValidationCategory.DATA_INTEGRITY: self._get_data_integrity_rules(),
            ValidationCategory.BUSINESS_LOGIC: self._get_business_logic_rules(),
            ValidationCategory.FINANCIAL_CONSISTENCY: self._get_financial_consistency_rules(),
            ValidationCategory.TECHNICAL_COMPLIANCE: self._get_technical_compliance_rules(),
            ValidationCategory.REGULATORY_COMPLIANCE: self._get_regulatory_compliance_rules(),
            ValidationCategory.AI_QUALITY: self._get_ai_quality_rules(),
            ValidationCategory.TEMPLATE_VALIDATION: self._get_template_validation_rules(),
            ValidationCategory.EXPORT_VALIDATION: self._get_export_validation_rules()
        }
        
        # Industry benchmarks for validation
        self.industry_benchmarks = {
            'solar_pv': {
                'capex_range_eur_kw': (400, 1200),
                'opex_range_eur_kw_year': (10, 40),
                'capacity_factor_range': (0.12, 0.25),
                'irr_range': (0.06, 0.18),
                'lcoe_range_eur_kwh': (0.025, 0.080)
            },
            'wind_onshore': {
                'capex_range_eur_kw': (800, 1500),
                'opex_range_eur_kw_year': (15, 50),
                'capacity_factor_range': (0.20, 0.40),
                'irr_range': (0.07, 0.16),
                'lcoe_range_eur_kwh': (0.030, 0.070)
            },
            'wind_offshore': {
                'capex_range_eur_kw': (2000, 4000),
                'opex_range_eur_kw_year': (40, 100),
                'capacity_factor_range': (0.35, 0.55),
                'irr_range': (0.08, 0.15),
                'lcoe_range_eur_kwh': (0.050, 0.120)
            }
        }
        
        self.logger.info("Data validation service initialized")
    
    def validate_comprehensive_data(self,
                                   client_profile: Dict[str, Any],
                                   assumptions: Dict[str, Any],
                                   financial_results: Dict[str, Any],
                                   ai_analysis: Optional[Dict[str, Any]] = None,
                                   validation_level: str = "standard") -> ValidationSummary:
        """Perform comprehensive data validation."""
        
        start_time = datetime.now()
        results = []
        
        try:
            self.logger.info("Starting comprehensive data validation")
            
            # 1. Data Integrity Validation
            results.extend(self._validate_data_integrity(client_profile, assumptions, financial_results))
            
            # 2. Business Logic Validation
            results.extend(self._validate_business_logic(assumptions, financial_results))
            
            # 3. Financial Consistency Validation
            results.extend(self._validate_financial_consistency(financial_results))
            
            # 4. Technical Compliance Validation
            results.extend(self._validate_technical_compliance(assumptions))
            
            # 5. Regulatory Compliance Validation
            results.extend(self._validate_regulatory_compliance(assumptions, financial_results))
            
            # 6. AI Quality Validation (if available)
            if ai_analysis:
                results.extend(self._validate_ai_quality(ai_analysis))
            
            # 7. Industry Benchmark Validation
            results.extend(self._validate_industry_benchmarks(assumptions, financial_results))
            
            # Generate summary
            validation_time = (datetime.now() - start_time).total_seconds()
            summary = self._generate_validation_summary(results, validation_time)
            
            self.logger.info(f"Validation completed in {validation_time:.2f}s with {len(results)} checks")
            return summary
            
        except Exception as e:
            self.logger.error(f"Validation failed: {str(e)}")
            raise
    
    def _validate_data_integrity(self,
                                client_profile: Dict[str, Any],
                                assumptions: Dict[str, Any],
                                financial_results: Dict[str, Any]) -> List[ValidationResult]:
        """Validate data integrity and completeness."""
        
        results = []
        
        # Required fields validation
        required_client_fields = ['company_name', 'project_name', 'consultant']
        for field in required_client_fields:
            if not client_profile.get(field):
                results.append(ValidationResult(
                    rule_id="CLIENT_REQUIRED_FIELD",
                    category=ValidationCategory.DATA_INTEGRITY,
                    level=ValidationLevel.ERROR,
                    message=f"Required client field '{field}' is missing or empty",
                    field_name=field,
                    suggestion=f"Please provide a value for {field}"
                ))
        
        # Assumptions validation using Pydantic
        try:
            ProjectAssumptionsValidator(**assumptions)
        except ValidationError as e:
            for error in e.errors():
                results.append(ValidationResult(
                    rule_id="ASSUMPTIONS_VALIDATION",
                    category=ValidationCategory.DATA_INTEGRITY,
                    level=ValidationLevel.ERROR,
                    message=f"Assumptions validation failed: {error['msg']}",
                    field_name='.'.join(str(x) for x in error['loc']),
                    current_value=error.get('input'),
                    suggestion="Please check the input value and ensure it meets the requirements"
                ))
        
        # Financial results validation
        if 'kpis' in financial_results:
            try:
                FinancialDataValidator(**financial_results['kpis'])
            except ValidationError as e:
                for error in e.errors():
                    results.append(ValidationResult(
                        rule_id="FINANCIAL_KPI_VALIDATION",
                        category=ValidationCategory.DATA_INTEGRITY,
                        level=ValidationLevel.ERROR,
                        message=f"Financial KPI validation failed: {error['msg']}",
                        field_name='.'.join(str(x) for x in error['loc']),
                        current_value=error.get('input'),
                        suggestion="Please verify the financial calculations and inputs"
                    ))
        
        # Data types validation
        if 'cashflow' in financial_results:
            cashflow = financial_results['cashflow']
            if isinstance(cashflow, pd.DataFrame):
                # Check for missing values
                missing_values = cashflow.isnull().sum()
                for column, count in missing_values.items():
                    if count > 0:
                        results.append(ValidationResult(
                            rule_id="CASHFLOW_MISSING_VALUES",
                            category=ValidationCategory.DATA_INTEGRITY,
                            level=ValidationLevel.WARNING,
                            message=f"Cashflow column '{column}' has {count} missing values",
                            field_name=column,
                            current_value=count,
                            suggestion="Consider filling missing values with appropriate estimates"
                        ))
                
                # Check for infinite or extremely large values
                numeric_columns = cashflow.select_dtypes(include=[np.number]).columns
                for column in numeric_columns:
                    if cashflow[column].isinf().any():
                        results.append(ValidationResult(
                            rule_id="CASHFLOW_INFINITE_VALUES",
                            category=ValidationCategory.DATA_INTEGRITY,
                            level=ValidationLevel.ERROR,
                            message=f"Cashflow column '{column}' contains infinite values",
                            field_name=column,
                            suggestion="Review calculations that might cause division by zero"
                        ))
                    
                    max_val = cashflow[column].max()
                    if abs(max_val) > 1e12:  # 1 trillion
                        results.append(ValidationResult(
                            rule_id="CASHFLOW_EXTREME_VALUES",
                            category=ValidationCategory.DATA_INTEGRITY,
                            level=ValidationLevel.WARNING,
                            message=f"Cashflow column '{column}' has extremely large values",
                            field_name=column,
                            current_value=max_val,
                            suggestion="Verify calculations and consider using appropriate units"
                        ))
        
        return results
    
    def _validate_business_logic(self,
                                assumptions: Dict[str, Any],
                                financial_results: Dict[str, Any]) -> List[ValidationResult]:
        """Validate business logic and relationships."""
        
        results = []
        
        # IRR consistency check
        kpis = financial_results.get('kpis', {})
        irr_project = kpis.get('IRR_project', 0)
        irr_equity = kpis.get('IRR_equity', 0)
        
        if irr_equity > 0 and irr_project > 0:
            if irr_equity <= irr_project:
                results.append(ValidationResult(
                    rule_id="IRR_LOGIC_CHECK",
                    category=ValidationCategory.BUSINESS_LOGIC,
                    level=ValidationLevel.WARNING,
                    message="Equity IRR should typically be higher than project IRR due to leverage",
                    field_name="IRR_equity",
                    current_value=irr_equity,
                    expected_value=f"> {irr_project:.1%}",
                    suggestion="Review debt structure and leverage assumptions"
                ))
        
        # CAPEX vs OPEX ratio check
        capex = assumptions.get('capex_meur', 0)
        opex = assumptions.get('opex_meur_per_year', 0)
        capacity = assumptions.get('capacity_mw', 1)
        
        if capex > 0 and opex > 0:
            capex_per_mw = capex * 1000 / capacity  # k€/MW
            opex_per_mw = opex * 1000 / capacity    # k€/MW/year
            
            if opex_per_mw > capex_per_mw * 0.1:  # OPEX > 10% of CAPEX per year
                results.append(ValidationResult(
                    rule_id="CAPEX_OPEX_RATIO",
                    category=ValidationCategory.BUSINESS_LOGIC,
                    level=ValidationLevel.WARNING,
                    message="OPEX appears high relative to CAPEX",
                    field_name="opex_meur_per_year",
                    current_value=opex_per_mw,
                    expected_value=f"< {capex_per_mw * 0.1:.1f} k€/MW/year",
                    suggestion="Review OPEX assumptions and benchmarks"
                ))
        
        # Debt service coverage consistency
        min_dscr = kpis.get('Min_DSCR', 0)
        equity_percentage = assumptions.get('equity_percentage', 1)
        
        if min_dscr > 0 and equity_percentage < 1:
            if min_dscr < 1.2:
                results.append(ValidationResult(
                    rule_id="DSCR_ADEQUACY",
                    category=ValidationCategory.BUSINESS_LOGIC,
                    level=ValidationLevel.WARNING,
                    message="DSCR below 1.2 may indicate tight debt service coverage",
                    field_name="Min_DSCR",
                    current_value=min_dscr,
                    expected_value=">= 1.2",
                    suggestion="Consider increasing equity or reducing debt to improve DSCR"
                ))
        
        # Project life vs payback period
        project_life = assumptions.get('project_life_years', 25)
        payback_period = kpis.get('Payback_Period', 0)
        
        if payback_period > 0 and project_life > 0:
            if payback_period > project_life * 0.8:  # Payback > 80% of project life
                results.append(ValidationResult(
                    rule_id="PAYBACK_PROJECT_LIFE",
                    category=ValidationCategory.BUSINESS_LOGIC,
                    level=ValidationLevel.WARNING,
                    message="Payback period is very long relative to project life",
                    field_name="Payback_Period",
                    current_value=payback_period,
                    expected_value=f"< {project_life * 0.8:.1f} years",
                    suggestion="Consider improving project economics or extending project life"
                ))
        
        return results
    
    def _validate_financial_consistency(self,
                                       financial_results: Dict[str, Any]) -> List[ValidationResult]:
        """Validate financial consistency and calculations."""
        
        results = []
        
        kpis = financial_results.get('kpis', {})
        
        # NPV consistency check
        npv_project = kpis.get('NPV_project', 0)
        npv_equity = kpis.get('NPV_equity', 0)
        
        if npv_project > 0 and npv_equity <= 0:
            results.append(ValidationResult(
                rule_id="NPV_CONSISTENCY",
                category=ValidationCategory.FINANCIAL_CONSISTENCY,
                level=ValidationLevel.WARNING,
                message="Project NPV is positive but equity NPV is negative",
                field_name="NPV_equity",
                current_value=npv_equity,
                suggestion="Review debt terms and equity investment structure"
            ))
        
        # LCOE reasonableness check
        lcoe = kpis.get('LCOE_eur_kwh', 0)
        if lcoe > 0:
            if lcoe > 0.20:  # 20 cents per kWh
                results.append(ValidationResult(
                    rule_id="LCOE_REASONABLENESS",
                    category=ValidationCategory.FINANCIAL_CONSISTENCY,
                    level=ValidationLevel.WARNING,
                    message="LCOE appears high compared to market rates",
                    field_name="LCOE_eur_kwh",
                    current_value=lcoe,
                    expected_value="< 0.20 €/kWh",
                    suggestion="Review cost assumptions and revenue projections"
                ))
        
        # Cashflow consistency
        if 'cashflow' in financial_results:
            cashflow = financial_results['cashflow']
            if isinstance(cashflow, pd.DataFrame):
                # Check for negative equity cash flows in later years
                if 'Free_Cash_Flow_Equity' in cashflow.columns and 'Year' in cashflow.columns:
                    later_years = cashflow[cashflow['Year'] > 5]
                    if not later_years.empty:
                        negative_years = later_years[later_years['Free_Cash_Flow_Equity'] < 0]
                        if len(negative_years) > 2:
                            results.append(ValidationResult(
                                rule_id="CASHFLOW_CONSISTENCY",
                                category=ValidationCategory.FINANCIAL_CONSISTENCY,
                                level=ValidationLevel.WARNING,
                                message="Multiple negative equity cash flows in later years",
                                field_name="Free_Cash_Flow_Equity",
                                current_value=len(negative_years),
                                suggestion="Review operational assumptions and debt structure"
                            ))
        
        return results
    
    def _validate_technical_compliance(self,
                                      assumptions: Dict[str, Any]) -> List[ValidationResult]:
        """Validate technical compliance and standards."""
        
        results = []
        
        # Technology-specific validation
        technology = assumptions.get('technology_type', '').lower()
        capacity = assumptions.get('capacity_mw', 0)
        
        if 'solar' in technology:
            # Solar-specific checks
            if capacity > 0:
                if capacity > 1000:  # 1 GW
                    results.append(ValidationResult(
                        rule_id="SOLAR_CAPACITY_LIMIT",
                        category=ValidationCategory.TECHNICAL_COMPLIANCE,
                        level=ValidationLevel.INFO,
                        message="Very large solar project capacity",
                        field_name="capacity_mw",
                        current_value=capacity,
                        suggestion="Verify grid connection and permitting requirements"
                    ))
        
        elif 'wind' in technology:
            # Wind-specific checks
            if capacity > 0:
                if capacity > 500:  # 500 MW
                    results.append(ValidationResult(
                        rule_id="WIND_CAPACITY_LIMIT",
                        category=ValidationCategory.TECHNICAL_COMPLIANCE,
                        level=ValidationLevel.INFO,
                        message="Large wind project capacity",
                        field_name="capacity_mw",
                        current_value=capacity,
                        suggestion="Consider environmental impact and grid stability"
                    ))
        
        # Project life validation
        project_life = assumptions.get('project_life_years', 25)
        if technology and project_life > 0:
            if 'solar' in technology and project_life > 30:
                results.append(ValidationResult(
                    rule_id="SOLAR_PROJECT_LIFE",
                    category=ValidationCategory.TECHNICAL_COMPLIANCE,
                    level=ValidationLevel.WARNING,
                    message="Solar project life exceeds typical range",
                    field_name="project_life_years",
                    current_value=project_life,
                    expected_value="<= 30 years",
                    suggestion="Consider equipment warranty and performance guarantees"
                ))
            
            elif 'wind' in technology and project_life > 25:
                results.append(ValidationResult(
                    rule_id="WIND_PROJECT_LIFE",
                    category=ValidationCategory.TECHNICAL_COMPLIANCE,
                    level=ValidationLevel.WARNING,
                    message="Wind project life exceeds typical range",
                    field_name="project_life_years",
                    current_value=project_life,
                    expected_value="<= 25 years",
                    suggestion="Consider turbine design life and maintenance requirements"
                ))
        
        return results
    
    def _validate_regulatory_compliance(self,
                                       assumptions: Dict[str, Any],
                                       financial_results: Dict[str, Any]) -> List[ValidationResult]:
        """Validate regulatory compliance requirements."""
        
        results = []
        
        # Debt-to-equity ratio limits
        equity_percentage = assumptions.get('equity_percentage', 1)
        if equity_percentage < 0.2:  # Less than 20% equity
            results.append(ValidationResult(
                rule_id="MINIMUM_EQUITY_REQUIREMENT",
                category=ValidationCategory.REGULATORY_COMPLIANCE,
                level=ValidationLevel.WARNING,
                message="Equity percentage below typical regulatory/lender requirements",
                field_name="equity_percentage",
                current_value=equity_percentage,
                expected_value=">= 0.2 (20%)",
                suggestion="Consider regulatory requirements and lender covenants"
            ))
        
        # Interest rate reasonableness
        interest_rate = assumptions.get('debt_interest_rate', 0)
        if interest_rate > 0.15:  # Above 15%
            results.append(ValidationResult(
                rule_id="INTEREST_RATE_REASONABLENESS",
                category=ValidationCategory.REGULATORY_COMPLIANCE,
                level=ValidationLevel.WARNING,
                message="Interest rate appears high for renewable energy projects",
                field_name="debt_interest_rate",
                current_value=interest_rate,
                expected_value="< 0.15 (15%)",
                suggestion="Review market conditions and credit rating"
            ))
        
        # Environmental compliance indicators
        technology = assumptions.get('technology_type', '').lower()
        capacity = assumptions.get('capacity_mw', 0)
        
        if capacity > 50:  # Large projects typically require EIA
            results.append(ValidationResult(
                rule_id="ENVIRONMENTAL_IMPACT_ASSESSMENT",
                category=ValidationCategory.REGULATORY_COMPLIANCE,
                level=ValidationLevel.INFO,
                message="Large project likely requires Environmental Impact Assessment",
                field_name="capacity_mw",
                current_value=capacity,
                suggestion="Ensure EIA compliance and environmental permits"
            ))
        
        return results
    
    def _validate_ai_quality(self,
                            ai_analysis: Dict[str, Any]) -> List[ValidationResult]:
        """Validate AI analysis quality and reliability."""
        
        results = []
        
        try:
            # Validate using Pydantic model
            AIAnalysisValidator(**ai_analysis)
        except ValidationError as e:
            for error in e.errors():
                results.append(ValidationResult(
                    rule_id="AI_ANALYSIS_VALIDATION",
                    category=ValidationCategory.AI_QUALITY,
                    level=ValidationLevel.WARNING,
                    message=f"AI analysis validation failed: {error['msg']}",
                    field_name='.'.join(str(x) for x in error['loc']),
                    current_value=error.get('input'),
                    suggestion="Review AI analysis configuration and model parameters"
                ))
        
        # Content quality checks
        narrative = ai_analysis.get('narrative', '')
        if narrative:
            # Check for repetitive content
            words = narrative.split()
            if len(set(words)) < len(words) * 0.5:  # Less than 50% unique words
                results.append(ValidationResult(
                    rule_id="AI_CONTENT_REPETITION",
                    category=ValidationCategory.AI_QUALITY,
                    level=ValidationLevel.WARNING,
                    message="AI narrative contains repetitive content",
                    field_name="narrative",
                    suggestion="Consider adjusting AI model parameters or prompts"
                ))
            
            # Check for placeholder content
            placeholder_phrases = ['lorem ipsum', 'placeholder', 'example', 'sample']
            if any(phrase in narrative.lower() for phrase in placeholder_phrases):
                results.append(ValidationResult(
                    rule_id="AI_PLACEHOLDER_CONTENT",
                    category=ValidationCategory.AI_QUALITY,
                    level=ValidationLevel.ERROR,
                    message="AI narrative contains placeholder content",
                    field_name="narrative",
                    suggestion="Regenerate AI analysis with proper data"
                ))
        
        # Recommendation quality
        recommendations = ai_analysis.get('recommendations', [])
        if recommendations:
            generic_recommendations = ['consider', 'review', 'analyze', 'evaluate']
            generic_count = sum(1 for rec in recommendations 
                              if any(generic in rec.lower() for generic in generic_recommendations))
            
            if generic_count > len(recommendations) * 0.7:  # More than 70% generic
                results.append(ValidationResult(
                    rule_id="AI_GENERIC_RECOMMENDATIONS",
                    category=ValidationCategory.AI_QUALITY,
                    level=ValidationLevel.WARNING,
                    message="AI recommendations appear generic",
                    field_name="recommendations",
                    current_value=generic_count,
                    suggestion="Consider more specific prompts or model fine-tuning"
                ))
        
        return results
    
    def _validate_industry_benchmarks(self,
                                     assumptions: Dict[str, Any],
                                     financial_results: Dict[str, Any]) -> List[ValidationResult]:
        """Validate against industry benchmarks."""
        
        results = []
        
        technology = assumptions.get('technology_type', '').lower()
        capacity = assumptions.get('capacity_mw', 0)
        capex = assumptions.get('capex_meur', 0)
        
        # Find appropriate benchmark
        benchmark_key = None
        if 'solar' in technology:
            benchmark_key = 'solar_pv'
        elif 'wind' in technology:
            if 'offshore' in technology:
                benchmark_key = 'wind_offshore'
            else:
                benchmark_key = 'wind_onshore'
        
        if benchmark_key and capacity > 0:
            benchmarks = self.industry_benchmarks[benchmark_key]
            
            # CAPEX benchmark
            if capex > 0:
                capex_per_kw = capex * 1000 / capacity  # €/kW
                capex_range = benchmarks['capex_range_eur_kw']
                
                if capex_per_kw < capex_range[0] or capex_per_kw > capex_range[1]:
                    results.append(ValidationResult(
                        rule_id="CAPEX_BENCHMARK",
                        category=ValidationCategory.BUSINESS_LOGIC,
                        level=ValidationLevel.WARNING,
                        message=f"CAPEX outside industry benchmark range for {technology}",
                        field_name="capex_meur",
                        current_value=capex_per_kw,
                        expected_value=f"{capex_range[0]}-{capex_range[1]} €/kW",
                        suggestion="Review cost assumptions against market data"
                    ))
            
            # IRR benchmark
            kpis = financial_results.get('kpis', {})
            irr_project = kpis.get('IRR_project', 0)
            if irr_project > 0:
                irr_range = benchmarks['irr_range']
                
                if irr_project < irr_range[0] or irr_project > irr_range[1]:
                    results.append(ValidationResult(
                        rule_id="IRR_BENCHMARK",
                        category=ValidationCategory.BUSINESS_LOGIC,
                        level=ValidationLevel.INFO,
                        message=f"Project IRR outside typical range for {technology}",
                        field_name="IRR_project",
                        current_value=irr_project,
                        expected_value=f"{irr_range[0]:.1%}-{irr_range[1]:.1%}",
                        suggestion="Compare with recent market transactions"
                    ))
            
            # LCOE benchmark
            lcoe = kpis.get('LCOE_eur_kwh', 0)
            if lcoe > 0:
                lcoe_range = benchmarks['lcoe_range_eur_kwh']
                
                if lcoe < lcoe_range[0] or lcoe > lcoe_range[1]:
                    results.append(ValidationResult(
                        rule_id="LCOE_BENCHMARK",
                        category=ValidationCategory.BUSINESS_LOGIC,
                        level=ValidationLevel.INFO,
                        message=f"LCOE outside typical range for {technology}",
                        field_name="LCOE_eur_kwh",
                        current_value=lcoe,
                        expected_value=f"{lcoe_range[0]:.3f}-{lcoe_range[1]:.3f} €/kWh",
                        suggestion="Verify cost and revenue assumptions"
                    ))
        
        return results
    
    def _generate_validation_summary(self,
                                    results: List[ValidationResult],
                                    validation_time: float) -> ValidationSummary:
        """Generate validation summary from results."""
        
        total_checks = len(results)
        critical_errors = sum(1 for r in results if r.level == ValidationLevel.CRITICAL)
        errors = sum(1 for r in results if r.level == ValidationLevel.ERROR)
        warnings = sum(1 for r in results if r.level == ValidationLevel.WARNING)
        
        failed_checks = critical_errors + errors
        passed_checks = total_checks - failed_checks
        
        # Determine overall status
        if critical_errors > 0:
            overall_status = "CRITICAL"
        elif errors > 0:
            overall_status = "FAILED"
        elif warnings > 0:
            overall_status = "PASSED_WITH_WARNINGS"
        else:
            overall_status = "PASSED"
        
        return ValidationSummary(
            total_checks=total_checks,
            passed_checks=passed_checks,
            failed_checks=failed_checks,
            warnings=warnings,
            errors=errors,
            critical_errors=critical_errors,
            overall_status=overall_status,
            validation_time=validation_time,
            results=results
        )
    
    def _get_data_integrity_rules(self) -> List[str]:
        """Get data integrity validation rules."""
        return [
            "CLIENT_REQUIRED_FIELD",
            "ASSUMPTIONS_VALIDATION",
            "FINANCIAL_KPI_VALIDATION",
            "CASHFLOW_MISSING_VALUES",
            "CASHFLOW_INFINITE_VALUES",
            "CASHFLOW_EXTREME_VALUES"
        ]
    
    def _get_business_logic_rules(self) -> List[str]:
        """Get business logic validation rules."""
        return [
            "IRR_LOGIC_CHECK",
            "CAPEX_OPEX_RATIO",
            "DSCR_ADEQUACY",
            "PAYBACK_PROJECT_LIFE",
            "CAPEX_BENCHMARK",
            "IRR_BENCHMARK",
            "LCOE_BENCHMARK"
        ]
    
    def _get_financial_consistency_rules(self) -> List[str]:
        """Get financial consistency validation rules."""
        return [
            "NPV_CONSISTENCY",
            "LCOE_REASONABLENESS",
            "CASHFLOW_CONSISTENCY"
        ]
    
    def _get_technical_compliance_rules(self) -> List[str]:
        """Get technical compliance validation rules."""
        return [
            "SOLAR_CAPACITY_LIMIT",
            "WIND_CAPACITY_LIMIT",
            "SOLAR_PROJECT_LIFE",
            "WIND_PROJECT_LIFE"
        ]
    
    def _get_regulatory_compliance_rules(self) -> List[str]:
        """Get regulatory compliance validation rules."""
        return [
            "MINIMUM_EQUITY_REQUIREMENT",
            "INTEREST_RATE_REASONABLENESS",
            "ENVIRONMENTAL_IMPACT_ASSESSMENT"
        ]
    
    def _get_ai_quality_rules(self) -> List[str]:
        """Get AI quality validation rules."""
        return [
            "AI_ANALYSIS_VALIDATION",
            "AI_CONTENT_REPETITION",
            "AI_PLACEHOLDER_CONTENT",
            "AI_GENERIC_RECOMMENDATIONS"
        ]
    
    def _get_template_validation_rules(self) -> List[str]:
        """Get template validation rules."""
        return [
            "TEMPLATE_SYNTAX_CHECK",
            "TEMPLATE_VARIABLE_CHECK",
            "TEMPLATE_RENDERING_CHECK"
        ]
    
    def _get_export_validation_rules(self) -> List[str]:
        """Get export validation rules."""
        return [
            "EXPORT_FORMAT_VALIDATION",
            "EXPORT_DATA_COMPLETENESS",
            "EXPORT_FILE_INTEGRITY"
        ]
    
    def export_validation_report(self,
                                summary: ValidationSummary,
                                output_path: Path) -> Path:
        """Export validation report to file."""
        
        try:
            report_data = {
                'validation_summary': {
                    'total_checks': summary.total_checks,
                    'passed_checks': summary.passed_checks,
                    'failed_checks': summary.failed_checks,
                    'warnings': summary.warnings,
                    'errors': summary.errors,
                    'critical_errors': summary.critical_errors,
                    'overall_status': summary.overall_status,
                    'validation_time': summary.validation_time
                },
                'validation_results': [
                    {
                        'rule_id': result.rule_id,
                        'category': result.category.value,
                        'level': result.level.value,
                        'message': result.message,
                        'details': result.details,
                        'field_name': result.field_name,
                        'current_value': str(result.current_value),
                        'expected_value': str(result.expected_value),
                        'suggestion': result.suggestion,
                        'timestamp': result.timestamp.isoformat()
                    }
                    for result in summary.results
                ],
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Validation report exported to: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to export validation report: {str(e)}")
            raise
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation service statistics."""
        
        return {
            'total_rules': sum(len(rules) for rules in self.validation_rules.values()),
            'categories': list(self.validation_rules.keys()),
            'industry_benchmarks': list(self.industry_benchmarks.keys()),
            'service_version': '2.0',
            'last_updated': datetime.now().isoformat()
        }
    
    def update_industry_benchmarks(self, 
                                  technology: str, 
                                  benchmarks: Dict[str, Any]):
        """Update industry benchmarks for a technology."""
        
        self.industry_benchmarks[technology] = benchmarks
        self.logger.info(f"Updated industry benchmarks for {technology}")
    
    def add_custom_validation_rule(self,
                                  category: ValidationCategory,
                                  rule_function: Callable,
                                  rule_id: str):
        """Add custom validation rule."""
        
        if category not in self.validation_rules:
            self.validation_rules[category] = []
        
        self.validation_rules[category].append(rule_id)
        self.logger.info(f"Added custom validation rule: {rule_id} to category {category.value}")
    
    def validate_template_data(self,
                              template_name: str,
                              template_data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate data for template rendering."""
        
        results = []
        
        # Check required template variables
        required_vars = ['client_profile', 'assumptions', 'financial_results']
        for var in required_vars:
            if var not in template_data:
                results.append(ValidationResult(
                    rule_id="TEMPLATE_REQUIRED_VAR",
                    category=ValidationCategory.TEMPLATE_VALIDATION,
                    level=ValidationLevel.ERROR,
                    message=f"Required template variable '{var}' is missing",
                    field_name=var,
                    suggestion=f"Ensure {var} is included in template data"
                ))
        
        # Validate template-specific requirements
        if template_name == "pdf_report.html":
            # PDF-specific validation
            if 'charts' in template_data:
                charts = template_data['charts']
                if not isinstance(charts, dict):
                    results.append(ValidationResult(
                        rule_id="PDF_CHARTS_FORMAT",
                        category=ValidationCategory.TEMPLATE_VALIDATION,
                        level=ValidationLevel.ERROR,
                        message="Charts data must be a dictionary for PDF template",
                        field_name="charts",
                        suggestion="Ensure charts are properly formatted"
                    ))
        
        return results
    
    def validate_export_data(self,
                            export_format: str,
                            export_data: Dict[str, Any]) -> List[ValidationResult]:
        """Validate data for export operations."""
        
        results = []
        
        # Format-specific validation
        if export_format.lower() == 'pdf':
            # PDF-specific checks
            if 'charts' in export_data:
                charts = export_data['charts']
                for chart_name, chart_data in charts.items():
                    if not isinstance(chart_data, (str, bytes)):
                        results.append(ValidationResult(
                            rule_id="PDF_CHART_FORMAT",
                            category=ValidationCategory.EXPORT_VALIDATION,
                            level=ValidationLevel.ERROR,
                            message=f"Chart '{chart_name}' must be string or bytes for PDF export",
                            field_name=f"charts.{chart_name}",
                            suggestion="Ensure chart data is properly encoded"
                        ))
        
        elif export_format.lower() == 'excel':
            # Excel-specific checks
            if 'financial_results' in export_data:
                financial_results = export_data['financial_results']
                if 'cashflow' in financial_results:
                    cashflow = financial_results['cashflow']
                    if not isinstance(cashflow, pd.DataFrame):
                        results.append(ValidationResult(
                            rule_id="EXCEL_CASHFLOW_FORMAT",
                            category=ValidationCategory.EXPORT_VALIDATION,
                            level=ValidationLevel.ERROR,
                            message="Cashflow data must be a DataFrame for Excel export",
                            field_name="financial_results.cashflow",
                            suggestion="Convert cashflow data to pandas DataFrame"
                        ))
        
        return results