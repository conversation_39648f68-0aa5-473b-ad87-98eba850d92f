"""
Location Comparison Service
===========================

Service for location comparison and analysis.
"""

from typing import Dict, Any, List, Optional, Callable
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from models.location_config import LocationManager, LocationConfig
from models.project_assumptions import EnhancedProjectAssumptions
from services.financial_service import FinancialModelService
from .error_handler import error_handler, ErrorSeverity, FallbackManager, LocationServiceError


class LocationComparisonService:
    """Service for comparing different project locations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.location_manager = LocationManager()
        self.financial_service = FinancialModelService()
        self._comparison_results: Optional[Dict[str, Any]] = None
    
    def compare_locations(self,
                         base_assumptions: EnhancedProjectAssumptions,
                         location_names: List[str],
                         progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Compare multiple locations for the project."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up location comparison...")
            
            comparison_results = {}
            total_locations = len(location_names)
            
            for i, location_name in enumerate(location_names):
                if progress_callback:
                    progress = 20 + (i / total_locations) * 60
                    progress_callback(progress, f"Analyzing {location_name}...")
                
                # Get location configuration with fallback
                location_config = self.location_manager.get_location(location_name)
                if not location_config:
                    self.logger.warning(f"Location {location_name} not found, using fallback data")
                    # Create fallback location config
                    fallback_data = FallbackManager.get_fallback_location_data()
                    fallback_data['name'] = location_name
                    location_config = LocationConfig(**fallback_data)
                    # Continue with fallback instead of skipping
                
                # Create modified assumptions for this location
                location_assumptions = self._create_location_assumptions(base_assumptions, location_config)
                
                # Run financial model for this location
                try:
                    financial_results = self.financial_service.run_financial_model(location_assumptions)
                    
                    # Store results
                    comparison_results[location_name] = {
                        'location_config': location_config.to_dict(),
                        'assumptions': location_assumptions.to_dict(),
                        'financial_results': financial_results,
                        'kpis': financial_results['kpis'],
                        'summary': self._create_location_summary(location_config, financial_results)
                    }
                    
                except Exception as e:
                    self.logger.error(f"Error analyzing location {location_name}: {str(e)}")
                    comparison_results[location_name] = {
                        'error': str(e),
                        'location_config': location_config.to_dict()
                    }
            
            if progress_callback:
                progress_callback(90, "Generating comparison analysis...")
            
            # Generate comparison analysis
            try:
                comparison_analysis = self._generate_comparison_analysis(comparison_results)
            except Exception as e:
                self.logger.error(f"Error generating comparison analysis: {str(e)}")
                comparison_analysis = {
                    'error': f"Analysis generation failed: {str(e)}",
                    'rankings': {},
                    'statistics': {},
                    'recommendations': {},
                    'comparison_matrix': []
                }

            self._comparison_results = {
                'locations': comparison_results,
                'analysis': comparison_analysis,
                'base_assumptions': base_assumptions.to_dict(),
                'comparison_date': pd.Timestamp.now().isoformat()
            }
            
            if progress_callback:
                progress_callback(100, "Location comparison completed")
            
            self.logger.info(f"Location comparison completed for {len(location_names)} locations")
            return self._comparison_results
            
        except Exception as e:
            self.logger.error(f"Error in location comparison: {str(e)}")
            raise
    
    def _create_location_assumptions(self, 
                                   base_assumptions: EnhancedProjectAssumptions,
                                   location_config: LocationConfig) -> EnhancedProjectAssumptions:
        """Create modified assumptions for a specific location."""
        # Create a copy of base assumptions
        location_assumptions = base_assumptions.copy_with_modifications(
            location_name=location_config.name,
            production_mwh_year1=location_config.production_mwh_year1,
            capex_meur=location_config.capex_meur,
            opex_keuros_year1=location_config.opex_keuros_year1,
            ppa_price_eur_kwh=location_config.ppa_price_eur_kwh,
            land_lease_eur_mw_year=location_config.land_lease_eur_mw_year
        )
        
        return location_assumptions
    
    def _create_location_summary(self, 
                               location_config: LocationConfig,
                               financial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary for a location."""
        kpis = financial_results.get('kpis', {})
        
        return {
            'location_name': location_config.name,
            'description': location_config.description,
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'npv_project_meur': kpis.get('NPV_project', 0) / 1e6,
            'npv_equity_meur': kpis.get('NPV_equity', 0) / 1e6,
            'lcoe_eur_kwh': kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'capacity_factor': location_config.production_mwh_year1 / (8760 * financial_results['assumptions']['capacity_mw']),
            'specific_capex_eur_kw': location_config.capex_meur * 1e6 / (financial_results['assumptions']['capacity_mw'] * 1000),
            'advantages': location_config.advantages,
            'challenges': location_config.challenges,
            'irradiation_kwh_m2': location_config.irradiation_kwh_m2
        }
    
    def _generate_comparison_analysis(self, comparison_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive comparison analysis."""
        analysis = {}
        
        # Extract valid results (no errors)
        valid_results = {name: data for name, data in comparison_results.items() 
                        if 'error' not in data}
        
        if not valid_results:
            return {'error': 'No valid location results to compare'}
        
        # Create comparison DataFrame
        comparison_data = []
        for location_name, data in valid_results.items():
            summary = data['summary']
            comparison_data.append({
                'Location': location_name,
                'IRR_Project': summary['irr_project'],
                'IRR_Equity': summary['irr_equity'],
                'NPV_Project_MEUR': summary['npv_project_meur'],
                'NPV_Equity_MEUR': summary['npv_equity_meur'],
                'LCOE_EUR_kWh': summary['lcoe_eur_kwh'],
                'Min_DSCR': summary['min_dscr'],
                'Capacity_Factor': summary['capacity_factor'],
                'Specific_CAPEX_EUR_kW': summary['specific_capex_eur_kw'],
                'Irradiation_kWh_m2': summary['irradiation_kwh_m2']
            })
        
        comparison_df = pd.DataFrame(comparison_data)

        # Validate that we have numeric data
        numeric_columns = ['IRR_Project', 'IRR_Equity', 'NPV_Project_MEUR', 'NPV_Equity_MEUR',
                          'LCOE_EUR_kWh', 'Min_DSCR', 'Capacity_Factor', 'Specific_CAPEX_EUR_kW']

        for col in numeric_columns:
            if col in comparison_df.columns:
                # Replace any infinite values with NaN
                comparison_df[col] = comparison_df[col].replace([np.inf, -np.inf], np.nan)
                # Fill NaN values with 0 for calculations
                comparison_df[col] = comparison_df[col].fillna(0)

        # Rankings
        analysis['rankings'] = {
            'best_irr_project': self._get_ranking(comparison_df, 'IRR_Project', ascending=False),
            'best_irr_equity': self._get_ranking(comparison_df, 'IRR_Equity', ascending=False),
            'best_npv_project': self._get_ranking(comparison_df, 'NPV_Project_MEUR', ascending=False),
            'best_npv_equity': self._get_ranking(comparison_df, 'NPV_Equity_MEUR', ascending=False),
            'lowest_lcoe': self._get_ranking(comparison_df, 'LCOE_EUR_kWh', ascending=True),
            'highest_dscr': self._get_ranking(comparison_df, 'Min_DSCR', ascending=False),
            'highest_capacity_factor': self._get_ranking(comparison_df, 'Capacity_Factor', ascending=False),
            'lowest_capex': self._get_ranking(comparison_df, 'Specific_CAPEX_EUR_kW', ascending=True)
        }
        
        # Statistics
        analysis['statistics'] = {
            'irr_project': {
                'mean': comparison_df['IRR_Project'].mean(),
                'std': comparison_df['IRR_Project'].std(),
                'min': comparison_df['IRR_Project'].min(),
                'max': comparison_df['IRR_Project'].max()
            },
            'irr_equity': {
                'mean': comparison_df['IRR_Equity'].mean(),
                'std': comparison_df['IRR_Equity'].std(),
                'min': comparison_df['IRR_Equity'].min(),
                'max': comparison_df['IRR_Equity'].max()
            },
            'lcoe': {
                'mean': comparison_df['LCOE_EUR_kWh'].mean(),
                'std': comparison_df['LCOE_EUR_kWh'].std(),
                'min': comparison_df['LCOE_EUR_kWh'].min(),
                'max': comparison_df['LCOE_EUR_kWh'].max()
            }
        }
        
        # Recommendations
        analysis['recommendations'] = self._generate_recommendations(comparison_df, valid_results)
        
        # Comparison matrix
        analysis['comparison_matrix'] = comparison_df.to_dict('records')
        
        return analysis
    
    def _get_ranking(self, df: pd.DataFrame, column: str, ascending: bool = False) -> List[Dict[str, Any]]:
        """Get ranking for a specific metric."""
        sorted_df = df.sort_values(column, ascending=ascending)
        ranking = []
        
        for i, (_, row) in enumerate(sorted_df.iterrows()):
            ranking.append({
                'rank': i + 1,
                'location': row['Location'],
                'value': row[column]
            })
        
        return ranking
    
    def _generate_recommendations(self, 
                                comparison_df: pd.DataFrame,
                                valid_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate location recommendations."""
        recommendations = {}
        
        # Best overall location (weighted score)
        weights = {
            'IRR_Project': 0.25,
            'IRR_Equity': 0.25,
            'LCOE_EUR_kWh': -0.20,  # Lower is better
            'Min_DSCR': 0.15,
            'Capacity_Factor': 0.15
        }
        
        # Normalize metrics for scoring
        normalized_df = comparison_df.copy()
        for metric in weights.keys():
            metric_min = normalized_df[metric].min()
            metric_max = normalized_df[metric].max()
            metric_range = metric_max - metric_min

            # Handle case where all values are the same (range = 0)
            if metric_range == 0 or pd.isna(metric_range):
                # If all values are the same, assign neutral score of 0.5
                normalized_df[f'{metric}_norm'] = 0.5
            else:
                if metric == 'LCOE_EUR_kWh':  # Lower is better
                    normalized_df[f'{metric}_norm'] = 1 - (normalized_df[metric] - metric_min) / metric_range
                else:  # Higher is better
                    normalized_df[f'{metric}_norm'] = (normalized_df[metric] - metric_min) / metric_range

        # Calculate weighted scores
        normalized_df['weighted_score'] = 0
        for metric, weight in weights.items():
            normalized_df['weighted_score'] += normalized_df[f'{metric}_norm'] * abs(weight)

        # Best overall location - handle case where all scores are NaN
        if normalized_df['weighted_score'].isna().all():
            # If all scores are NaN, just pick the first location
            best_overall = normalized_df.iloc[0]
        else:
            # Use skipna=True to handle any remaining NaN values
            best_overall = normalized_df.loc[normalized_df['weighted_score'].idxmax(skipna=True)]
        recommendations['best_overall'] = {
            'location': best_overall['Location'],
            'score': best_overall['weighted_score'],
            'reasons': self._get_location_strengths(best_overall['Location'], valid_results)
        }
        
        # Best for different criteria - handle NaN values
        try:
            recommendations['best_for_returns'] = comparison_df.loc[comparison_df['IRR_Equity'].idxmax(skipna=True)]['Location']
        except (ValueError, KeyError):
            recommendations['best_for_returns'] = comparison_df.iloc[0]['Location'] if not comparison_df.empty else "N/A"

        try:
            recommendations['best_for_lcoe'] = comparison_df.loc[comparison_df['LCOE_EUR_kWh'].idxmin(skipna=True)]['Location']
        except (ValueError, KeyError):
            recommendations['best_for_lcoe'] = comparison_df.iloc[0]['Location'] if not comparison_df.empty else "N/A"

        try:
            recommendations['best_for_risk'] = comparison_df.loc[comparison_df['Min_DSCR'].idxmax(skipna=True)]['Location']
        except (ValueError, KeyError):
            recommendations['best_for_risk'] = comparison_df.iloc[0]['Location'] if not comparison_df.empty else "N/A"
        
        # Risk assessment
        recommendations['risk_assessment'] = self._assess_location_risks(valid_results)
        
        return recommendations
    
    def _get_location_strengths(self, location_name: str, valid_results: Dict[str, Any]) -> List[str]:
        """Get strengths of a specific location."""
        location_data = valid_results.get(location_name, {})
        summary = location_data.get('summary', {})
        
        strengths = []
        
        if summary.get('irr_project', 0) > 0.12:
            strengths.append("Strong project returns")
        
        if summary.get('lcoe_eur_kwh', 1) < 0.045:
            strengths.append("Competitive LCOE")
        
        if summary.get('capacity_factor', 0) > 0.25:
            strengths.append("High capacity factor")
        
        if summary.get('min_dscr', 0) > 1.35:
            strengths.append("Strong debt service coverage")
        
        # Add location-specific advantages
        advantages = summary.get('advantages', [])
        strengths.extend(advantages[:2])  # Add top 2 advantages
        
        return strengths
    
    def _assess_location_risks(self, valid_results: Dict[str, Any]) -> Dict[str, List[str]]:
        """Assess risks for each location."""
        risk_assessment = {}
        
        for location_name, data in valid_results.items():
            summary = data['summary']
            risks = []
            
            if summary.get('irr_project', 0) < 0.10:
                risks.append("Low project returns")
            
            if summary.get('min_dscr', 0) < 1.25:
                risks.append("Tight debt service coverage")
            
            if summary.get('capacity_factor', 0) < 0.20:
                risks.append("Low capacity factor")
            
            # Add location-specific challenges
            challenges = summary.get('challenges', [])
            risks.extend(challenges[:2])  # Add top 2 challenges
            
            risk_assessment[location_name] = risks
        
        return risk_assessment
    
    def get_comparison_results(self) -> Optional[Dict[str, Any]]:
        """Get current comparison results."""
        return self._comparison_results
    
    def has_comparison_results(self) -> bool:
        """Check if comparison has been performed."""
        return self._comparison_results is not None
    
    def get_comparable_locations(self, project_location_id: str) -> List[LocationConfig]:
        """Return a curated, filterable list of candidate locations.
        
        Args:
            project_location_id: The base location ID to find comparable locations for
            
        Returns:
            List of LocationConfig objects that are comparable to the base location
            
        Raises:
            LocationServiceError: If the base location is not found
        """
        base_location = self.location_manager.get_location(project_location_id)
        if base_location is None:
            self.logger.error(f"Base location '{project_location_id}' not found")
            raise LocationServiceError(f"Base location '{project_location_id}' not found")
        
        comparable_locations = []
        all_locations = self.location_manager.get_all_locations()
        
        # Define similarity thresholds
        irradiation_threshold = 200  # kWh/m2
        production_threshold = 1000  # MWh
        
        for loc_id, loc_config in all_locations.items():
            if loc_id == project_location_id:
                continue  # Skip the base location itself
                
            # Check if locations are in similar climate zones (irradiation similarity)
            irradiation_diff = abs(base_location.irradiation_kwh_m2 - loc_config.irradiation_kwh_m2)
            production_diff = abs(base_location.production_mwh_year1 - loc_config.production_mwh_year1)
            
            if irradiation_diff <= irradiation_threshold and production_diff <= production_threshold:
                comparable_locations.append(loc_config)
        
        self.logger.info(f"Found {len(comparable_locations)} comparable locations for {project_location_id}")
        return comparable_locations
    
    def compare_locations_kpi(self, base_id: str, target_ids: List[str]) -> Dict[str, Any]:
        """Compare base location with target locations using KPI bundle.
        
        Args:
            base_id: The base location ID for comparison
            target_ids: List of target location IDs to compare against
            
        Returns:
            Dictionary containing KPI comparison data suitable for radar chart
            
        Raises:
            LocationServiceError: If base location or any target location is not found
        """
        base_location = self.location_manager.get_location(base_id)
        if not base_location:
            self.logger.error(f"Base location '{base_id}' not found")
            raise LocationServiceError(f"Base location '{base_id}' not found")
        
        # Validate all target locations exist
        target_locations = {}
        for target_id in target_ids:
            target_location = self.location_manager.get_location(target_id)
            if not target_location:
                self.logger.error(f"Target location '{target_id}' not found")
                raise LocationServiceError(f"Target location '{target_id}' not found")
            target_locations[target_id] = target_location
        
        # Calculate KPI comparisons
        comparison_results = {
            'base_location': {
                'id': base_id,
                'name': base_location.name,
                'kpis': self._calculate_location_kpis(base_location)
            },
            'target_locations': {},
            'comparison_matrix': [],
            'radar_chart_data': self._prepare_radar_chart_data(base_location, target_locations)
        }
        
        # Calculate KPIs for each target location
        for target_id, target_location in target_locations.items():
            target_kpis = self._calculate_location_kpis(target_location)
            comparison_results['target_locations'][target_id] = {
                'id': target_id,
                'name': target_location.name,
                'kpis': target_kpis
            }
            
            # Calculate differences for comparison matrix
            comparison_results['comparison_matrix'].append({
                'location': target_location.name,
                'irradiation_difference': target_location.irradiation_kwh_m2 - base_location.irradiation_kwh_m2,
                'production_difference': target_location.production_mwh_year1 - base_location.production_mwh_year1,
                'capex_difference': target_location.capex_meur - base_location.capex_meur,
                'opex_difference': target_location.opex_keuros_year1 - base_location.opex_keuros_year1,
                'ppa_difference': target_location.ppa_price_eur_kwh - base_location.ppa_price_eur_kwh,
                'lcoe_difference': target_kpis['lcoe_eur_kwh'] - comparison_results['base_location']['kpis']['lcoe_eur_kwh']
            })
        
        self.logger.info(f"Completed KPI comparison for {base_id} vs {len(target_ids)} targets")
        return comparison_results
    
    def _calculate_location_kpis(self, location: LocationConfig) -> Dict[str, Any]:
        """Calculate key performance indicators for a location."""
        return {
            'irradiation_kwh_m2': location.irradiation_kwh_m2,
            'production_mwh_year1': location.production_mwh_year1,
            'capex_meur': location.capex_meur,
            'opex_keuros_year1': location.opex_keuros_year1,
            'ppa_price_eur_kwh': location.ppa_price_eur_kwh,
            'lcoe_eur_kwh': location.get_lcoe_estimate(),
            'land_lease_eur_mw_year': location.land_lease_eur_mw_year,
            'environmental_impact_score': location.environmental_impact_score,
            'regulatory_complexity_score': location.regulatory_complexity_score,
            'permitting_time_months': location.permitting_time_months
        }
    
    def _prepare_radar_chart_data(self, base_location: LocationConfig, target_locations: Dict[str, LocationConfig]) -> Dict[str, Any]:
        """Prepare data structure for radar chart visualization."""
        # Define KPI categories for radar chart
        kpi_categories = [
            {'name': 'Solar Resource', 'key': 'irradiation_kwh_m2', 'higher_is_better': True},
            {'name': 'Production', 'key': 'production_mwh_year1', 'higher_is_better': True},
            {'name': 'CAPEX', 'key': 'capex_meur', 'higher_is_better': False},
            {'name': 'OPEX', 'key': 'opex_keuros_year1', 'higher_is_better': False},
            {'name': 'PPA Price', 'key': 'ppa_price_eur_kwh', 'higher_is_better': False},
            {'name': 'Environmental', 'key': 'environmental_impact_score', 'higher_is_better': True},
            {'name': 'Regulatory', 'key': 'regulatory_complexity_score', 'higher_is_better': False}
        ]
        
        # Collect all values for normalization
        all_locations = {base_location.name: base_location}
        all_locations.update(target_locations)
        
        # Calculate min/max for each KPI for normalization
        kpi_ranges = {}
        for category in kpi_categories:
            key = category['key']
            values = [getattr(loc, key) for loc in all_locations.values()]
            kpi_ranges[key] = {'min': min(values), 'max': max(values)}
        
        # Normalize values to 0-100 scale
        radar_data = {
            'categories': [cat['name'] for cat in kpi_categories],
            'locations': []
        }
        
        for loc_name, location in all_locations.items():
            normalized_values = []
            for category in kpi_categories:
                key = category['key']
                value = getattr(location, key)
                min_val = kpi_ranges[key]['min']
                max_val = kpi_ranges[key]['max']
                
                # Normalize to 0-100 scale
                if max_val == min_val:
                    normalized_value = 50  # Neutral value when all locations have same value
                else:
                    normalized_value = ((value - min_val) / (max_val - min_val)) * 100
                    
                # Invert for metrics where lower is better
                if not category['higher_is_better']:
                    normalized_value = 100 - normalized_value
                
                normalized_values.append(normalized_value)
            
            radar_data['locations'].append({
                'name': loc_name,
                'values': normalized_values,
                'is_base': loc_name == base_location.name
            })
        
        return radar_data
    
    def clear_results(self):
        """Clear comparison results."""
        self._comparison_results = None
        self.logger.info("Location comparison results cleared")
    
    def get_available_locations(self) -> List[str]:
        """Get list of available locations."""
        return self.location_manager.get_location_names()
    
    def add_custom_location(self, location_config: LocationConfig):
        """Add a custom location configuration."""
        self.location_manager.add_location(location_config)
        self.logger.info(f"Added custom location: {location_config.name}")
    
    def export_comparison_results(self) -> Optional[pd.DataFrame]:
        """Export comparison results as DataFrame."""
        if not self._comparison_results:
            return None
        
        analysis = self._comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if comparison_matrix:
            return pd.DataFrame(comparison_matrix)
        
        return None
