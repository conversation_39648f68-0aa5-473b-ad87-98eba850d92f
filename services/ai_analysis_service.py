"""
AI-Powered Analysis Service
===========================

Advanced AI analysis service with configurable LLM providers for generating
professional insights, narratives, and recommendations for financial reports.
"""

import json
import logging
import asyncio
import hashlib
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import pandas as pd
import numpy as np

# Import for secure API calls
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Import for data protection
from cryptography.fernet import Fernet
import base64
import os


@dataclass
class LLMConfig:
    """Configuration for LLM providers."""
    provider: str  # 'openai', 'anthropic', 'custom', 'local'
    api_url: str
    api_key: str
    model_id: str
    system_prompt: str
    max_tokens: int = 4000
    temperature: float = 0.3
    timeout: int = 30
    rate_limit: int = 10  # requests per minute
    data_retention_days: int = 0  # 0 = no retention
    enable_caching: bool = True
    privacy_mode: bool = True  # anonymize sensitive data


@dataclass
class AnalysisRequest:
    """Request structure for AI analysis."""
    analysis_type: str
    data: Dict[str, Any]
    context: Dict[str, Any]
    output_format: str = "structured"  # structured, narrative, executive
    include_charts: bool = True
    include_recommendations: bool = True
    anonymize_data: bool = True


@dataclass
class AnalysisResult:
    """Result structure for AI analysis."""
    insights: Dict[str, Any]
    narrative: str
    recommendations: List[str]
    charts_analysis: Dict[str, str]
    risk_assessment: Dict[str, Any]
    confidence_score: float
    processing_time: float
    model_used: str
    timestamp: datetime


class DataProtectionManager:
    """Handles data protection and anonymization with comprehensive security measures."""
    
    def __init__(self, encryption_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Initialize encryption
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            key = Fernet.generate_key()
            self.cipher = Fernet(key)
            self.logger.info("Generated new encryption key for data protection")
        
        # PII detection patterns
        self.pii_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            'ssn': r'\b\d{3}-\d{2}-\d{4}\b',
            'credit_card': r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',
            'iban': r'\b[A-Z]{2}\d{2}\s?[A-Z0-9]{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b',
            'ip_address': r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        }
        
        # Sensitive field names
        self.sensitive_fields = {
            'password', 'secret', 'key', 'token', 'private', 'confidential',
            'personal_id', 'tax_id', 'social_security', 'bank_account',
            'credit_card', 'pin', 'access_code', 'license_key'
        }
        
        # Financial data anonymization thresholds
        self.financial_thresholds = {
            'high_sensitivity': 1000000,  # 1M+
            'medium_sensitivity': 100000,  # 100K+
            'low_sensitivity': 10000      # 10K+
        }
    
    def anonymize_financial_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Anonymize sensitive financial data while preserving analytical value."""
        anonymized = self._deep_copy_dict(data)
        
        # Comprehensive PII detection and removal
        anonymized = self._detect_and_remove_pii(anonymized)
        
        # Anonymize client information
        if 'client_profile' in anonymized:
            client = anonymized['client_profile']
            client['company_name'] = self._anonymize_text(client.get('company_name', ''))
            client['client_name'] = self._anonymize_text(client.get('client_name', ''))
            client['project_name'] = f"Project_{self._generate_hash(client.get('project_name', ''))[:8]}"
            client['project_location'] = self._anonymize_location(client.get('project_location', ''))
            
            # Remove or anonymize contact information
            if 'contact_email' in client:
                client['contact_email'] = self._anonymize_email(client['contact_email'])
            if 'contact_phone' in client:
                client['contact_phone'] = self._anonymize_phone(client['contact_phone'])
        
        # Enhanced financial data anonymization
        if 'financial_results' in anonymized:
            financial = anonymized['financial_results']
            if 'kpis' in financial:
                kpis = financial['kpis']
                # Categorize and anonymize based on sensitivity
                for key, value in kpis.items():
                    if isinstance(value, (int, float)):
                        sensitivity = self._assess_financial_sensitivity(value)
                        if sensitivity == 'high':
                            # High sensitivity: use normalized ranges
                            kpis[key] = self._normalize_to_range(value, 'high')
                        elif sensitivity == 'medium':
                            # Medium sensitivity: use bucketed values
                            kpis[key] = self._bucket_value(value, 'medium')
                        # Low sensitivity values preserved for analysis
        
        # Remove sensitive metadata
        anonymized = self._remove_sensitive_metadata(anonymized)
        
        # Add anonymization metadata
        anonymized['_anonymization_info'] = {
            'timestamp': datetime.now().isoformat(),
            'version': '2.0',
            'method': 'comprehensive_anonymization',
            'pii_removed': True,
            'financial_data_protected': True
        }
        
        return anonymized
    
    def _anonymize_text(self, text: str) -> str:
        """Anonymize text while preserving length and structure."""
        if not text:
            return ""
        
        # Keep first and last character, anonymize middle
        if len(text) <= 2:
            return "*" * len(text)
        
        return text[0] + "*" * (len(text) - 2) + text[-1]
    
    def _anonymize_location(self, location: str) -> str:
        """Anonymize location while preserving regional characteristics."""
        location_map = {
            'morocco': 'North Africa Region',
            'germany': 'Central Europe Region',
            'france': 'Western Europe Region',
            'spain': 'Southern Europe Region',
            'italy': 'Southern Europe Region'
        }
        
        location_lower = location.lower()
        for key, value in location_map.items():
            if key in location_lower:
                return value
        
        return "Undisclosed Region"
    
    def _normalize_value(self, value: Union[int, float]) -> float:
        """Normalize financial values to 0-100 scale."""
        if value == 0:
            return 0
        
        # Use log scale for large values
        if abs(value) > 1000000:
            return min(100, max(0, (np.log10(abs(value)) - 5) * 20))
        
        return min(100, max(0, abs(value) / 10000))
    
    def _generate_hash(self, text: str) -> str:
        """Generate consistent hash for anonymization."""
        return hashlib.sha256(text.encode()).hexdigest()
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        return base64.b64encode(self.cipher.encrypt(data.encode())).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        return self.cipher.decrypt(base64.b64decode(encrypted_data)).decode()
    
    def _deep_copy_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a deep copy of dictionary data."""
        import copy
        return copy.deepcopy(data)
    
    def _detect_and_remove_pii(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect and remove personally identifiable information."""
        import re
        
        def clean_value(value):
            if isinstance(value, str) and value is not None:
                # Check for PII patterns
                for pii_type, pattern in self.pii_patterns.items():
                    if re.search(pattern, value):
                        self.logger.warning(f"PII detected and removed: {pii_type}")
                        value = re.sub(pattern, f"[REDACTED_{pii_type.upper()}]", value)
                return value
            elif isinstance(value, dict) and value is not None:
                return {k: clean_value(v) for k, v in value.items()}
            elif isinstance(value, list) and value is not None:
                return [clean_value(item) for item in value]
            elif value is None:
                return ""  # Convert None to empty string
            return value
        
        return clean_value(data)
    
    def _anonymize_email(self, email: str) -> str:
        """Anonymize email address."""
        if '@' in email:
            local, domain = email.split('@', 1)
            return f"{local[:1]}***@{domain}"
        return "[REDACTED_EMAIL]"
    
    def _anonymize_phone(self, phone: str) -> str:
        """Anonymize phone number."""
        # Keep only the last 4 digits
        digits = ''.join(filter(str.isdigit, phone))
        if len(digits) >= 4:
            return f"***-***-{digits[-4:]}"
        return "[REDACTED_PHONE]"
    
    def _assess_financial_sensitivity(self, value: float) -> str:
        """Assess financial data sensitivity level."""
        abs_value = abs(value)
        
        if abs_value >= self.financial_thresholds['high_sensitivity']:
            return 'high'
        elif abs_value >= self.financial_thresholds['medium_sensitivity']:
            return 'medium'
        else:
            return 'low'
    
    def _normalize_to_range(self, value: float, sensitivity: str) -> str:
        """Normalize sensitive values to ranges."""
        abs_value = abs(value)
        
        if sensitivity == 'high':
            if abs_value >= 10000000:  # 10M+
                return "Very High (>10M)"
            elif abs_value >= 5000000:  # 5M+
                return "High (5M-10M)"
            elif abs_value >= 1000000:  # 1M+
                return "Medium-High (1M-5M)"
            else:
                return "Medium (100K-1M)"
        
        return f"Range: {sensitivity}"
    
    def _bucket_value(self, value: float, sensitivity: str) -> str:
        """Bucket values into ranges."""
        abs_value = abs(value)
        
        if sensitivity == 'medium':
            if abs_value >= 500000:
                return "500K+"
            elif abs_value >= 100000:
                return "100K-500K"
            elif abs_value >= 50000:
                return "50K-100K"
            else:
                return "10K-50K"
        
        return f"Bucket: {sensitivity}"
    
    def _remove_sensitive_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sensitive metadata fields."""
        sensitive_keys = set()
        
        def find_sensitive_keys(obj, prefix=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    full_key = f"{prefix}.{key}" if prefix else key
                    
                    # Check if key name suggests sensitive data
                    if any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                        sensitive_keys.add(full_key)
                    
                    # Check if value looks like sensitive data
                    if isinstance(value, str) and len(value) > 0:
                        for pattern in self.pii_patterns.values():
                            if re.search(pattern, value):
                                sensitive_keys.add(full_key)
                                break
                    
                    # Recursively check nested objects
                    if isinstance(value, (dict, list)):
                        find_sensitive_keys(value, full_key)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    find_sensitive_keys(item, f"{prefix}[{i}]")
        
        find_sensitive_keys(data)
        
        # Remove sensitive keys
        for key_path in sensitive_keys:
            self._remove_nested_key(data, key_path)
        
        return data
    
    def _remove_nested_key(self, data: Dict[str, Any], key_path: str):
        """Remove nested key from dictionary."""
        try:
            keys = key_path.split('.')
            current = data
            
            for key in keys[:-1]:
                if '[' in key and ']' in key:
                    # Handle list indices
                    list_key, index = key.split('[')
                    index = int(index.rstrip(']'))
                    current = current[list_key][index]
                else:
                    current = current[key]
            
            final_key = keys[-1]
            if '[' in final_key and ']' in final_key:
                list_key, index = final_key.split('[')
                index = int(index.rstrip(']'))
                if list_key in current and index < len(current[list_key]):
                    current[list_key][index] = "[REDACTED]"
            else:
                if final_key in current:
                    current[final_key] = "[REDACTED]"
        except (KeyError, IndexError, ValueError):
            # Key path not found, ignore
            pass


class LLMProvider:
    """Base class for LLM providers."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Setup HTTP session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    async def generate_analysis(self, request: AnalysisRequest) -> AnalysisResult:
        """Generate AI analysis based on request."""
        raise NotImplementedError("Subclasses must implement generate_analysis")
    
    def _prepare_prompt(self, request: AnalysisRequest) -> str:
        """Prepare prompt for LLM."""
        system_prompt = self.config.system_prompt
        
        # Add context about analysis type
        context = f"""
Analysis Type: {request.analysis_type}
Output Format: {request.output_format}
Include Charts Analysis: {request.include_charts}
Include Recommendations: {request.include_recommendations}

Financial Data Summary:
{self._format_financial_data(request.data)}

Context Information:
{json.dumps(request.context, indent=2)}
"""
        
        return f"{system_prompt}\n\n{context}"
    
    def _format_financial_data(self, data: Dict[str, Any]) -> str:
        """Format financial data for prompt."""
        formatted = []
        
        if 'financial_results' in data:
            kpis = data['financial_results'].get('kpis', {})
            formatted.append("Key Performance Indicators:")
            for key, value in kpis.items():
                if isinstance(value, (int, float)):
                    if 'IRR' in key or 'rate' in key.lower():
                        formatted.append(f"  {key}: {value:.2%}")
                    elif 'NPV' in key or 'value' in key.lower():
                        formatted.append(f"  {key}: €{value:,.0f}")
                    else:
                        formatted.append(f"  {key}: {value:.2f}")
        
        if 'scenarios' in data:
            formatted.append("\nScenario Analysis Results:")
            for scenario, results in data['scenarios'].items():
                formatted.append(f"  {scenario}: {results}")
        
        return "\n".join(formatted)


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider."""
    
    async def generate_analysis(self, request: AnalysisRequest) -> AnalysisResult:
        """Generate analysis using OpenAI API."""
        start_time = datetime.now()
        
        try:
            prompt = self._prepare_prompt(request)
            
            payload = {
                "model": self.config.model_id,
                "messages": [
                    {"role": "system", "content": self.config.system_prompt},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            response = self.session.post(
                self.config.api_url,
                json=payload,
                headers=headers,
                timeout=self.config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Parse response
            analysis_text = result['choices'][0]['message']['content']
            
            return self._parse_analysis_response(
                analysis_text, 
                start_time, 
                self.config.model_id
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI analysis failed: {str(e)}")
            return AnalysisResult(
                insights={'error': 'AI analysis unavailable—see log'},
                narrative="AI analysis unavailable—see log",
                recommendations=["Check configuration logs"],
                charts_analysis={},
                risk_assessment={},
                confidence_score=0.0,
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used="fallback",
                timestamp=datetime.now()
            )


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider."""
    
    async def generate_analysis(self, request: AnalysisRequest) -> AnalysisResult:
        """Generate analysis using Anthropic API."""
        start_time = datetime.now()
        
        try:
            prompt = self._prepare_prompt(request)
            
            payload = {
                "model": self.config.model_id,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            headers = {
                "x-api-key": self.config.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            response = self.session.post(
                self.config.api_url,
                json=payload,
                headers=headers,
                timeout=self.config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Parse response
            analysis_text = result['content'][0]['text']
            
            return self._parse_analysis_response(
                analysis_text, 
                start_time, 
                self.config.model_id
            )
            
        except Exception as e:
            self.logger.error(f"Anthropic analysis failed: {str(e)}")
            return self._create_fallback_analysis(start_time, str(e))


class CustomProvider(LLMProvider):
    """Custom LLM provider for proprietary models."""
    
    async def generate_analysis(self, request: AnalysisRequest) -> AnalysisResult:
        """Generate analysis using custom API."""
        start_time = datetime.now()
        
        try:
            prompt = self._prepare_prompt(request)
            
            # Custom payload format - adaptable
            payload = {
                "prompt": prompt,
                "model": self.config.model_id,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            response = self.session.post(
                self.config.api_url,
                json=payload,
                headers=headers,
                timeout=self.config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Parse response (adapt based on API format)
            analysis_text = result.get('response', result.get('text', ''))
            
            return self._parse_analysis_response(
                analysis_text, 
                start_time, 
                self.config.model_id
            )
            
        except Exception as e:
            self.logger.error(f"Custom provider analysis failed: {str(e)}")
            return self._create_fallback_analysis(start_time, str(e))


class LocalProvider(LLMProvider):
    """Local LLM provider (e.g., Ollama, local models)."""
    
    async def generate_analysis(self, request: AnalysisRequest) -> AnalysisResult:
        """Generate analysis using local model."""
        start_time = datetime.now()
        
        try:
            prompt = self._prepare_prompt(request)
            
            # Local model payload
            payload = {
                "model": self.config.model_id,
                "prompt": prompt,
                "options": {
                    "temperature": self.config.temperature,
                    "num_predict": self.config.max_tokens
                },
                "stream": False
            }
            
            response = self.session.post(
                self.config.api_url,
                json=payload,
                timeout=self.config.timeout
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Parse response
            analysis_text = result.get('response', '')
            
            return self._parse_analysis_response(
                analysis_text, 
                start_time, 
                self.config.model_id
            )
            
        except Exception as e:
            self.logger.error(f"Local provider analysis failed: {str(e)}")
            return self._create_fallback_analysis(start_time, str(e))


class AIAnalysisService:
    """Main AI analysis service with multi-provider support."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.data_protection = DataProtectionManager()
        
        # Initialize provider
        self.provider = self._create_provider(config)
        
        # Analysis cache
        self.analysis_cache = {}
        
        # Initialize helper methods
        from .ai_analysis_helper_methods import AIAnalysisHelperMethods
        self.helper_methods = AIAnalysisHelperMethods(self)
        
        # Default system prompts
        self.default_prompts = {
            'financial_analysis': """
You are a professional financial analyst specializing in renewable energy projects.
Analyze the provided financial data and generate comprehensive insights.

Your analysis should include:
1. Executive summary of financial performance
2. Key strengths and weaknesses
3. Risk assessment with specific recommendations
4. Market positioning analysis
5. Investment recommendation with rationale
6. Strategic next steps

Format your response as structured JSON with the following sections:
- executive_summary
- financial_performance
- risk_assessment
- recommendations
- market_analysis
- confidence_score (0-100)

Be precise, professional, and focus on actionable insights.
Avoid speculation and base all conclusions on the provided data.
""",
            'chart_analysis': """
You are a data visualization expert analyzing financial charts.
Provide professional interpretation of chart patterns and trends.

Your analysis should identify:
1. Key trends and patterns
2. Anomalies or outliers
3. Correlation insights
4. Predictive indicators
5. Risk signals

Format as structured analysis with clear, actionable insights.
""",
            'risk_assessment': """
You are a risk management specialist for renewable energy investments.
Analyze the provided data for potential risks and mitigation strategies.

Focus on:
1. Financial risks (IRR, NPV, DSCR sensitivity)
2. Market risks (pricing, competition, regulation)
3. Technical risks (technology, performance, maintenance)
4. Project risks (construction, permits, timeline)
5. Mitigation strategies for each risk category

Provide risk scoring (1-10) and specific recommendations.
"""
        }
    
    def _create_provider(self, config: LLMConfig) -> LLMProvider:
        """Create appropriate LLM provider based on configuration."""
        providers = {
            'openai': OpenAIProvider,
            'anthropic': AnthropicProvider,
            'custom': CustomProvider,
            'local': LocalProvider
        }
        
        provider_class = providers.get(config.provider)
        if not provider_class:
            raise ValueError(f"Unsupported provider: {config.provider}")
        
        return provider_class(config)
    
    def _create_analysis_request_from_prompt(self, prompt: str, analysis_type: str) -> AnalysisRequest:
        """Create analysis request from prompt for helper methods."""
        return AnalysisRequest(
            data={'prompt': prompt},
            analysis_type=analysis_type,
            output_format="structured",
            include_charts=False,
            include_recommendations=True,
            context_level="detailed"
        )
    
    async def analyze_comprehensive_data(self, 
                                       analysis_data: Dict[str, Any],
                                       include_charts: bool = True,
                                       include_project_context: bool = True,
                                       detailed_sections: bool = True) -> Dict[str, Any]:
        """
        Comprehensive AI analysis with full context, charts, and detailed sections.
        
        Args:
            analysis_data: Complete project data including client profile, assumptions, 
                          financial results, and charts
            include_charts: Whether to include chart analysis
            include_project_context: Whether to include project-specific context
            detailed_sections: Whether to include detailed intro, conclusion, and recommendations
        
        Returns:
            Comprehensive analysis with all requested sections
        """
        
        try:
            self.logger.info("Starting comprehensive AI analysis with enhanced security")
            
            # Protect sensitive data before analysis
            protected_data = self.data_protection.anonymize_financial_data(analysis_data)
            
            # Create comprehensive analysis request
            analysis_request = AnalysisRequest(
                data=protected_data,
                analysis_type="comprehensive_with_context",
                output_format="structured_json",
                include_charts=include_charts,
                include_recommendations=True,
                context_level="detailed"
            )
            
            # Generate base analysis
            base_analysis = await self.provider.generate_analysis(analysis_request)
            
            # Structure comprehensive response
            comprehensive_result = {
                # Executive Summary
                'executive_summary': await self.helper_methods._generate_executive_summary(protected_data),
                
                # Detailed Introduction
                'introduction': await self.helper_methods._generate_detailed_introduction(protected_data) if detailed_sections else None,
                
                # Financial Performance Analysis
                'financial_performance': await self.helper_methods._analyze_financial_performance(protected_data),
                
                # Project Context Analysis
                'project_context': await self.helper_methods._analyze_project_context(protected_data) if include_project_context else None,
                
                # Chart Analysis
                'chart_analysis': await self.helper_methods._analyze_charts_comprehensive(protected_data) if include_charts else None,
                
                # Risk Assessment
                'risk_assessment': await self.helper_methods._generate_risk_assessment(protected_data),
                
                # Market Analysis
                'market_analysis': await self.helper_methods._analyze_market_position(protected_data),
                
                # Technology Assessment
                'technology_assessment': await self.helper_methods._assess_technology_factors(protected_data),
                
                # Financing Analysis
                'financing_analysis': await self.helper_methods._analyze_financing_structure(protected_data),
                
                # Recommendations
                'recommendations': await self.helper_methods._generate_detailed_recommendations(protected_data),
                
                # Detailed Conclusion
                'conclusion': await self.helper_methods._generate_detailed_conclusion(protected_data) if detailed_sections else None,
                
                # Metadata
                'analysis_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'model_used': self.config.model_id,
                    'provider': self.config.provider,
                    'data_protected': True,
                    'analysis_type': 'comprehensive_with_context',
                    'confidence_score': base_analysis.confidence_score,
                    'processing_time': base_analysis.processing_time
                }
            }
            
            return comprehensive_result
            
        except Exception as e:
            self.logger.error(f"Comprehensive analysis failed: {str(e)}")
            return {
                'error': str(e),
                'executive_summary': 'Analysis could not be completed due to technical issues.',
                'financial_insights': 'Please check the system configuration and try again.',
                'recommendations': ['Review AI service configuration', 'Check data quality', 'Verify network connectivity'],
                'analysis_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'status': 'failed',
                    'error': str(e)
                }
            }
    
    async def analyze_financial_data(self, 
                                   financial_results: Dict[str, Any],
                                   client_profile: Dict[str, Any],
                                   assumptions: Dict[str, Any],
                                   analysis_type: str = "comprehensive") -> AnalysisResult:
        """Analyze financial data with AI insights."""
        
        # Prepare analysis request
        data = {
            'financial_results': financial_results,
            'client_profile': client_profile,
            'assumptions': assumptions
        }
        
        # Anonymize data if privacy mode is enabled
        if self.config.privacy_mode:
            data = self.data_protection.anonymize_financial_data(data)
        
        # Create analysis request
        request = AnalysisRequest(
            analysis_type=analysis_type,
            data=data,
            context={
                'timestamp': datetime.now().isoformat(),
                'analysis_version': '2.0',
                'privacy_mode': self.config.privacy_mode
            },
            output_format="structured",
            include_charts=True,
            include_recommendations=True,
            anonymize_data=self.config.privacy_mode
        )
        
        # Check cache
        cache_key = self._generate_cache_key(request)
        if self.config.enable_caching and cache_key in self.analysis_cache:
            self.logger.info("Returning cached analysis result")
            return self.analysis_cache[cache_key]
        
        # Generate analysis
        try:
            result = await self.provider.generate_analysis(request)
            
            # Cache result
            if self.config.enable_caching:
                self.analysis_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"AI analysis failed: {str(e)}")
            return self._create_fallback_analysis(datetime.now(), str(e))
    
    async def analyze_charts(self, 
                           charts_data: Dict[str, Any],
                           chart_types: List[str]) -> Dict[str, str]:
        """Analyze charts and generate narrative insights."""
        
        insights = {}
        
        for chart_type in chart_types:
            if chart_type not in charts_data:
                continue
            
            request = AnalysisRequest(
                analysis_type="chart_analysis",
                data={'chart_type': chart_type, 'data': charts_data[chart_type]},
                context={'chart_type': chart_type},
                output_format="narrative",
                include_charts=True,
                include_recommendations=False
            )
            
            try:
                result = await self.provider.generate_analysis(request)
                insights[chart_type] = result.narrative
                
            except Exception as e:
                self.logger.error(f"Chart analysis failed for {chart_type}: {str(e)}")
                insights[chart_type] = f"Analysis unavailable: {str(e)}"
        
        return insights
    
    async def generate_executive_summary(self, 
                                       analysis_results: Dict[str, Any]) -> str:
        """Generate AI-powered executive summary."""
        
        request = AnalysisRequest(
            analysis_type="executive_summary",
            data=analysis_results,
            context={'report_type': 'executive_summary'},
            output_format="executive",
            include_charts=False,
            include_recommendations=True
        )
        
        try:
            result = await self.provider.generate_analysis(request)
            return result.narrative
            
        except Exception as e:
            self.logger.error(f"Executive summary generation failed: {str(e)}")
            return "Executive summary generation unavailable due to technical issues."
    
    def _generate_cache_key(self, request: AnalysisRequest) -> str:
        """Generate cache key for analysis request."""
        key_data = {
            'type': request.analysis_type,
            'data_hash': hashlib.sha256(json.dumps(request.data, sort_keys=True).encode()).hexdigest(),
            'format': request.output_format
        }
        return hashlib.sha256(json.dumps(key_data, sort_keys=True).encode()).hexdigest()
    
    def _create_fallback_analysis(self, start_time: datetime, error: str) -> AnalysisResult:
        """Create fallback analysis result when AI fails."""
        return AnalysisResult(
            insights={'error': error, 'fallback': True},
            narrative="AI analysis temporarily unavailable. Please refer to quantitative metrics.",
            recommendations=[
                "Review financial KPIs manually",
                "Consult with financial advisor",
                "Check system configuration"
            ],
            charts_analysis={},
            risk_assessment={'status': 'unavailable', 'reason': error},
            confidence_score=0.0,
            processing_time=(datetime.now() - start_time).total_seconds(),
            model_used="fallback",
            timestamp=datetime.now()
        )
    
    def _parse_analysis_response(self, 
                               analysis_text: str, 
                               start_time: datetime, 
                               model_id: str) -> AnalysisResult:
        """Parse AI response into structured result."""
        try:
            # Try to parse as JSON first
            if analysis_text.strip().startswith('{'):
                parsed = json.loads(analysis_text)
                return AnalysisResult(
                    insights=parsed.get('insights', {}),
                    narrative=parsed.get('narrative', analysis_text),
                    recommendations=parsed.get('recommendations', []),
                    charts_analysis=parsed.get('charts_analysis', {}),
                    risk_assessment=parsed.get('risk_assessment', {}),
                    confidence_score=parsed.get('confidence_score', 85.0),
                    processing_time=(datetime.now() - start_time).total_seconds(),
                    model_used=model_id,
                    timestamp=datetime.now()
                )
            else:
                # Parse as narrative text
                return AnalysisResult(
                    insights={'narrative_analysis': True},
                    narrative=analysis_text,
                    recommendations=self._extract_recommendations(analysis_text),
                    charts_analysis={},
                    risk_assessment={'status': 'analyzed', 'text': analysis_text},
                    confidence_score=80.0,
                    processing_time=(datetime.now() - start_time).total_seconds(),
                    model_used=model_id,
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            self.logger.error(f"Failed to parse analysis response: {str(e)}")
            return self._create_fallback_analysis(start_time, str(e))
    
    def _extract_recommendations(self, text: str) -> List[str]:
        """Extract recommendations from narrative text."""
        recommendations = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if (line.startswith('•') or line.startswith('-') or 
                line.startswith('*') or 'recommend' in line.lower()):
                recommendations.append(line.lstrip('•-* '))
        
        return recommendations[:10]  # Limit to top 10 recommendations
    
    def update_config(self, new_config: LLMConfig):
        """Update LLM configuration."""
        self.config = new_config
        self.provider = self._create_provider(new_config)
        self.analysis_cache.clear()  # Clear cache on config change
        
        self.logger.info(f"Updated AI configuration for provider: {new_config.provider}")
    
    def get_provider_status(self) -> Dict[str, Any]:
        """Get current provider status and capabilities."""
        return {
            'provider': self.config.provider,
            'model_id': self.config.model_id,
            'status': 'active',
            'privacy_mode': self.config.privacy_mode,
            'cache_enabled': self.config.enable_caching,
            'cache_size': len(self.analysis_cache),
            'last_update': datetime.now().isoformat()
        }
    
    def clear_cache(self):
        """Clear analysis cache."""
        self.analysis_cache.clear()
        self.logger.info("Analysis cache cleared")
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test connection to AI provider."""
        try:
            test_request = AnalysisRequest(
                analysis_type="test",
                data={'test': True},
                context={'test': True},
                output_format="structured"
            )
            
            # Simple test prompt
            original_prompt = self.config.system_prompt
            self.config.system_prompt = "Respond with 'Connection successful' if you can read this."
            
            result = await self.provider.generate_analysis(test_request)
            
            # Restore original prompt
            self.config.system_prompt = original_prompt
            
            return {
                'status': 'success',
                'provider': self.config.provider,
                'model': self.config.model_id,
                'response_time': result.processing_time,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'provider': self.config.provider,
                'timestamp': datetime.now().isoformat()
            }