"""
Project Service
===============

Service for managing projects with async location comparison support.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from services.persistence_service import DataPersistenceService, ProjectData
from models.project_analytics import ProjectAnalytics
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


logger = logging.getLogger(__name__)


class ProjectService:
    """Service for managing projects with async processing capabilities."""
    
    def __init__(self, persistence_service: Optional[DataPersistenceService] = None):
        self.logger = logging.getLogger(__name__)
        self.persistence_service = persistence_service or DataPersistenceService()
        
        # Try to import Celery for task queueing
        try:
            from celery_app import celery_app
            from tasks.location_comparison import run_location_comparison
            self.celery_app = celery_app
            self.run_location_comparison = run_location_comparison
            self.celery_available = True
            self.logger.info("Celery integration available")
        except ImportError:
            self.celery_app = None
            self.run_location_comparison = None
            self.celery_available = False
            self.logger.warning("Celery not available - async tasks disabled")
    
    def create_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new project and optionally trigger location comparison."""
        try:
            # Extract project components
            project_id = project_data.get('project_id')
            if not project_id:
                raise ValueError("Project ID is required")
            
            client_profile = project_data.get('client_profile', {})
            project_assumptions = project_data.get('project_assumptions', {})
            comparison_locations = project_data.get('comparison_locations', [])
            
            # Create project data object
            project = ProjectData(
                id=project_id,
                name=project_data.get('name', project_id),
                client_profile=client_profile,
                project_assumptions=project_assumptions,
                comparison_locations=comparison_locations,
                financial_results=project_data.get('financial_results'),
                tags=project_data.get('tags', []),
                description=project_data.get('description', '')
            )
            
            # Save project to database
            success = self.persistence_service.save_project(project)
            if not success:
                raise Exception("Failed to save project to database")
            
            # Create preliminary analytics
            analytics = ProjectAnalytics(project_id=project_id)
            analytics.set_comparison_status('pending')
            
            # Trigger location comparison if locations are provided
            task_result = None
            if comparison_locations:
                task_result = self._trigger_location_comparison(project_id, comparison_locations)
                if task_result:
                    analytics.set_comparison_status('running', {'task_id': task_result.id})
            
            # Save analytics
            self.persistence_service.save_project_analytics(analytics)
            
            # Return preliminary data with immediate feedback
            return {
                'project_id': project_id,
                'status': 'created',
                'preliminary_data': analytics.get_preliminary_data(),
                'task_id': task_result.id if task_result else None,
                'message': 'Project created successfully'
            }
            
        except Exception as e:
            self.logger.error(f"Error creating project {project_id}: {e}")
            raise
    
    def update_project(self, project_id: str, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing project and optionally trigger location comparison."""
        try:
            # Load existing project
            existing_project = self.persistence_service.load_project(project_id)
            if not existing_project:
                raise ValueError(f"Project {project_id} not found")
            
            # Update project data
            if 'client_profile' in project_data:
                existing_project.client_profile = project_data['client_profile']
            if 'project_assumptions' in project_data:
                existing_project.project_assumptions = project_data['project_assumptions']
            if 'comparison_locations' in project_data:
                existing_project.comparison_locations = project_data['comparison_locations']
            if 'financial_results' in project_data:
                existing_project.financial_results = project_data['financial_results']
            if 'tags' in project_data:
                existing_project.tags = project_data['tags']
            if 'description' in project_data:
                existing_project.description = project_data['description']
            
            # Update metadata
            existing_project.modified_at = datetime.now()
            
            # Save updated project
            success = self.persistence_service.save_project(existing_project)
            if not success:
                raise Exception("Failed to update project in database")
            
            # Load or create analytics
            analytics = self.persistence_service.load_project_analytics(project_id)
            if not analytics:
                analytics = ProjectAnalytics(project_id=project_id)
            
            # Check if location comparison is needed
            comparison_locations = existing_project.comparison_locations or []
            task_result = None
            
            if comparison_locations:
                # Check if comparison data is stale or locations have changed
                if analytics.is_comparison_stale(max_age_hours=1):
                    task_result = self._trigger_location_comparison(project_id, comparison_locations)
                    if task_result:
                        analytics.set_comparison_status('running', {'task_id': task_result.id})
            
            # Save analytics
            self.persistence_service.save_project_analytics(analytics)
            
            # Return preliminary data with immediate feedback
            return {
                'project_id': project_id,
                'status': 'updated',
                'preliminary_data': analytics.get_preliminary_data(),
                'task_id': task_result.id if task_result else None,
                'message': 'Project updated successfully'
            }
            
        except Exception as e:
            self.logger.error(f"Error updating project {project_id}: {e}")
            raise
    
    def _trigger_location_comparison(self, project_id: str, comparison_locations: List[str]) -> Optional[Any]:
        """Trigger async location comparison task."""
        if not self.celery_available or not comparison_locations:
            self.logger.warning("Celery not available or no locations to compare")
            return None
        
        try:
            # Queue the location comparison task
            task_result = self.run_location_comparison.delay(
                project_id=project_id,
                comparison_locations=comparison_locations
            )
            
            self.logger.info(f"Location comparison task queued for project {project_id}: {task_result.id}")
            return task_result
            
        except Exception as e:
            self.logger.error(f"Error queueing location comparison task: {e}")
            return None
    
    def get_project_with_analytics(self, project_id: str) -> Dict[str, Any]:
        """Get project data with analytics information."""
        try:
            # Load project
            project = self.persistence_service.load_project(project_id)
            if not project:
                raise ValueError(f"Project {project_id} not found")
            
            # Load analytics
            analytics = self.persistence_service.load_project_analytics(project_id)
            if not analytics:
                analytics = ProjectAnalytics(project_id=project_id)
            
            return {
                'project': {
                    'id': project.id,
                    'name': project.name,
                    'client_profile': project.client_profile,
                    'project_assumptions': project.project_assumptions,
                    'financial_results': project.financial_results,
                    'comparison_locations': project.comparison_locations,
                    'created_at': project.created_at.isoformat() if project.created_at else None,
                    'modified_at': project.modified_at.isoformat() if project.modified_at else None,
                    'version': project.version,
                    'tags': project.tags,
                    'description': project.description
                },
                'analytics': analytics.to_dict()
            }
            
        except Exception as e:
            self.logger.error(f"Error loading project with analytics {project_id}: {e}")
            raise
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get status of a Celery task."""
        if not self.celery_available:
            return {'status': 'UNAVAILABLE', 'message': 'Celery not available'}
        
        try:
            task_result = self.celery_app.AsyncResult(task_id)
            
            return {
                'task_id': task_id,
                'status': task_result.status,
                'result': task_result.result,
                'traceback': task_result.traceback,
                'successful': task_result.successful(),
                'failed': task_result.failed(),
                'ready': task_result.ready()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting task status {task_id}: {e}")
            return {'status': 'ERROR', 'message': str(e)}
    
    def list_projects_with_analytics(self, limit: int = 50) -> List[Dict[str, Any]]:
        """List projects with their analytics status."""
        try:
            projects = self.persistence_service.list_projects(limit=limit)
            
            # Enrich with analytics information
            enriched_projects = []
            for project in projects:
                analytics = self.persistence_service.load_project_analytics(project['id'])
                
                enriched_project = project.copy()
                enriched_project['analytics'] = {
                    'has_comparisons': analytics.location_comparisons is not None if analytics else False,
                    'comparison_status': analytics.comparison_status if analytics else 'pending',
                    'last_comparison_date': analytics.last_comparison_date.isoformat() if analytics and analytics.last_comparison_date else None,
                    'total_locations': analytics.kpi_summary.get('total_locations', 0) if analytics and analytics.kpi_summary else 0
                }
                
                enriched_projects.append(enriched_project)
            
            return enriched_projects
            
        except Exception as e:
            self.logger.error(f"Error listing projects with analytics: {e}")
            raise
    
    def delete_project(self, project_id: str) -> bool:
        """Delete a project and its analytics."""
        try:
            # Delete project
            success = self.persistence_service.delete_project(project_id)
            if success:
                # Delete analytics
                self.persistence_service.delete_project_analytics(project_id)
                self.logger.info(f"Project {project_id} and its analytics deleted")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error deleting project {project_id}: {e}")
            return False
    
    def get_project_analytics(self, project_id: str) -> Optional[ProjectAnalytics]:
        """Get project analytics."""
        return self.persistence_service.load_project_analytics(project_id)
    
    def update_project_analytics(self, analytics: ProjectAnalytics) -> bool:
        """Update project analytics."""
        return self.persistence_service.save_project_analytics(analytics)
