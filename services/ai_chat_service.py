import logging
import asyncio
import hashlib
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from .ai_analysis_service import LLMConfig, AnalysisRequest, AIAnalysisService, AnalysisResult


class AIAnalysisChatService:
    """AI Analysis Chat Service with conversational memory support."""
    
    def __init__(self, config: LLMConfig):
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.ai_analysis_service = AIAnalysisService(config)
        self.session_cache = {}
        rate_limit = config.rate_limit if config and hasattr(config, 'rate_limit') else 10
        self.rate_limiter = RateLimiter(rate_limit)
        
    async def start_session(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Start a new AI session and return the initial analysis using AnalysisRequest."""
        try:
            session_id = self._generate_session_id()
            
            # Check rate limit
            if not self.rate_limiter.can_make_request():
                raise Exception("Rate limit exceeded. Please wait before starting a new session.")
            
            # Ensure data is not None and has required structure
            if data is None:
                data = {}
            
            # Ensure required keys exist with default values
            financial_results = data.get('financial_results') or {}
            client_profile = data.get('client_profile') or {}
            assumptions = data.get('assumptions') or {}
            
            # Create analysis request for initial session
            analysis_request = AnalysisRequest(
                analysis_type="comprehensive",
                data=data,
                context={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "conversation_start": True
                },
                output_format="narrative",
                include_charts=True,
                include_recommendations=True,
                anonymize_data=self.config.privacy_mode if self.config else True
            )
            
            # Generate initial analysis using existing service
            analysis_result = await self.ai_analysis_service.analyze_financial_data(
                financial_results,
                client_profile,
                assumptions,
                analysis_type="comprehensive"
            )
            
            # Initialize session with conversation history
            session_data = {
                "session_id": session_id,
                "created_at": datetime.now().isoformat(),
                "conversation_history": [
                    {
                        "role": "system",
                        "content": "Session started with financial analysis",
                        "timestamp": datetime.now().isoformat()
                    },
                    {
                        "role": "assistant",
                        "content": analysis_result.narrative,
                        "timestamp": datetime.now().isoformat(),
                        "analysis_data": {
                            "insights": analysis_result.insights,
                            "recommendations": analysis_result.recommendations,
                            "confidence_score": analysis_result.confidence_score
                        }
                    }
                ],
                "original_data": data,
                "request_count": 1
            }
            
            self.session_cache[session_id] = session_data
            
            return {
                "session_id": session_id,
                "initial_analysis": analysis_result.narrative,
                "insights": analysis_result.insights,
                "recommendations": analysis_result.recommendations,
                "confidence_score": analysis_result.confidence_score,
                "charts_analysis": analysis_result.charts_analysis,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to start session: {str(e)}")
            raise
    
    async def send_message(self, session_id: str, new_message: str) -> str:
        """Send a message in an ongoing session and receive reply."""
        try:
            if session_id not in self.session_cache:
                raise ValueError("Invalid session ID. Session may have expired or never existed.")
            
            session_data = self.session_cache[session_id]
            
            # Check rate limit
            if not self.rate_limiter.can_make_request():
                raise Exception("Rate limit exceeded. Please wait before sending another message.")
            
            # Check session request limit
            if session_data["request_count"] >= self.config.rate_limit:
                raise Exception("Session request limit exceeded. Please start a new session.")
            
            # Add user message to conversation history
            session_data["conversation_history"].append({
                "role": "user",
                "content": new_message,
                "timestamp": datetime.now().isoformat()
            })
            
            # Prepare context with conversation history
            conversation_context = self._build_conversation_context(session_data)
            
            # Create analysis request for conversational response
            analysis_request = AnalysisRequest(
                analysis_type="conversational",
                data={
                    "user_message": new_message,
                    "conversation_history": conversation_context,
                    "original_data": session_data["original_data"]
                },
                context={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "conversation_turn": len(session_data["conversation_history"])
                },
                output_format="narrative",
                include_charts=False,
                include_recommendations=True,
                anonymize_data=self.config.privacy_mode
            )
            
            # Generate conversational response using provider
            analysis_result = await self.ai_analysis_service.provider.generate_analysis(analysis_request)
            
            # Add AI response to conversation history
            session_data["conversation_history"].append({
                "role": "assistant",
                "content": analysis_result.narrative,
                "timestamp": datetime.now().isoformat(),
                "confidence_score": analysis_result.confidence_score
            })
            
            # Update session metrics
            session_data["request_count"] += 1
            session_data["last_activity"] = datetime.now().isoformat()
            
            return analysis_result.narrative
            
        except Exception as e:
            self.logger.error(f"Failed to send message in session {session_id}: {str(e)}")
            raise
    
    def get_session_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Get the conversation history for a session."""
        if session_id not in self.session_cache:
            raise ValueError("Invalid session ID")
        
        return self.session_cache[session_id]["conversation_history"]
    
    def end_session(self, session_id: str) -> bool:
        """End a session and clean up resources."""
        if session_id in self.session_cache:
            del self.session_cache[session_id]
            self.logger.info(f"Session {session_id} ended")
            return True
        return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.session_cache.keys())
    
    def clear_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clear sessions older than max_age_hours."""
        current_time = datetime.now()
        expired_sessions = []
        
        for session_id, session_data in self.session_cache.items():
            created_at = datetime.fromisoformat(session_data["created_at"])
            age_hours = (current_time - created_at).total_seconds() / 3600
            
            if age_hours > max_age_hours:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.session_cache[session_id]
        
        self.logger.info(f"Cleared {len(expired_sessions)} expired sessions")
        return len(expired_sessions)
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        timestamp = datetime.now().isoformat()
        hash_input = f"{timestamp}_{self.config.provider}_{self.config.model_id}"
        return hashlib.sha256(hash_input.encode()).hexdigest()[:16]
    
    def _build_conversation_context(self, session_data: Dict[str, Any]) -> str:
        """Build conversation context for AI provider."""
        history = session_data["conversation_history"]
        context_messages = []
        
        # Include last few exchanges to maintain context
        recent_history = history[-6:] if len(history) > 6 else history
        
        for message in recent_history:
            role = message["role"]
            content = message["content"]
            
            if role == "user":
                context_messages.append(f"User: {content}")
            elif role == "assistant":
                context_messages.append(f"Assistant: {content}")
        
        return "\n".join(context_messages)
    
    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get statistics for a session."""
        if session_id not in self.session_cache:
            raise ValueError("Invalid session ID")
        
        session_data = self.session_cache[session_id]
        
        return {
            "session_id": session_id,
            "created_at": session_data["created_at"],
            "last_activity": session_data.get("last_activity", session_data["created_at"]),
            "request_count": session_data["request_count"],
            "conversation_length": len(session_data["conversation_history"]),
            "rate_limit": self.config.rate_limit,
            "provider": self.config.provider,
            "model_id": self.config.model_id
        }


class RateLimiter:
    """Simple rate limiter for AI requests."""
    
    def __init__(self, requests_per_minute: int):
        self.requests_per_minute = requests_per_minute
        self.request_times = []
    
    def can_make_request(self) -> bool:
        """Check if a request can be made within rate limit."""
        current_time = datetime.now()
        
        # Remove requests older than 1 minute
        self.request_times = [
            req_time for req_time in self.request_times
            if (current_time - req_time).total_seconds() < 60
        ]
        
        if len(self.request_times) < self.requests_per_minute:
            self.request_times.append(current_time)
            return True
        
        return False

