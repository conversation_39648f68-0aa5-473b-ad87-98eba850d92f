"""
Secure Configuration Service
============================

Service for securely handling API keys and sensitive configuration data.
Provides environment variable injection, encryption at rest, and validation.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from cryptography.fernet import Fernet
import base64

# Import the existing DataProtectionManager for encryption
from .ai_analysis_service import DataProtectionManager


class SecureConfigService:
    """Service for secure configuration management with encryption and validation."""
    
    def __init__(self, encryption_key: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Initialize encryption using the existing DataProtectionManager
        self.data_protection = DataProtectionManager(encryption_key)
        
        # Environment variable mappings for API keys
        self.env_key_mappings = {
            'openai': 'OPENAI_API_KEY',
            'openrouter': 'OPENROUTER_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'custom': 'CUSTOM_API_KEY',
            'azure': 'AZURE_OPENAI_API_KEY',
            'cohere': 'COHERE_API_KEY',
            'huggingface': 'HUGGINGFACE_API_KEY'
        }
        
        self.logger.info("Secure configuration service initialized")
    
    def load_secure_config(self, config_path: Path) -> Dict[str, Any]:
        """
        Load configuration file and securely inject API keys from environment variables.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Configuration dictionary with securely injected API keys
        """
        try:
            # Load base configuration
            if not config_path.exists():
                self.logger.error(f"Configuration file not found: {config_path}")
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Inject API keys securely
            config_data = self._inject_api_keys(config_data)
            
            # Validate the configuration
            validation_result = self._validate_ai_config(config_data)
            
            if not validation_result['is_valid']:
                self.logger.warning("Configuration validation failed")
                config_data = self._apply_graceful_fallback(config_data, validation_result)
            
            self.logger.info("Configuration loaded securely")
            return config_data
            
        except Exception as e:
            self.logger.error(f"Failed to load secure configuration: {e}")
            raise
    
    def _inject_api_keys(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Inject API keys from environment variables into the configuration.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Configuration with injected API keys
        """
        if 'ai_config' not in config_data:
            return config_data
        
        ai_config = config_data['ai_config']
        provider = ai_config.get('provider', '').lower()
        
        # Determine the environment variable name
        env_var_name = self.env_key_mappings.get(provider, 'OPENAI_API_KEY')
        
        # Get API key from environment
        api_key = os.getenv(env_var_name)
        
        if api_key:
            # Encrypt the API key for storage
            encrypted_key = self.data_protection.encrypt_data(api_key)
            ai_config['api_key'] = encrypted_key
            ai_config['_key_source'] = 'environment'
            ai_config['_key_encrypted'] = True
            
            self.logger.info(f"API key injected from environment variable: {env_var_name}")
        else:
            self.logger.warning(f"No API key found in environment variable: {env_var_name}")
            ai_config['api_key'] = ""
            ai_config['_key_source'] = 'missing'
            ai_config['_key_encrypted'] = False
        
        return config_data
    
    def _validate_ai_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate AI configuration and check for required API keys.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Validation result dictionary
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'can_enable_ai': False
        }
        
        if 'ai_config' not in config_data:
            validation_result['warnings'].append("No AI configuration section found")
            return validation_result
        
        ai_config = config_data['ai_config']
        
        # Check if AI analysis is enabled
        include_ai_analysis = config_data.get('include_ai_analysis', False)
        
        if include_ai_analysis:
            # Validate required fields
            required_fields = ['provider', 'api_key', 'model_id']
            for field in required_fields:
                if not ai_config.get(field):
                    validation_result['errors'].append(f"Missing required AI config field: {field}")
                    validation_result['is_valid'] = False
            
            # Check API key specifically
            api_key = ai_config.get('api_key', '')
            if not api_key:
                validation_result['errors'].append("API key is missing - AI analysis cannot be enabled")
                validation_result['is_valid'] = False
            else:
                # Try to decrypt the key to verify it's valid
                try:
                    if ai_config.get('_key_encrypted', False):
                        decrypted_key = self.data_protection.decrypt_data(api_key)
                        if len(decrypted_key.strip()) > 0:
                            validation_result['can_enable_ai'] = True
                        else:
                            validation_result['errors'].append("Decrypted API key is empty")
                            validation_result['is_valid'] = False
                    else:
                        validation_result['warnings'].append("API key is not encrypted")
                        validation_result['can_enable_ai'] = len(api_key.strip()) > 0
                except Exception as e:
                    validation_result['errors'].append(f"Failed to decrypt API key: {e}")
                    validation_result['is_valid'] = False
            
            # Validate provider
            provider = ai_config.get('provider', '').lower()
            supported_providers = ['openai', 'openrouter', 'anthropic', 'custom', 'azure', 'cohere', 'huggingface']
            if provider not in supported_providers:
                validation_result['warnings'].append(f"Provider '{provider}' may not be fully supported")
            
            # Validate API URL
            api_url = ai_config.get('api_url', '')
            if not api_url:
                validation_result['errors'].append("API URL is missing")
                validation_result['is_valid'] = False
            elif not api_url.startswith(('http://', 'https://')):
                validation_result['errors'].append("API URL must start with http:// or https://")
                validation_result['is_valid'] = False
        
        return validation_result
    
    def _apply_graceful_fallback(self, config_data: Dict[str, Any], validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply graceful fallback when AI configuration is invalid.
        
        Args:
            config_data: Configuration dictionary
            validation_result: Validation result
            
        Returns:
            Configuration with graceful fallback applied
        """
        # Log clear warning
        self.logger.warning("AI configuration validation failed - applying graceful fallback")
        
        for error in validation_result['errors']:
            self.logger.warning(f"AI Config Error: {error}")
        
        # Disable AI analysis if validation failed
        if not validation_result['can_enable_ai']:
            config_data['include_ai_analysis'] = False
            self.logger.warning("AI analysis has been disabled due to configuration errors")
        
        # Add fallback metadata
        config_data['_fallback_applied'] = True
        config_data['_fallback_timestamp'] = datetime.now().isoformat()
        config_data['_fallback_reasons'] = validation_result['errors']
        
        return config_data
    
    def get_decrypted_api_key(self, config_data: Dict[str, Any]) -> Optional[str]:
        """
        Get the decrypted API key from configuration.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Decrypted API key or None if not available
        """
        if 'ai_config' not in config_data:
            return None
        
        ai_config = config_data['ai_config']
        api_key = ai_config.get('api_key', '')
        
        if not api_key:
            return None
        
        try:
            if ai_config.get('_key_encrypted', False):
                return self.data_protection.decrypt_data(api_key)
            else:
                return api_key
        except Exception as e:
            self.logger.error(f"Failed to decrypt API key: {e}")
            return None
    
    def encrypt_and_store_api_key(self, api_key: str, provider: str = 'openai') -> str:
        """
        Encrypt API key for secure storage.
        
        Args:
            api_key: The API key to encrypt
            provider: The provider name
            
        Returns:
            Encrypted API key
        """
        try:
            encrypted_key = self.data_protection.encrypt_data(api_key)
            self.logger.info(f"API key encrypted for provider: {provider}")
            return encrypted_key
        except Exception as e:
            self.logger.error(f"Failed to encrypt API key: {e}")
            raise
    
    def validate_environment_setup(self) -> Dict[str, Any]:
        """
        Validate that environment variables are properly configured.
        
        Returns:
            Validation result dictionary
        """
        validation_result = {
            'is_valid': True,
            'available_keys': [],
            'missing_keys': [],
            'recommendations': []
        }
        
        # Check for available API keys
        for provider, env_var in self.env_key_mappings.items():
            if os.getenv(env_var):
                validation_result['available_keys'].append({
                    'provider': provider,
                    'env_var': env_var,
                    'key_length': len(os.getenv(env_var))
                })
            else:
                validation_result['missing_keys'].append({
                    'provider': provider,
                    'env_var': env_var
                })
        
        # Generate recommendations
        if not validation_result['available_keys']:
            validation_result['is_valid'] = False
            validation_result['recommendations'].append(
                "No API keys found in environment variables. Please set at least one of: " +
                ", ".join(self.env_key_mappings.values())
            )
        else:
            validation_result['recommendations'].append(
                f"Found {len(validation_result['available_keys'])} API key(s) in environment"
            )
        
        return validation_result
    
    def create_secure_config_template(self, output_path: Path) -> bool:
        """
        Create a secure configuration template file.
        
        Args:
            output_path: Path where to save the template
            
        Returns:
            True if successful, False otherwise
        """
        try:
            template_config = {
                "_template_info": {
                    "description": "Secure Enhanced Reporting Configuration Template",
                    "version": "2.0",
                    "created_at": datetime.now().isoformat(),
                    "security_features": [
                        "API keys injected from environment variables",
                        "Encryption at rest using Fernet",
                        "Validation with graceful fallback",
                        "PII detection and removal"
                    ]
                },
                "mode": "enhanced",
                "include_ai_analysis": True,
                "include_professional_styling": True,
                "include_security_features": True,
                "include_charts": True,
                "include_validation": True,
                "parallel_processing": True,
                "export_formats": ["PDF", "HTML", "DOCX"],
                "ai_config": {
                    "provider": "openai",
                    "api_url": "https://api.openai.com/v1/chat/completions",
                    "api_key": "",
                    "_key_note": "API key will be injected from environment variable during service startup",
                    "model_id": "gpt-4",
                    "system_prompt": "You are a professional financial analyst specializing in renewable energy projects.\\nAnalyze the provided financial data and generate comprehensive insights.\\n\\nYour analysis should include:\\n1. Executive summary of financial performance\\n2. Key strengths and weaknesses\\n3. Risk assessment with specific recommendations\\n4. Market positioning analysis\\n5. Investment recommendation with rationale\\n6. Strategic next steps\\n\\nFormat your response as structured content with clear sections.\\nBe precise, professional, and focus on actionable insights.\\nAvoid speculation and base all conclusions on the provided data.\\nUse industry-standard financial terminology and metrics.",
                    "max_tokens": 4000,
                    "temperature": 0.3,
                    "timeout": 30,
                    "rate_limit": 10,
                    "data_retention_days": 0,
                    "enable_caching": True,
                    "privacy_mode": True
                },
                "security_config": {
                    "enable_encryption": True,
                    "enable_data_anonymization": True,
                    "enable_audit_logging": True,
                    "data_retention_policy": "no_retention",
                    "access_control_enabled": False,
                    "allowed_domains": [],
                    "blocked_domains": [],
                    "_encryption_note": "API keys and sensitive data are encrypted at rest using Fernet encryption"
                },
                "pdf_config": {
                    "page_size": "A4",
                    "margins": {
                        "top": 2.5,
                        "bottom": 2.5,
                        "left": 2.0,
                        "right": 2.0
                    },
                    "header_height": 1.5,
                    "footer_height": 1.0,
                    "font_family": "Helvetica",
                    "font_size": 10,
                    "line_spacing": 1.2,
                    "paragraph_spacing": 0.5,
                    "company_name": "Professional Financial Analysis",
                    "primary_color": "#2E86AB",
                    "secondary_color": "#A23B72",
                    "accent_color": "#F18F01",
                    "footer_text": "Confidential Financial Analysis Report"
                },
                "performance_config": {
                    "enable_parallel_processing": True,
                    "max_workers": 4,
                    "enable_ai_cache": True,
                    "cache_ttl_minutes": 60,
                    "batch_processing": False,
                    "optimize_for_speed": True
                },
                "template_config": {
                    "default_template": "professional_financial_report",
                    "custom_templates_path": "templates/custom",
                    "enable_template_caching": True,
                    "template_validation": True
                },
                "export_config": {
                    "output_directory": "output/enhanced_reports",
                    "filename_format": "{client_name}_{timestamp}_report",
                    "include_metadata": True,
                    "compress_outputs": False,
                    "auto_cleanup_days": 30
                },
                "validation_config": {
                    "enable_data_validation": True,
                    "validation_level": "comprehensive",
                    "fail_on_critical_errors": True,
                    "show_warnings": True,
                    "validation_timeout": 10
                },
                "logging_config": {
                    "log_level": "INFO",
                    "enable_debug_logging": False,
                    "log_ai_interactions": False,
                    "log_file_path": "logs/enhanced_reporting.log",
                    "max_log_size_mb": 10,
                    "backup_count": 5,
                    "_security_note": "Set log_ai_interactions to false to prevent API keys from being logged"
                },
                "created_at": datetime.now().isoformat(),
                "version": "2.0"
            }
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write the template
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(template_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Secure configuration template created: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create secure configuration template: {e}")
            return False
    
    def audit_config_security(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Audit configuration for security issues.
        
        Args:
            config_data: Configuration dictionary
            
        Returns:
            Security audit result
        """
        audit_result = {
            'security_score': 100,
            'issues': [],
            'recommendations': [],
            'passed_checks': []
        }
        
        # Check for plain text API keys
        if 'ai_config' in config_data:
            ai_config = config_data['ai_config']
            api_key = ai_config.get('api_key', '')
            
            if api_key and not ai_config.get('_key_encrypted', False):
                audit_result['issues'].append({
                    'severity': 'HIGH',
                    'type': 'unencrypted_api_key',
                    'description': 'API key is stored in plain text',
                    'recommendation': 'Use environment variables and encryption for API keys'
                })
                audit_result['security_score'] -= 30
            elif api_key and ai_config.get('_key_encrypted', False):
                audit_result['passed_checks'].append('API key is encrypted')
            
            # Check for environment variable injection
            if ai_config.get('_key_source') == 'environment':
                audit_result['passed_checks'].append('API key injected from environment variable')
            elif api_key:
                audit_result['recommendations'].append('Consider using environment variables for API keys')
        
        # Check logging configuration
        if 'logging_config' in config_data:
            logging_config = config_data['logging_config']
            if logging_config.get('log_ai_interactions', True):
                audit_result['issues'].append({
                    'severity': 'MEDIUM',
                    'type': 'logging_risk',
                    'description': 'AI interactions logging is enabled - may expose sensitive data',
                    'recommendation': 'Set log_ai_interactions to false for security'
                })
                audit_result['security_score'] -= 15
            else:
                audit_result['passed_checks'].append('AI interactions logging is disabled')
        
        # Check security features
        if config_data.get('include_security_features', True):
            audit_result['passed_checks'].append('Security features are enabled')
        else:
            audit_result['recommendations'].append('Enable security features for better protection')
        
        # Check data retention policy
        if 'security_config' in config_data:
            security_config = config_data['security_config']
            if security_config.get('data_retention_policy') == 'no_retention':
                audit_result['passed_checks'].append('Data retention policy is set to no retention')
            else:
                audit_result['recommendations'].append('Consider setting data retention policy to no_retention')
        
        return audit_result
