"""
Progress Service for Long-Running Operations
===========================================

Centralized progress tracking service for all long-running operations in the application.
"""

import asyncio
import threading
import time
import logging
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import flet as ft

from components.ui.unified_progress_system import (
    UnifiedProgressSystem, ProgressDisplayMode, ProgressStatus, 
    ProgressStep, ProgressOperation, create_progress_bottom_sheet
)


class OperationType(Enum):
    """Types of operations that can be tracked."""
    FINANCIAL_CALCULATION = "financial_calculation"
    ML_TRAINING = "ml_training"
    DATA_EXPORT = "data_export"
    FILE_OPERATION = "file_operation"
    MONTE_CARLO_SIMULATION = "monte_carlo_simulation"
    SENSITIVITY_ANALYSIS = "sensitivity_analysis"
    REPORT_GENERATION = "report_generation"
    MODEL_VALIDATION = "model_validation"
    AI_ANALYSIS = "ai_analysis"
    COMPREHENSIVE_ANALYSIS = "comprehensive_analysis"


# Remove duplicate definitions - using unified system classes


class ProgressService:
    """Centralized progress tracking service using unified progress system."""
    
    def __init__(self, page: ft.Page, display_mode: ProgressDisplayMode = ProgressDisplayMode.BOTTOM_SHEET):
        self.page = page
        self.logger = logging.getLogger(__name__)
        self.operations: Dict[str, ProgressOperation] = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._lock = threading.RLock()
        self.current_operation: Optional[ProgressOperation] = None
        
        # Create unified progress system
        self.progress_system = UnifiedProgressSystem(page, display_mode)
        
        # Progress update callbacks
        self.progress_callbacks: List[Callable] = []
        
        # Operation templates using unified ProgressStep
        self.operation_templates = {
            OperationType.FINANCIAL_CALCULATION: [
                ProgressStep("validate_inputs", "Validating inputs", 0.1),
                ProgressStep("dcf_calculation", "DCF calculation", 0.4),
                ProgressStep("kpi_calculation", "KPI calculation", 0.2),
                ProgressStep("ml_prediction", "ML predictions", 0.2),
                ProgressStep("finalize_results", "Finalizing results", 0.1)
            ],
            OperationType.AI_ANALYSIS: [
                ProgressStep("prepare_data", "Preparing data for AI analysis", 0.1),
                ProgressStep("anonymize_data", "Anonymizing sensitive data", 0.1),
                ProgressStep("generate_insights", "Generating AI insights", 0.4),
                ProgressStep("analyze_charts", "Analyzing charts with AI", 0.2),
                ProgressStep("create_narratives", "Creating AI narratives", 0.2)
            ],
            OperationType.ML_TRAINING: [
                ProgressStep("prepare_data", "Preparing training data", 0.2),
                ProgressStep("feature_engineering", "Feature engineering", 0.1),
                ProgressStep("model_training", "Training models", 0.5),
                ProgressStep("model_validation", "Validating models", 0.1),
                ProgressStep("save_models", "Saving models", 0.1)
            ],
            OperationType.MONTE_CARLO_SIMULATION: [
                ProgressStep("setup_simulation", "Setting up simulation", 0.1),
                ProgressStep("generate_scenarios", "Generating scenarios", 0.2),
                ProgressStep("run_simulations", "Running simulations", 0.6),
                ProgressStep("analyze_results", "Analyzing results", 0.1)
            ],
            OperationType.DATA_EXPORT: [
                ProgressStep("prepare_data", "Preparing data", 0.2),
                ProgressStep("format_data", "Formatting data", 0.1),
                ProgressStep("generate_charts", "Generating charts", 0.3),
                ProgressStep("create_document", "Creating document", 0.3),
                ProgressStep("save_file", "Saving file", 0.1)
            ],
            OperationType.REPORT_GENERATION: [
                ProgressStep("collect_data", "Collecting data", 0.1),
                ProgressStep("run_ai_analysis", "Running AI analysis", 0.3),
                ProgressStep("generate_charts", "Generating charts", 0.2),
                ProgressStep("create_sections", "Creating report sections", 0.2),
                ProgressStep("format_document", "Formatting document", 0.1),
                ProgressStep("save_report", "Saving report", 0.1)
            ],
            OperationType.COMPREHENSIVE_ANALYSIS: [
                ProgressStep("validate_inputs", "Validating inputs", 0.05),
                ProgressStep("financial_modeling", "Financial modeling", 0.25),
                ProgressStep("ai_analysis", "AI analysis", 0.25),
                ProgressStep("generate_charts", "Generating charts", 0.20),
                ProgressStep("create_reports", "Creating reports", 0.20),
                ProgressStep("finalize_outputs", "Finalizing outputs", 0.05)
            ]
        }
    
    def create_operation(self, operation_id: str, name: str, operation_type: OperationType,
                        custom_steps: Optional[List[ProgressStep]] = None,
                        callback: Optional[Callable] = None) -> ProgressOperation:
        """Create a new progress operation."""
        with self._lock:
            if custom_steps:
                steps = custom_steps
            else:
                steps = self.operation_templates.get(operation_type, [
                    ProgressStep("default_step", "Processing", 1.0)
                ])
            
            # Create copies of template steps
            steps_copy = []
            for step in steps:
                new_step = ProgressStep(
                    id=step.id,
                    name=step.name,
                    weight=step.weight,
                    status=ProgressStatus.PENDING
                )
                steps_copy.append(new_step)
            
            operation = ProgressOperation(
                id=operation_id,
                name=name,
                steps=steps_copy,
                callback=callback,
                cancellation_token=threading.Event()
            )
            
            self.operations[operation_id] = operation
            self.logger.info(f"Created operation: {operation_id} - {name}")
            return operation
    
    async def start_operation(self, operation_id: str, show_overlay: bool = True) -> None:
        """Start a progress operation."""
        with self._lock:
            if operation_id not in self.operations:
                raise ValueError(f"Operation {operation_id} not found")
            
            operation = self.operations[operation_id]
            operation.start_time = time.time()
            operation.status = ProgressStatus.RUNNING
            self.current_operation = operation
        
        if show_overlay:
            await self.progress_system.show(operation)
        
        self.logger.info(f"Started operation: {operation_id}")
    
    def update_step_progress(self, operation_id: str, step_id: str, progress: float, 
                           message: str = "", substep_name: str = "") -> None:
        """Update progress for a specific step."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            step = next((s for s in operation.steps if s.id == step_id), None)
            
            if not step:
                return
            
            # Update step
            step.progress = min(100.0, max(0.0, progress))
            step.message = message
            
            if step.status == ProgressStatus.PENDING and progress > 0:
                step.status = ProgressStatus.RUNNING
                step.start_time = time.time()
            elif progress >= 100:
                step.status = ProgressStatus.COMPLETED
                step.end_time = time.time()
            
            # Calculate overall progress
            overall_progress = operation.current_progress()
            
            # Update progress UI
            current_step_index = next((i for i, s in enumerate(operation.steps) if s.id == step_id), None)
            if current_step_index is not None:
                display_message = f"{step.name}: {message}" if message else step.name
                if substep_name:
                    display_message += f" - {substep_name}"
                
                self.progress_system.update_progress(
                    overall_progress,
                    display_message,
                    current_step_index + 1,
                    len(operation.steps)
                )
            
            # Call progress callbacks
            for callback in self.progress_callbacks:
                try:
                    callback(operation_id, step_id, progress, message)
                except Exception as e:
                    self.logger.error(f"Error in progress callback: {e}")
    
    def complete_step(self, operation_id: str, step_id: str, message: str = "Completed") -> None:
        """Mark a step as completed."""
        self.update_step_progress(operation_id, step_id, 100.0, message)
    
    def fail_step(self, operation_id: str, step_id: str, error_message: str) -> None:
        """Mark a step as failed."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            step = next((s for s in operation.steps if s.id == step_id), None)
            
            if step:
                step.status = ProgressStatus.FAILED
                step.message = error_message
                step.end_time = time.time()
                
                # Update progress UI with error
                self.progress_system.update_progress(
                    operation.current_progress(),
                    f"Error in {step.name}: {error_message}"
                )
        
        self.logger.error(f"Step failed: {operation_id}.{step_id} - {error_message}")
    
    async def complete_operation(self, operation_id: str, final_message: str = "Operation completed") -> None:
        """Complete an operation."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.status = ProgressStatus.COMPLETED
            operation.end_time = time.time()
            
            # Mark any pending steps as completed
            for step in operation.steps:
                if step.status == ProgressStatus.PENDING:
                    step.status = ProgressStatus.COMPLETED
                    step.progress = 100.0
        
        # Update progress UI to 100% and hide
        self.progress_system.update_progress(100.0, final_message)
        await asyncio.sleep(0.5)
        await self.progress_system.hide(final_message, True)
        
        # Call completion callback
        if operation.callback:
            try:
                operation.callback(operation_id, True, None)
            except Exception as e:
                self.logger.error(f"Error in completion callback: {e}")
        
        self.logger.info(f"Completed operation: {operation_id}")
    
    async def fail_operation(self, operation_id: str, error_message: str) -> None:
        """Fail an operation."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.status = ProgressStatus.FAILED
            operation.end_time = time.time()
        
        # Update progress UI with error and hide
        self.progress_system.update_progress(
            operation.current_progress(),
            f"Operation failed: {error_message}"
        )
        await asyncio.sleep(2.0)
        await self.progress_system.hide("Operation failed", False)
        
        # Call completion callback with error
        if operation.callback:
            try:
                operation.callback(operation_id, False, error_message)
            except Exception as e:
                self.logger.error(f"Error in failure callback: {e}")
        
        self.logger.error(f"Failed operation: {operation_id} - {error_message}")
    
    def cancel_operation(self, operation_id: str) -> None:
        """Cancel an operation."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.cancellation_token.set()
            operation.status = ProgressStatus.CANCELLED
            operation.end_time = time.time()
        
        self.logger.info(f"Cancelled operation: {operation_id}")
    
    def is_cancelled(self, operation_id: str) -> bool:
        """Check if an operation is cancelled."""
        with self._lock:
            if operation_id not in self.operations:
                return False
            return self.operations[operation_id].cancellation_token.is_set()
    
    def get_operation_status(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """Get operation status."""
        with self._lock:
            if operation_id not in self.operations:
                return None
            
            operation = self.operations[operation_id]
            return {
                'id': operation.id,
                'name': operation.name,
                'type': operation.operation_type.value,
                'status': operation.status,
                'progress': operation.current_progress(),
                'start_time': operation.start_time,
                'end_time': operation.end_time,
                'duration': operation.end_time - operation.start_time if operation.end_time and operation.start_time else None,
                'steps': [
                    {
                        'id': step.id,
                        'name': step.name,
                        'status': step.status,
                        'progress': step.progress,
                        'message': step.message,
                        'duration': step.duration()
                    }
                    for step in operation.steps
                ]
            }
    
    def add_progress_callback(self, callback: Callable) -> None:
        """Add a progress callback."""
        self.progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable) -> None:
        """Remove a progress callback."""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)
    
    def cleanup_operation(self, operation_id: str) -> None:
        """Clean up completed/failed operations."""
        with self._lock:
            if operation_id in self.operations:
                del self.operations[operation_id]
                self.logger.info(f"Cleaned up operation: {operation_id}")
    
    def get_all_operations(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all operations."""
        with self._lock:
            return {
                op_id: self.get_operation_status(op_id)
                for op_id in self.operations.keys()
            }
    
    def set_cancel_callback(self, callback: Callable) -> None:
        """Set cancel callback for the progress UI."""
        self.progress_system.set_cancel_callback(callback)


# Global progress service instance
_progress_service: Optional[ProgressService] = None


def get_progress_service(page: ft.Page = None) -> Optional[ProgressService]:
    """Get or create global progress service."""
    global _progress_service
    if _progress_service is None and page:
        _progress_service = ProgressService(page)
    return _progress_service


def init_progress_service(page: ft.Page) -> ProgressService:
    """Initialize global progress service."""
    global _progress_service
    _progress_service = ProgressService(page)
    return _progress_service