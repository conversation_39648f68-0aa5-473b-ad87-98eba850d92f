"""
Enhanced Reporting Integration Service
======================================

Main orchestration service that integrates all enhanced reporting components:
- AI-powered analysis service
- Professional template engine
- Advanced export service
- Data validation framework
- Advanced PDF generation
- Security and performance optimization
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
import json
from dataclasses import dataclass, field
from enum import Enum

# Import enhanced services
from .ai_analysis_service import AIAnalysisService, LLMConfig
from .secure_config_service import SecureConfigService
from .professional_template_engine import ProfessionalTemplateEngine, BrandingConfig, LayoutConfig
from .enhanced_export_service import EnhancedExportService
from .data_validation_service import DataValidationService, ValidationSummary
from .advanced_pdf_service import AdvancedPDFService, PDFLayoutConfig, PDFBrandingConfig, PDFWatermarkConfig, PDFSignatureConfig, PDFSecurityLevel

# Import legacy services for backward compatibility
from .report_service import ReportService
from .export_service import ExportService

# Import data models
from models.client_profile import ClientProfile
from models.enhanced_project_assumptions import EnhancedProjectAssumptions


class ReportingMode(Enum):
    """Reporting generation modes."""
    STANDARD = "standard"
    ENHANCED = "enhanced"
    AI_POWERED = "ai_powered"
    PROFESSIONAL = "professional"


@dataclass
class ReportingConfig:
    """Configuration for enhanced reporting."""
    mode: ReportingMode = ReportingMode.ENHANCED
    include_ai_analysis: bool = True
    include_professional_styling: bool = True
    include_security_features: bool = True
    include_charts: bool = True
    include_validation: bool = True
    parallel_processing: bool = True
    export_formats: List[str] = field(default_factory=lambda: ['PDF', 'HTML', 'DOCX'])
    
    # AI Configuration
    ai_config: Optional[Dict[str, Any]] = None
    
    # PDF Configuration
    pdf_config: Optional[Dict[str, Any]] = None
    
    # Security Configuration
    security_config: Optional[Dict[str, Any]] = None
    
    # Performance Configuration
    performance_config: Optional[Dict[str, Any]] = None


@dataclass
class ReportingResult:
    """Result of enhanced reporting generation."""
    success: bool
    report_id: str
    generated_files: Dict[str, Path]
    processing_time: float
    validation_summary: Optional[ValidationSummary] = None
    ai_analysis_summary: Optional[Dict[str, Any]] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class EnhancedReportingIntegration:
    """Main integration service for enhanced reporting."""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        
        # Initialize secure configuration service
        self.secure_config_service = SecureConfigService()
        
        # Load configuration
        self.config = self._load_configuration(config_path)
        
        # Initialize services
        self._initialize_services()
        
        # Report generation history
        self.generation_history: List[ReportingResult] = []
        
        self.logger.info("Enhanced reporting integration service initialized")
    
    def _load_configuration(self, config_path: Optional[Path] = None) -> ReportingConfig:
        """Load configuration securely with environment variable injection and validation."""
        
        if config_path and config_path.exists():
            try:
                # Load configuration using secure service
                config_data = self.secure_config_service.load_secure_config(config_path)
                
                # Log security audit information
                audit_result = self.secure_config_service.audit_config_security(config_data)
                self.logger.info(f"Configuration security score: {audit_result['security_score']}/100")
                
                if audit_result['issues']:
                    for issue in audit_result['issues']:
                        self.logger.warning(f"Security issue ({issue['severity']}): {issue['description']}")
                
                return ReportingConfig(
                    mode=ReportingMode(config_data.get('mode', 'enhanced')),
                    include_ai_analysis=config_data.get('include_ai_analysis', True),
                    include_professional_styling=config_data.get('include_professional_styling', True),
                    include_security_features=config_data.get('include_security_features', True),
                    include_charts=config_data.get('include_charts', True),
                    include_validation=config_data.get('include_validation', True),
                    parallel_processing=config_data.get('parallel_processing', True),
                    export_formats=config_data.get('export_formats', ['PDF', 'HTML', 'DOCX']),
                    ai_config=config_data.get('ai_config'),
                    pdf_config=config_data.get('pdf_config'),
                    security_config=config_data.get('security_config'),
                    performance_config=config_data.get('performance_config')
                )
            except Exception as e:
                self.logger.error(f"Failed to load secure configuration: {e}")
                
                # Validate environment setup for better error messages
                env_validation = self.secure_config_service.validate_environment_setup()
                if not env_validation['is_valid']:
                    for recommendation in env_validation['recommendations']:
                        self.logger.warning(f"Environment setup: {recommendation}")
                
                return ReportingConfig()
        
        self.logger.info("No configuration file provided, using defaults")
        return ReportingConfig()
    
    def _initialize_services(self):
        """Initialize all enhanced services."""
        
        try:
            # AI Analysis Service
            if self.config.ai_config:
                # Get decrypted API key from secure config service
                decrypted_api_key = self.secure_config_service.get_decrypted_api_key({
                    'ai_config': self.config.ai_config
                })
                
                if decrypted_api_key:
                    ai_llm_config = LLMConfig(
                        provider=self.config.ai_config.get('provider', 'openai'),
                        api_key=decrypted_api_key,
                        model_id=self.config.ai_config.get('model_id', 'gpt-4'),
                        api_url=self.config.ai_config.get('api_url', 'https://api.openai.com/v1/chat/completions'),
                        system_prompt=self.config.ai_config.get('system_prompt', ''),
                        max_tokens=self.config.ai_config.get('max_tokens', 4000),
                        temperature=self.config.ai_config.get('temperature', 0.3)
                    )
                    self.ai_service = AIAnalysisService(ai_llm_config)
                    self.logger.info("AI Analysis Service initialized successfully")
                    try:
                        self.ai_service.ping()
                    except Exception as e:
                        self.logger.error(f"AI Analysis Service ping failed: {e}")
                        self.ai_service = None
                else:
                    self.logger.warning("AI Analysis Service disabled: No valid API key available")
                    self.ai_service = None
            else:
                self.logger.info("AI Analysis Service disabled: No AI configuration found")
                self.ai_service = None
            
            # Professional Template Engine
            self.template_engine = ProfessionalTemplateEngine()
            
            # Enhanced Export Service
            self.enhanced_export_service = EnhancedExportService()
            
            # Data Validation Service
            self.validation_service = DataValidationService()
            
            # Advanced PDF Service
            self.pdf_service = AdvancedPDFService()
            
            # Legacy services for backward compatibility
            self.legacy_report_service = ReportService()
            self.legacy_export_service = ExportService()
            
            self.logger.info("All enhanced services initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Service initialization failed: {e}")
            raise
    
    async def generate_comprehensive_report(self,
                                          client_profile: ClientProfile,
                                          assumptions: EnhancedProjectAssumptions,
                                          output_directory: Path,
                                          report_config: Optional[ReportingConfig] = None,
                                          progress_service: Optional['ProgressService'] = None,
                                          notification_system: Optional['NotificationSystem'] = None) -> ReportingResult:
        """Generate comprehensive report with all enhancements."""
        
        start_time = datetime.now()
        report_id = f"report_{start_time.strftime('%Y%m%d_%H%M%S')}"
        
        # Use provided config or default
        config = report_config or self.config
        
        result = ReportingResult(
            success=False,
            report_id=report_id,
            generated_files={},
            processing_time=0.0,
            metadata={
                'client_name': client_profile.company_name,
                'project_name': client_profile.project_name,
                'generation_timestamp': start_time.isoformat(),
                'config_mode': config.mode.value
            }
        )
        
        try:
            self.logger.info(f"Starting enhanced report generation: {report_id}")
            
            # Step 1: Data Validation
            if config.include_validation:
                validation_result = await self._validate_input_data(client_profile, assumptions)
                result.validation_summary = validation_result
                
                if not validation_result.overall_valid:
                    result.errors.extend(validation_result.errors)
                    result.warnings.extend(validation_result.warnings)
                    
                    if validation_result.critical_errors:
                        result.success = False
                        result.processing_time = (datetime.now() - start_time).total_seconds()
                        return result
            
            # Step 2: Generate financial analysis
            financial_results = await self._generate_financial_analysis(client_profile, assumptions)
            
            # Step 3: Generate charts
            charts_data = {}
            if config.include_charts:
                charts_data = await self._generate_charts(client_profile, assumptions, financial_results)
            else:
                charts_data = {}
            
            # Step 4: AI Analysis
            ai_analysis = {}
            if config.include_ai_analysis and self.ai_service:
                ai_analysis = await self._generate_ai_analysis(
                    client_profile, assumptions, financial_results, charts_data,
                    progress_service, notification_system
                )
                result.ai_analysis_summary = ai_analysis
            
            # Step 5: Combine all data
            comprehensive_data = {
                'client_profile': client_profile.to_dict(),
                'assumptions': assumptions.to_dict(),
                'financial_results': financial_results,
                'charts': charts_data,
                'ai_analysis': ai_analysis,
                'metadata': result.metadata
            }
            
            # Step 6: Generate reports in different formats
            if config.parallel_processing:
                generated_files = await self._generate_reports_parallel(comprehensive_data, output_directory, config)
            else:
                generated_files = await self._generate_reports_sequential(comprehensive_data, output_directory, config)
            
            result.generated_files = generated_files
            result.success = True
            
            self.logger.info(f"Report generation completed successfully: {report_id}")
            
        except Exception as e:
            self.logger.error(f"Report generation failed: {e}")
            result.errors.append(f"Report generation failed: {str(e)}")
            result.success = False
        
        result.processing_time = (datetime.now() - start_time).total_seconds()
        self.generation_history.append(result)
        
        return result
    
    async def _validate_input_data(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions) -> ValidationSummary:
        """Validate input data using the validation service."""
        
        try:
            return self.validation_service.validate_comprehensive_data(
                client_profile.to_dict(),
                assumptions.to_dict()
            )
        except Exception as e:
            self.logger.error(f"Data validation failed: {e}")
            # Return minimal validation result
            return ValidationSummary(
                overall_valid=False,
                critical_errors=[f"Validation service error: {str(e)}"],
                errors=[],
                warnings=[],
                data_integrity_score=0.0,
                business_logic_score=0.0,
                completeness_score=0.0
            )
    
    async def _generate_financial_analysis(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Generate financial analysis using legacy service."""
        
        try:
            # Use legacy report service for core financial calculations
            return self.legacy_report_service.generate_financial_analysis(client_profile, assumptions)
        except Exception as e:
            self.logger.error(f"Financial analysis failed: {e}")
            return {
                'error': f"Financial analysis failed: {str(e)}",
                'kpis': {},
                'cash_flows': {},
                'sensitivity_analysis': {}
            }
    
    async def _generate_charts(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions, financial_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate charts using legacy export service."""
        
        try:
            # Use legacy export service for chart generation
            return self.legacy_export_service.generate_comprehensive_charts(client_profile, assumptions, financial_results)
        except Exception as e:
            self.logger.error(f"Chart generation failed: {e}")
            return {}
    
    async def _generate_ai_analysis(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions, 
                                  financial_results: Dict[str, Any], charts_data: Dict[str, Any],
                                  progress_service: Optional['ProgressService'] = None,
                                  notification_system: Optional['NotificationSystem'] = None) -> Dict[str, Any]:
        """Generate AI analysis using AI service with progress tracking and error handling."""
        
        # Initialize progress tracking if available
        operation_id = None
        if progress_service:
            from services.progress_service import OperationType
            operation_id = f"ai_analysis_{datetime.now().timestamp()}"
            progress_operation = progress_service.create_operation(
                operation_id=operation_id,
                name="AI Analysis Generation",
                operation_type=OperationType.AI_ANALYSIS
            )
            await progress_service.start_operation(operation_id)
        
        try:
            # Ensure charts_data is passed, even if empty
            analysis_data = {
                'client_profile': client_profile.to_dict(),
                'assumptions': assumptions.to_dict(),
                'financial_results': financial_results,
                'charts': charts_data or {}
            }
            
            # Update progress for data preparation
            if progress_service:
                progress_service.update_step_progress(
                    operation_id, "prepare_data", 100.0, "Data prepared for AI analysis"
                )
            
            # Call AI service
            result = await self.ai_service.analyze_comprehensive_data(analysis_data)
            
            # Complete progress tracking
            if progress_service:
                await progress_service.complete_operation(operation_id, "AI analysis completed successfully")
            
            return result
            
        except Exception as e:
            self.logger.error(f"AI analysis failed: {e}")
            
            # Push error notification
            if notification_system:
                from components.ui.notification_system import NotificationType
                notification_system.show_notification(
                    message=f"AI analysis failed: {str(e)}",
                    notification_type=NotificationType.ERROR,
                    title="AI Analysis Error",
                    auto_dismiss=False
                )
            
            # Fail progress tracking
            if progress_service:
                await progress_service.fail_operation(operation_id, f"AI analysis failed: {str(e)}")
            
            return {
                'error': f"AI analysis failed: {str(e)}",
                'financial_insights': 'AI analysis was not available for this report.',
                'recommendations': [],
                'confidence_score': 0.0
            }
    
    async def _generate_reports_parallel(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate reports in parallel for better performance."""
        
        tasks = []
        
        # Create tasks for each format
        if 'PDF' in config.export_formats:
            tasks.append(self._generate_pdf_report(data, output_directory, config))
        
        if 'HTML' in config.export_formats:
            tasks.append(self._generate_html_report(data, output_directory, config))
        
        if 'DOCX' in config.export_formats:
            tasks.append(self._generate_docx_report(data, output_directory, config))
        
        if 'PPTX' in config.export_formats:
            tasks.append(self._generate_pptx_report(data, output_directory, config))
        
        # Execute tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect successful results
        generated_files = {}
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Parallel report generation error: {result}")
            elif isinstance(result, dict):
                generated_files.update(result)
        
        return generated_files
    
    async def _generate_reports_sequential(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate reports sequentially."""
        
        generated_files = {}
        
        # Generate each format sequentially
        if 'PDF' in config.export_formats:
            try:
                pdf_result = await self._generate_pdf_report(data, output_directory, config)
                generated_files.update(pdf_result)
            except Exception as e:
                self.logger.error(f"PDF generation failed: {e}")
        
        if 'HTML' in config.export_formats:
            try:
                html_result = await self._generate_html_report(data, output_directory, config)
                generated_files.update(html_result)
            except Exception as e:
                self.logger.error(f"HTML generation failed: {e}")
        
        if 'DOCX' in config.export_formats:
            try:
                docx_result = await self._generate_docx_report(data, output_directory, config)
                generated_files.update(docx_result)
            except Exception as e:
                self.logger.error(f"DOCX generation failed: {e}")
        
        if 'PPTX' in config.export_formats:
            try:
                pptx_result = await self._generate_pptx_report(data, output_directory, config)
                generated_files.update(pptx_result)
            except Exception as e:
                self.logger.error(f"PPTX generation failed: {e}")
        
        return generated_files
    
    async def _generate_pdf_report(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate professional PDF report."""
        
        output_path = output_directory / f"{data['metadata']['client_name']}_report.pdf"
        
        try:
            # Create PDF configuration
            pdf_config = config.pdf_config or {}
            
            layout_config = PDFLayoutConfig(
                page_size=pdf_config.get('page_size', 'A4'),
                margins=pdf_config.get('margins', {"top": 2.5, "bottom": 2.5, "left": 2.0, "right": 2.0}),
                font_family=pdf_config.get('font_family', 'Helvetica'),
                font_size=pdf_config.get('font_size', 10)
            )
            
            branding_config = PDFBrandingConfig(
                company_name=pdf_config.get('company_name', 'Professional Financial Analysis'),
                primary_color=pdf_config.get('primary_color', '#2E86AB'),
                secondary_color=pdf_config.get('secondary_color', '#A23B72'),
                footer_text=pdf_config.get('footer_text', 'Confidential Financial Analysis Report')
            )
            
            # Generate PDF
            final_path = self.pdf_service.generate_professional_pdf(
                content_data=data,
                output_path=output_path,
                layout_config=layout_config,
                branding_config=branding_config,
                security_level=PDFSecurityLevel.NONE
            )
            
            return {'PDF': final_path}
            
        except Exception as e:
            self.logger.error(f"PDF generation failed: {e}")
            return {}
    
    async def _generate_html_report(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate professional HTML report."""
        
        output_path = output_directory / f"{data['metadata']['client_name']}_report.html"
        
        try:
            # Use template engine for HTML generation
            if config.include_professional_styling:
                html_content = self.template_engine.render_report(
                    template_name='professional_financial_report.html',
                    data=data
                )
            else:
                # Use legacy service
                html_content = self.legacy_export_service.generate_html_report(data)
            
            # Write HTML file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return {'HTML': output_path}
            
        except Exception as e:
            self.logger.error(f"HTML generation failed: {e}")
            return {}
    
    async def _generate_docx_report(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate professional DOCX report."""
        
        output_path = output_directory / f"{data['metadata']['client_name']}_report.docx"
        
        try:
            # Use enhanced export service for DOCX generation
            await self.enhanced_export_service.generate_professional_docx(
                content_data=data,
                output_path=output_path,
                include_ai_analysis=config.include_ai_analysis
            )
            
            return {'DOCX': output_path}
            
        except Exception as e:
            self.logger.error(f"DOCX generation failed: {e}")
            return {}
    
    async def _generate_pptx_report(self, data: Dict[str, Any], output_directory: Path, config: ReportingConfig) -> Dict[str, Path]:
        """Generate professional PPTX report."""
        
        output_path = output_directory / f"{data['metadata']['client_name']}_report.pptx"
        
        try:
            # Use enhanced export service for PPTX generation
            await self.enhanced_export_service.generate_professional_pptx(
                content_data=data,
                output_path=output_path,
                include_ai_analysis=config.include_ai_analysis
            )
            
            return {'PPTX': output_path}
            
        except Exception as e:
            self.logger.error(f"PPTX generation failed: {e}")
            return {}
    
    def get_generation_history(self) -> List[ReportingResult]:
        """Get report generation history."""
        return self.generation_history.copy()
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all services."""
        
        return {
            'ai_service': self.ai_service.get_service_status() if self.ai_service else {'status': 'disabled'},
            'template_engine': self.template_engine.get_engine_status(),
            'enhanced_export': self.enhanced_export_service.get_service_status(),
            'validation_service': self.validation_service.get_service_status(),
            'pdf_service': self.pdf_service.get_pdf_capabilities(),
            'integration_service': {
                'status': 'active',
                'version': '2.0',
                'reports_generated': len(self.generation_history),
                'last_generation': self.generation_history[-1].metadata if self.generation_history else None
            }
        }
    
    def update_ai_configuration(self, ai_config: Dict[str, Any]):
        """Update AI configuration."""
        
        try:
            self.config.ai_config = ai_config
            
            # Reinitialize AI service
            if ai_config.get('provider') and ai_config.get('api_key'):
                ai_llm_config = LLMConfig(
                    provider=ai_config['provider'],
                    api_key=ai_config['api_key'],
                    model_id=ai_config.get('model_id', 'gpt-4'),
                    api_url=ai_config.get('api_url', 'https://api.openai.com/v1/chat/completions'),
                    system_prompt=ai_config.get('system_prompt', ''),
                    max_tokens=ai_config.get('max_tokens', 4000),
                    temperature=ai_config.get('temperature', 0.3)
                )
                self.ai_service = AIAnalysisService(ai_llm_config)
                # Sanity check: ping the AI service to ensure it's working
                try:
                    self.ai_service.ping()
                    self.logger.info("AI service ping successful after configuration update")
                except Exception as e:
                    self.logger.error(f"AI service ping failed after configuration update: {e}")
                    self.ai_service = None
            else:
                self.ai_service = None
            
            self.logger.info("AI configuration updated successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to update AI configuration: {e}")
            raise
    
    def save_configuration(self, config_path: Path):
        """Save current configuration to file."""
        
        try:
            config_data = {
                'mode': self.config.mode.value,
                'include_ai_analysis': self.config.include_ai_analysis,
                'include_professional_styling': self.config.include_professional_styling,
                'include_security_features': self.config.include_security_features,
                'include_charts': self.config.include_charts,
                'include_validation': self.config.include_validation,
                'parallel_processing': self.config.parallel_processing,
                'export_formats': self.config.export_formats,
                'ai_config': self.config.ai_config,
                'pdf_config': self.config.pdf_config,
                'security_config': self.config.security_config,
                'performance_config': self.config.performance_config,
                'saved_at': datetime.now().isoformat()
            }
            
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.logger.info(f"Configuration saved to: {config_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    async def generate_sample_report(self, output_directory: Path) -> ReportingResult:
        """Generate a sample report for testing purposes."""
        
        # Create sample data
        sample_client = ClientProfile(
            company_name="Sample Renewable Energy Corp",
            project_name="Sample Solar Project",
            project_location="Sample Location",
            consultant="AI-Enhanced Analysis System",
            contact_email="<EMAIL>"
        )
        
        sample_assumptions = EnhancedProjectAssumptions(
            capacity_mw=100.0,
            technology_type="Solar PV",
            project_life_years=25,
            capex_meur=80.0,
            opex_meur_per_year=2.0,
            equity_percentage=0.3,
            debt_interest_rate=0.04,
            discount_rate=0.08
        )
        
        return await self.generate_comprehensive_report(
            sample_client,
            sample_assumptions,
            output_directory
        )