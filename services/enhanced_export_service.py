"""
Enhanced Export Service with AI Integration
==========================================

Advanced export service that integrates AI analysis, professional templates,
and enhanced features for PDF, HTML, DOCX, and PPTX generation.
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from datetime import datetime
import base64
import io
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# Import existing services
from .export_service import ExportService
from .ai_analysis_service import AIAnalysisService, LLMConfig, AnalysisRequest
from .professional_template_engine import (
    ProfessionalTemplateEngine, 
    BrandingConfig, 
    LayoutConfig, 
    ContentConfig
)

# Enhanced PDF capabilities
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    from reportlab.graphics.charts.linecharts import HorizontalLineChart
    
    # Enhanced PDF features
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    
    # Digital signature support
    try:
        from reportlab.lib.utils import ImageReader
        from reportlab.graphics.barcode import qr
        ADVANCED_PDF_AVAILABLE = True
    except ImportError:
        ADVANCED_PDF_AVAILABLE = False
    
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    ADVANCED_PDF_AVAILABLE = False

# HTML to PDF conversion
try:
    import weasyprint
    HTML_TO_PDF_AVAILABLE = True
except ImportError:
    HTML_TO_PDF_AVAILABLE = False

# Password protection
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False

# Data validation
from pydantic import BaseModel, validator
from typing import Union
import pandas as pd
import numpy as np

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from utils.file_utils import FileUtils


class ExportValidationModel(BaseModel):
    """Data validation model for export operations."""
    
    client_profile: Dict[str, Any]
    assumptions: Dict[str, Any]
    financial_results: Dict[str, Any]
    export_formats: List[str]
    include_ai_analysis: bool = True
    
    @validator('export_formats')
    def validate_export_formats(cls, v):
        valid_formats = ['pdf', 'html', 'docx', 'pptx', 'excel', 'json']
        for fmt in v:
            if fmt.lower() not in valid_formats:
                raise ValueError(f"Invalid export format: {fmt}")
        return [fmt.lower() for fmt in v]
    
    @validator('financial_results')
    def validate_financial_results(cls, v):
        required_keys = ['kpis']
        for key in required_keys:
            if key not in v:
                raise ValueError(f"Missing required financial data: {key}")
        return v


class EnhancedExportService(ExportService):
    """Enhanced export service with AI integration and advanced features."""
    
    def __init__(self, 
                 ai_config: Optional[LLMConfig] = None,
                 template_engine: Optional[ProfessionalTemplateEngine] = None):
        super().__init__()
        
        self.logger = logging.getLogger(__name__)
        
        # Initialize AI service
        self.ai_service = AIAnalysisService(ai_config) if ai_config else None
        
        # Initialize template engine
        self.template_engine = template_engine or ProfessionalTemplateEngine()
        
        # Performance settings
        self.max_workers = 4
        self.enable_parallel_processing = True
        
        # Security settings
        self.encryption_enabled = ENCRYPTION_AVAILABLE
        self.password_protection_enabled = False
        
        # Cache for AI analysis results
        self.ai_cache = {}
        
        self.logger.info("Enhanced export service initialized")
    
    async def generate_comprehensive_report_enhanced(self,
                                                   client_profile: ClientProfile,
                                                   assumptions: EnhancedProjectAssumptions,
                                                   financial_results: Dict[str, Any],
                                                   export_formats: List[str] = None,
                                                   include_ai_analysis: bool = True,
                                                   branding: Optional[BrandingConfig] = None,
                                                   layout: Optional[LayoutConfig] = None,
                                                   content: Optional[ContentConfig] = None,
                                                   progress_callback: Optional[Callable[[float, str], None]] = None,
                                                   detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None) -> Dict[str, Any]:
        """Generate enhanced comprehensive report with AI analysis and professional templates."""
        
        start_time = datetime.now()
        
        try:
            # Validate input data
            validation_data = ExportValidationModel(
                client_profile=client_profile.to_dict(),
                assumptions=assumptions.to_dict(),
                financial_results=financial_results,
                export_formats=export_formats or ['pdf', 'html', 'docx'],
                include_ai_analysis=include_ai_analysis
            )
            
            if progress_callback:
                progress_callback(5, "Validating input data...")
            
            # Create output directory
            output_dir = self.file_utils.create_timestamped_output_directory(client_profile)
            
            results = {
                'output_directory': output_dir,
                'generated_files': [],
                'ai_analysis': None,
                'processing_time': 0,
                'generation_time': datetime.now().isoformat(),
                'template_engine_version': '2.0',
                'ai_enabled': include_ai_analysis and self.ai_service is not None
            }
            
            # Generate AI analysis if enabled
            ai_analysis = None
            if include_ai_analysis and self.ai_service:
                if progress_callback:
                    progress_callback(10, "Generating AI analysis...")
                
                try:
                    ai_analysis = await self.ai_service.analyze_financial_data(
                        financial_results=financial_results,
                        client_profile=client_profile.to_dict(),
                        assumptions=assumptions.to_dict(),
                        analysis_type="comprehensive"
                    )
                    results['ai_analysis'] = ai_analysis
                    
                    if progress_callback:
                        progress_callback(25, "AI analysis completed")
                
                except Exception as e:
                    self.logger.error(f"AI analysis failed: {str(e)}")
                    if progress_callback:
                        progress_callback(25, "AI analysis failed, continuing without AI insights")
            
            # Prepare enhanced context for templates
            enhanced_context = {
                'client_profile': client_profile.to_dict(),
                'assumptions': assumptions.to_dict(),
                'financial_results': financial_results,
                'ai_analysis': self._format_ai_analysis(ai_analysis) if ai_analysis else None,
                'generation_metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'version': '2.0',
                    'ai_enabled': include_ai_analysis and self.ai_service is not None
                }
            }
            
            # Generate charts with AI analysis
            if progress_callback:
                progress_callback(30, "Generating enhanced charts...")
            
            charts = await self._generate_charts_with_ai_analysis(
                financial_results, 
                ai_analysis, 
                output_dir
            )
            enhanced_context['charts'] = charts
            
            # Generate exports in parallel
            if self.enable_parallel_processing:
                export_tasks = []
                
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    for i, export_format in enumerate(validation_data.export_formats):
                        progress_step = 40 + (i * 40 / len(validation_data.export_formats))
                        
                        if export_format == 'pdf':
                            task = executor.submit(
                                self._generate_enhanced_pdf,
                                enhanced_context,
                                branding,
                                layout,
                                content,
                                output_dir,
                                progress_callback,
                                progress_step
                            )
                        elif export_format == 'html':
                            task = executor.submit(
                                self._generate_enhanced_html,
                                enhanced_context,
                                branding,
                                layout,
                                content,
                                output_dir,
                                progress_callback,
                                progress_step
                            )
                        elif export_format == 'docx':
                            task = executor.submit(
                                self._generate_enhanced_docx,
                                enhanced_context,
                                branding,
                                layout,
                                content,
                                output_dir,
                                progress_callback,
                                progress_step
                            )
                        elif export_format == 'pptx':
                            task = executor.submit(
                                self._generate_enhanced_pptx,
                                enhanced_context,
                                branding,
                                layout,
                                content,
                                output_dir,
                                progress_callback,
                                progress_step
                            )
                        
                        export_tasks.append((export_format, task))
                    
                    # Collect results
                    for format_name, task in export_tasks:
                        try:
                            file_path = task.result()
                            if file_path:
                                results['generated_files'].append((f"{format_name.upper()} Report", file_path))
                                self.logger.info(f"Generated {format_name} report: {file_path}")
                        except Exception as e:
                            self.logger.error(f"Failed to generate {format_name} report: {str(e)}")
            
            else:
                # Sequential processing
                for i, export_format in enumerate(validation_data.export_formats):
                    progress_step = 40 + (i * 40 / len(validation_data.export_formats))
                    
                    try:
                        if export_format == 'pdf':
                            file_path = self._generate_enhanced_pdf(
                                enhanced_context, branding, layout, content, output_dir, progress_callback, progress_step
                            )
                        elif export_format == 'html':
                            file_path = self._generate_enhanced_html(
                                enhanced_context, branding, layout, content, output_dir, progress_callback, progress_step
                            )
                        elif export_format == 'docx':
                            file_path = self._generate_enhanced_docx(
                                enhanced_context, branding, layout, content, output_dir, progress_callback, progress_step
                            )
                        elif export_format == 'pptx':
                            file_path = self._generate_enhanced_pptx(
                                enhanced_context, branding, layout, content, output_dir, progress_callback, progress_step
                            )
                        
                        if file_path:
                            results['generated_files'].append((f"{export_format.upper()} Report", file_path))
                            self.logger.info(f"Generated {export_format} report: {file_path}")
                    
                    except Exception as e:
                        self.logger.error(f"Failed to generate {export_format} report: {str(e)}")
            
            # Generate executive summary
            if progress_callback:
                progress_callback(90, "Generating executive summary...")
            
            if ai_analysis:
                try:
                    executive_summary = await self.ai_service.generate_executive_summary(
                        {'financial_results': financial_results, 'ai_analysis': ai_analysis}
                    )
                    results['executive_summary'] = executive_summary
                except Exception as e:
                    self.logger.error(f"Executive summary generation failed: {str(e)}")
            
            # Calculate processing time
            results['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            if progress_callback:
                progress_callback(100, "Enhanced report generation completed!")
            
            self.logger.info(f"Enhanced comprehensive report generated in {results['processing_time']:.2f} seconds")
            return results
            
        except Exception as e:
            self.logger.error(f"Enhanced report generation failed: {str(e)}")
            raise
    
    def _format_ai_analysis(self, ai_analysis) -> Dict[str, Any]:
        """Format AI analysis for template consumption."""
        if not ai_analysis:
            return None
        
        return {
            'executive_summary': ai_analysis.narrative,
            'financial_insights': ai_analysis.insights.get('financial_performance', ''),
            'risk_assessment': ai_analysis.insights.get('risk_assessment', ''),
            'recommendations': ai_analysis.recommendations,
            'charts_analysis': ai_analysis.charts_analysis,
            'confidence_score': ai_analysis.confidence_score,
            'model_used': ai_analysis.model_used,
            'timestamp': ai_analysis.timestamp.isoformat()
        }
    
    async def _generate_charts_with_ai_analysis(self,
                                              financial_results: Dict[str, Any],
                                              ai_analysis,
                                              output_dir: Dict[str, Path]) -> Dict[str, Any]:
        """Generate charts with AI analysis integration."""
        
        charts = {}
        
        try:
            # Import chart factory
            from components.charts.chart_factory import ChartFactory
            chart_factory = ChartFactory()
            
            # Generate standard charts
            charts_dir = output_dir.get('charts_dir', Path('charts'))
            
            # Financial KPIs chart
            if 'kpis' in financial_results:
                kpis = financial_results['kpis']
                kpi_data = {
                    'Project IRR': kpis.get('IRR_project', 0) * 100,
                    'Equity IRR': kpis.get('IRR_equity', 0) * 100,
                    'NPV (M€)': kpis.get('NPV_project', 0) / 1e6,
                    'LCOE (c€/kWh)': kpis.get('LCOE_eur_kwh', 0) * 100,
                    'Min DSCR': kpis.get('Min_DSCR', 0)
                }
                
                chart_path = charts_dir / "financial_kpis_enhanced.png"
                _, chart_bytes = chart_factory.create_and_export_bar_chart(
                    data=kpi_data,
                    title="Financial Performance Dashboard",
                    save_path=chart_path
                )
                charts['financial_kpis'] = base64.b64encode(chart_bytes).decode('utf-8')
            
            # Cash flow analysis
            if 'cashflow' in financial_results:
                cashflow = financial_results['cashflow']
                if isinstance(cashflow, dict):
                    cashflow_df = pd.DataFrame(cashflow)
                else:
                    cashflow_df = cashflow
                
                if not cashflow_df.empty:
                    cash_flow_columns = ['Free_Cash_Flow_Project', 'Free_Cash_Flow_Equity']
                    available_columns = [col for col in cash_flow_columns if col in cashflow_df.columns]
                    
                    if available_columns:
                        chart_path = charts_dir / "cash_flow_analysis_enhanced.png"
                        _, chart_bytes = chart_factory.create_and_export_line_chart(
                            data=cashflow_df,
                            title="Cash Flow Analysis",
                            x_column='Year',
                            y_columns=available_columns,
                            save_path=chart_path
                        )
                        charts['cash_flow_analysis'] = base64.b64encode(chart_bytes).decode('utf-8')
            
            # Generate AI analysis for charts if available
            if ai_analysis and self.ai_service:
                try:
                    chart_types = list(charts.keys())
                    charts_analysis = await self.ai_service.analyze_charts(
                        charts_data=charts,
                        chart_types=chart_types
                    )
                    
                    # Add AI analysis to charts
                    for chart_type, analysis in charts_analysis.items():
                        if chart_type in charts:
                            charts[f"{chart_type}_ai_analysis"] = analysis
                
                except Exception as e:
                    self.logger.error(f"Chart AI analysis failed: {str(e)}")
            
            return charts
            
        except Exception as e:
            self.logger.error(f"Chart generation failed: {str(e)}")
            return {}
    
    def _generate_enhanced_pdf(self,
                              context: Dict[str, Any],
                              branding: Optional[BrandingConfig],
                              layout: Optional[LayoutConfig],
                              content: Optional[ContentConfig],
                              output_dir: Dict[str, Path],
                              progress_callback: Optional[Callable[[float, str], None]],
                              progress_step: float) -> Optional[Path]:
        """Generate enhanced PDF with AI analysis and professional templates."""
        
        try:
            if progress_callback:
                progress_callback(progress_step, "Generating enhanced PDF...")
            
            # Generate HTML first using template engine
            html_content = self.template_engine.render_report(
                template_name="pdf_report.html",
                data=context,
                branding=branding,
                layout=layout,
                content=content,
                ai_analysis=context.get('ai_analysis')
            )
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_financial_report_{timestamp}.pdf"
            filepath = output_dir['reports_dir'] / filename
            
            # Convert HTML to PDF using WeasyPrint if available
            if HTML_TO_PDF_AVAILABLE:
                try:
                    weasyprint.HTML(string=html_content).write_pdf(str(filepath))
                    
                    # Add password protection if enabled
                    if self.password_protection_enabled and ENCRYPTION_AVAILABLE:
                        self._add_pdf_password_protection(filepath)
                    
                    return filepath
                    
                except Exception as e:
                    self.logger.error(f"WeasyPrint PDF generation failed: {str(e)}")
            
            # Fallback to ReportLab
            if PDF_AVAILABLE:
                return self._generate_reportlab_pdf(context, filepath, branding, layout)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Enhanced PDF generation failed: {str(e)}")
            return None
    
    def _generate_enhanced_html(self,
                               context: Dict[str, Any],
                               branding: Optional[BrandingConfig],
                               layout: Optional[LayoutConfig],
                               content: Optional[ContentConfig],
                               output_dir: Dict[str, Path],
                               progress_callback: Optional[Callable[[float, str], None]],
                               progress_step: float) -> Optional[Path]:
        """Generate enhanced HTML with AI analysis and professional templates."""
        
        try:
            if progress_callback:
                progress_callback(progress_step, "Generating enhanced HTML...")
            
            # Generate HTML content using template engine
            html_content = self.template_engine.render_report(
                template_name="html_report.html",
                data=context,
                branding=branding,
                layout=layout,
                content=content,
                ai_analysis=context.get('ai_analysis')
            )
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_financial_report_{timestamp}.html"
            filepath = output_dir['reports_dir'] / filename
            
            # Write HTML file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Enhanced HTML generation failed: {str(e)}")
            return None
    
    def _generate_enhanced_docx(self,
                               context: Dict[str, Any],
                               branding: Optional[BrandingConfig],
                               layout: Optional[LayoutConfig],
                               content: Optional[ContentConfig],
                               output_dir: Dict[str, Path],
                               progress_callback: Optional[Callable[[float, str], None]],
                               progress_step: float) -> Optional[Path]:
        """Generate enhanced DOCX with AI analysis."""
        
        try:
            if progress_callback:
                progress_callback(progress_step, "Generating enhanced DOCX...")
            
            # Use existing DOCX functionality with AI enhancements
            client_profile = ClientProfile(**context['client_profile'])
            assumptions = EnhancedProjectAssumptions(**context['assumptions'])
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_financial_report_{timestamp}.docx"
            filepath = output_dir['reports_dir'] / filename
            
            # Create enhanced DOCX with AI analysis
            docx_path = self._create_enhanced_docx(
                client_profile=client_profile,
                assumptions=assumptions,
                financial_results=context['financial_results'],
                ai_analysis=context.get('ai_analysis'),
                charts=context.get('charts', {}),
                output_path=filepath,
                branding=branding
            )
            
            return docx_path
            
        except Exception as e:
            self.logger.error(f"Enhanced DOCX generation failed: {str(e)}")
            return None
    
    def _generate_enhanced_pptx(self,
                               context: Dict[str, Any],
                               branding: Optional[BrandingConfig],
                               layout: Optional[LayoutConfig],
                               content: Optional[ContentConfig],
                               output_dir: Dict[str, Path],
                               progress_callback: Optional[Callable[[float, str], None]],
                               progress_step: float) -> Optional[Path]:
        """Generate enhanced PPTX with AI analysis."""
        
        try:
            if progress_callback:
                progress_callback(progress_step, "Generating enhanced PPTX...")
            
            # Use existing PPTX functionality with AI enhancements
            client_profile = ClientProfile(**context['client_profile'])
            assumptions = EnhancedProjectAssumptions(**context['assumptions'])
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_financial_presentation_{timestamp}.pptx"
            filepath = output_dir['reports_dir'] / filename
            
            # Create enhanced PPTX with AI analysis
            pptx_path = self._create_enhanced_pptx(
                client_profile=client_profile,
                assumptions=assumptions,
                financial_results=context['financial_results'],
                ai_analysis=context.get('ai_analysis'),
                charts=context.get('charts', {}),
                output_path=filepath,
                branding=branding
            )
            
            return pptx_path
            
        except Exception as e:
            self.logger.error(f"Enhanced PPTX generation failed: {str(e)}")
            return None
    
    def _create_enhanced_docx(self,
                             client_profile: ClientProfile,
                             assumptions: EnhancedProjectAssumptions,
                             financial_results: Dict[str, Any],
                             ai_analysis: Optional[Dict[str, Any]],
                             charts: Dict[str, Any],
                             output_path: Path,
                             branding: Optional[BrandingConfig]) -> Path:
        """Create enhanced DOCX with AI analysis integration."""
        
        try:
            from docx import Document
            from docx.shared import Inches, Pt, RGBColor
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            
            # Create document
            doc = Document()
            
            # Add title with branding
            title = doc.add_heading('Enhanced Financial Analysis Report', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add project information
            doc.add_heading('Project Information', level=1)
            
            info_table = doc.add_table(rows=6, cols=2)
            info_table.style = 'Table Grid'
            
            info_data = [
                ('Client Company', client_profile.company_name),
                ('Project Name', client_profile.project_name),
                ('Capacity', f"{assumptions.capacity_mw} MW"),
                ('Location', client_profile.project_location or 'Not specified'),
                ('Report Date', client_profile.report_date),
                ('Analysis Type', 'AI-Enhanced Financial Analysis')
            ]
            
            for i, (label, value) in enumerate(info_data):
                info_table.cell(i, 0).text = label
                info_table.cell(i, 1).text = str(value)
            
            # Add AI Executive Summary if available
            if ai_analysis and ai_analysis.get('executive_summary'):
                doc.add_heading('AI Executive Summary', level=1)
                doc.add_paragraph(ai_analysis['executive_summary'])
            
            # Add financial results
            doc.add_heading('Financial Performance', level=1)
            
            kpis = financial_results.get('kpis', {})
            
            # KPI table
            kpi_table = doc.add_table(rows=len(kpis) + 1, cols=3)
            kpi_table.style = 'Table Grid'
            
            # Header
            kpi_table.cell(0, 0).text = 'Metric'
            kpi_table.cell(0, 1).text = 'Value'
            kpi_table.cell(0, 2).text = 'AI Assessment'
            
            # Data rows
            for i, (key, value) in enumerate(kpis.items(), 1):
                kpi_table.cell(i, 0).text = key.replace('_', ' ').title()
                
                if 'IRR' in key:
                    kpi_table.cell(i, 1).text = f"{value:.1%}"
                elif 'NPV' in key:
                    kpi_table.cell(i, 1).text = f"€{value/1e6:.2f}M"
                elif 'LCOE' in key:
                    kpi_table.cell(i, 1).text = f"{value:.3f} €/kWh"
                else:
                    kpi_table.cell(i, 1).text = f"{value:.2f}"
                
                # Add AI assessment if available
                if ai_analysis and ai_analysis.get('financial_insights'):
                    kpi_table.cell(i, 2).text = self._get_ai_kpi_assessment(key, value, ai_analysis)
                else:
                    kpi_table.cell(i, 2).text = "Standard analysis"
            
            # Add AI recommendations if available
            if ai_analysis and ai_analysis.get('recommendations'):
                doc.add_heading('AI Recommendations', level=1)
                
                for recommendation in ai_analysis['recommendations']:
                    doc.add_paragraph(f"• {recommendation}")
            
            # Add charts section
            if charts:
                doc.add_heading('Visual Analysis', level=1)
                
                for chart_name, chart_data in charts.items():
                    if chart_name.endswith('_ai_analysis'):
                        continue
                    
                    doc.add_heading(chart_name.replace('_', ' ').title(), level=2)
                    
                    # Add chart image
                    if isinstance(chart_data, str):
                        # Base64 encoded image
                        try:
                            chart_bytes = base64.b64decode(chart_data)
                            temp_path = Path(f"temp_{chart_name}.png")
                            
                            with open(temp_path, 'wb') as f:
                                f.write(chart_bytes)
                            
                            doc.add_picture(str(temp_path), width=Inches(6))
                            temp_path.unlink()  # Delete temporary file
                            
                        except Exception as e:
                            doc.add_paragraph(f"Chart could not be embedded: {str(e)}")
                    
                    # Add AI analysis for this chart
                    ai_analysis_key = f"{chart_name}_ai_analysis"
                    if ai_analysis and ai_analysis_key in charts:
                        doc.add_paragraph("AI Analysis:")
                        doc.add_paragraph(charts[ai_analysis_key])
            
            # Save document
            doc.save(str(output_path))
            return output_path
            
        except Exception as e:
            self.logger.error(f"Enhanced DOCX creation failed: {str(e)}")
            raise
    
    def _create_enhanced_pptx(self,
                             client_profile: ClientProfile,
                             assumptions: EnhancedProjectAssumptions,
                             financial_results: Dict[str, Any],
                             ai_analysis: Optional[Dict[str, Any]],
                             charts: Dict[str, Any],
                             output_path: Path,
                             branding: Optional[BrandingConfig]) -> Path:
        """Create enhanced PPTX with AI analysis integration."""
        
        try:
            from pptx import Presentation
            from pptx.util import Inches as PPTXInches, Pt as PPTXPt
            from pptx.enum.text import PP_ALIGN
            
            # Create presentation
            prs = Presentation()
            
            # Slide 1: Title slide
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            
            title.text = f"Enhanced Financial Analysis\n{client_profile.project_name}"
            subtitle.text = f"""
{client_profile.company_name}
{assumptions.capacity_mw} MW {assumptions.technology_type} Project
{datetime.now().strftime('%B %Y')}

AI-Enhanced Analysis
Prepared by: {client_profile.consultant}
"""
            
            # Slide 2: AI Executive Summary
            if ai_analysis and ai_analysis.get('executive_summary'):
                bullet_slide_layout = prs.slide_layouts[1]
                slide = prs.slides.add_slide(bullet_slide_layout)
                title = slide.shapes.title
                content = slide.placeholders[1]
                
                title.text = "AI Executive Summary"
                content.text = ai_analysis['executive_summary'][:500] + "..." if len(ai_analysis['executive_summary']) > 500 else ai_analysis['executive_summary']
            
            # Slide 3: Financial Performance with AI insights
            bullet_slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(bullet_slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]
            
            title.text = "Financial Performance - AI Analysis"
            
            kpis = financial_results.get('kpis', {})
            content_text = "Key Financial Metrics:\n"
            
            for key, value in kpis.items():
                if 'IRR' in key:
                    content_text += f"• {key.replace('_', ' ')}: {value:.1%}\n"
                elif 'NPV' in key:
                    content_text += f"• {key.replace('_', ' ')}: €{value/1e6:.1f}M\n"
                elif 'LCOE' in key:
                    content_text += f"• {key.replace('_', ' ')}: {value:.3f} €/kWh\n"
                else:
                    content_text += f"• {key.replace('_', ' ')}: {value:.2f}\n"
            
            if ai_analysis and ai_analysis.get('financial_insights'):
                content_text += f"\nAI Insights:\n{ai_analysis['financial_insights'][:200]}..."
            
            content.text = content_text
            
            # Slide 4: AI Recommendations
            if ai_analysis and ai_analysis.get('recommendations'):
                slide = prs.slides.add_slide(bullet_slide_layout)
                title = slide.shapes.title
                content = slide.placeholders[1]
                
                title.text = "AI-Generated Recommendations"
                
                recommendations_text = "Strategic Recommendations:\n"
                for i, recommendation in enumerate(ai_analysis['recommendations'][:5], 1):
                    recommendations_text += f"{i}. {recommendation}\n"
                
                content.text = recommendations_text
            
            # Add chart slides with AI analysis
            if charts:
                chart_slide_layout = prs.slide_layouts[5]  # Blank layout
                
                for chart_name, chart_data in charts.items():
                    if chart_name.endswith('_ai_analysis'):
                        continue
                    
                    slide = prs.slides.add_slide(chart_slide_layout)
                    
                    # Add title
                    title_shape = slide.shapes.add_textbox(
                        PPTXInches(0.5), PPTXInches(0.5),
                        PPTXInches(9), PPTXInches(1)
                    )
                    title_frame = title_shape.text_frame
                    title_frame.text = chart_name.replace('_', ' ').title()
                    title_frame.paragraphs[0].font.size = PPTXPt(24)
                    title_frame.paragraphs[0].font.bold = True
                    
                    # Add chart image
                    if isinstance(chart_data, str):
                        try:
                            chart_bytes = base64.b64decode(chart_data)
                            img_stream = io.BytesIO(chart_bytes)
                            slide.shapes.add_picture(
                                img_stream,
                                PPTXInches(1), PPTXInches(1.5),
                                PPTXInches(8), PPTXInches(4)
                            )
                            
                            # Add AI analysis text box
                            ai_analysis_key = f"{chart_name}_ai_analysis"
                            if charts.get(ai_analysis_key):
                                ai_text_shape = slide.shapes.add_textbox(
                                    PPTXInches(1), PPTXInches(6),
                                    PPTXInches(8), PPTXInches(1.5)
                                )
                                ai_text_frame = ai_text_shape.text_frame
                                ai_text_frame.text = f"AI Analysis: {charts[ai_analysis_key][:150]}..."
                                ai_text_frame.paragraphs[0].font.size = PPTXPt(12)
                            
                        except Exception as e:
                            # Add error text
                            error_shape = slide.shapes.add_textbox(
                                PPTXInches(1), PPTXInches(2),
                                PPTXInches(8), PPTXInches(1)
                            )
                            error_frame = error_shape.text_frame
                            error_frame.text = f"Chart could not be displayed: {str(e)}"
            
            # Save presentation
            prs.save(str(output_path))
            return output_path
            
        except Exception as e:
            self.logger.error(f"Enhanced PPTX creation failed: {str(e)}")
            raise
    
    def _get_ai_kpi_assessment(self, kpi_name: str, kpi_value: float, ai_analysis: Dict[str, Any]) -> str:
        """Get AI assessment for a specific KPI."""
        
        # Simple assessment based on common financial thresholds
        if 'IRR' in kpi_name:
            if kpi_value > 0.15:
                return "Excellent return"
            elif kpi_value > 0.10:
                return "Good return"
            else:
                return "Below target"
        
        elif 'NPV' in kpi_name:
            if kpi_value > 0:
                return "Positive value creation"
            else:
                return "Value destruction"
        
        elif 'LCOE' in kpi_name:
            if kpi_value < 0.045:
                return "Competitive cost"
            elif kpi_value < 0.060:
                return "Moderate cost"
            else:
                return "High cost"
        
        elif 'DSCR' in kpi_name:
            if kpi_value > 1.25:
                return "Strong coverage"
            elif kpi_value > 1.0:
                return "Adequate coverage"
            else:
                return "Weak coverage"
        
        return "Standard analysis"
    
    def _add_pdf_password_protection(self, pdf_path: Path, password: str = "financial_report"):
        """Add password protection to PDF file."""
        
        try:
            if not ENCRYPTION_AVAILABLE:
                self.logger.warning("PDF password protection not available - missing cryptography library")
                return
            
            # This would require additional PDF encryption libraries
            # For now, just log the intent
            self.logger.info(f"Password protection would be added to: {pdf_path}")
            
        except Exception as e:
            self.logger.error(f"PDF password protection failed: {str(e)}")
    
    def _generate_reportlab_pdf(self,
                               context: Dict[str, Any],
                               filepath: Path,
                               branding: Optional[BrandingConfig],
                               layout: Optional[LayoutConfig]) -> Path:
        """Generate PDF using ReportLab as fallback."""
        
        try:
            doc = SimpleDocTemplate(str(filepath), pagesize=A4)
            story = []
            
            # Get styles
            styles = getSampleStyleSheet()
            
            # Create custom styles with branding
            primary_color = branding.primary_color if branding else "#2E86AB"
            
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.HexColor(primary_color)
            )
            
            # Add title
            client_profile = context['client_profile']
            story.append(Paragraph(f"Enhanced Financial Analysis Report", title_style))
            story.append(Paragraph(f"<b>{client_profile.get('project_name', 'Financial Project')}</b>", styles['Heading2']))
            story.append(Spacer(1, 20))
            
            # Add AI summary if available
            ai_analysis = context.get('ai_analysis')
            if ai_analysis and ai_analysis.get('executive_summary'):
                story.append(Paragraph("AI Executive Summary", styles['Heading2']))
                story.append(Paragraph(ai_analysis['executive_summary'], styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Add financial results
            story.append(Paragraph("Financial Performance", styles['Heading2']))
            
            financial_results = context['financial_results']
            kpis = financial_results.get('kpis', {})
            
            # Create KPI table
            kpi_data = [['Metric', 'Value', 'Status']]
            
            for key, value in kpis.items():
                formatted_value = ""
                if 'IRR' in key:
                    formatted_value = f"{value:.1%}"
                elif 'NPV' in key:
                    formatted_value = f"€{value/1e6:.2f}M"
                elif 'LCOE' in key:
                    formatted_value = f"{value:.3f} €/kWh"
                else:
                    formatted_value = f"{value:.2f}"
                
                status = "✓" if value > 0 else "⚠"
                kpi_data.append([key.replace('_', ' ').title(), formatted_value, status])
            
            kpi_table = Table(kpi_data, colWidths=[3*inch, 2*inch, 1*inch])
            kpi_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(primary_color)),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(kpi_table)
            story.append(Spacer(1, 20))
            
            # Add AI recommendations
            if ai_analysis and ai_analysis.get('recommendations'):
                story.append(Paragraph("AI Recommendations", styles['Heading2']))
                
                for recommendation in ai_analysis['recommendations']:
                    story.append(Paragraph(f"• {recommendation}", styles['Normal']))
                
                story.append(Spacer(1, 20))
            
            # Add footer
            story.append(Spacer(1, 30))
            story.append(Paragraph("Enhanced Financial Analysis Report", styles['Normal']))
            story.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
            
            # Build PDF
            doc.build(story)
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"ReportLab PDF generation failed: {str(e)}")
            raise
    
    def update_ai_config(self, ai_config: LLMConfig):
        """Update AI configuration."""
        if self.ai_service:
            self.ai_service.update_config(ai_config)
        else:
            self.ai_service = AIAnalysisService(ai_config)
        
        self.logger.info("AI configuration updated")
    
    def get_ai_status(self) -> Dict[str, Any]:
        """Get AI service status."""
        if not self.ai_service:
            return {'status': 'disabled', 'message': 'AI service not configured'}
        
        return self.ai_service.get_provider_status()
    
    async def test_ai_connection(self) -> Dict[str, Any]:
        """Test AI service connection."""
        if not self.ai_service:
            return {'status': 'error', 'message': 'AI service not configured'}
        
        return await self.ai_service.test_connection()
    
    def clear_ai_cache(self):
        """Clear AI analysis cache."""
        if self.ai_service:
            self.ai_service.clear_cache()
        
        self.ai_cache.clear()
        self.logger.info("AI cache cleared")
    
    def get_export_capabilities(self) -> Dict[str, Any]:
        """Get current export capabilities."""
        return {
            'pdf_available': PDF_AVAILABLE,
            'html_to_pdf_available': HTML_TO_PDF_AVAILABLE,
            'advanced_pdf_available': ADVANCED_PDF_AVAILABLE,
            'encryption_available': ENCRYPTION_AVAILABLE,
            'ai_available': self.ai_service is not None,
            'parallel_processing': self.enable_parallel_processing,
            'max_workers': self.max_workers,
            'template_engine': 'Professional Template Engine 2.0'
        }
    
    def set_performance_settings(self, 
                                enable_parallel: bool = True,
                                max_workers: int = 4):
        """Configure performance settings."""
        self.enable_parallel_processing = enable_parallel
        self.max_workers = max_workers
        
        self.logger.info(f"Performance settings updated: parallel={enable_parallel}, workers={max_workers}")
    
    def set_security_settings(self, 
                             enable_encryption: bool = False,
                             enable_password_protection: bool = False):
        """Configure security settings."""
        self.encryption_enabled = enable_encryption and ENCRYPTION_AVAILABLE
        self.password_protection_enabled = enable_password_protection and ENCRYPTION_AVAILABLE
        
        self.logger.info(f"Security settings updated: encryption={self.encryption_enabled}, password={self.password_protection_enabled}")