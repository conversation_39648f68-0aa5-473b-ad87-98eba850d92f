"""
Advanced PDF Service
====================

Professional PDF generation with digital signatures, password protection,
watermarks, and advanced formatting features.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from datetime import datetime
import io
import base64
import hashlib
import os

# PDF generation libraries
try:
    from reportlab.lib.pagesizes import letter, A4, LETTER
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, mm, cm
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
    from reportlab.pdfgen import canvas
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.utils import ImageReader
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.barcharts import VerticalBar<PERSON>hart
    from reportlab.graphics.charts.linecharts import <PERSON>tal<PERSON>ine<PERSON><PERSON>
    from reportlab.graphics.charts.piecharts import Pie
    from reportlab.graphics.barcode import qr
    from reportlab.graphics.barcode.code128 import Code128
    
    # Advanced PDF features
    from reportlab.pdfgen.canvas import Canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus.flowables import Flowable
    from reportlab.platypus.tableofcontents import TableOfContents
    from reportlab.lib.styles import ParagraphStyle as PS
    
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

# Digital signature support
try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.primitives.serialization import load_pem_private_key
    from cryptography.x509 import load_pem_x509_certificate
    import cryptography
    DIGITAL_SIGNATURE_AVAILABLE = True
except ImportError:
    DIGITAL_SIGNATURE_AVAILABLE = False

# Password protection
try:
    from PyPDF2 import PdfWriter, PdfReader
    from PyPDF2.generic import DictionaryObject, DecodedStreamObject
    PASSWORD_PROTECTION_AVAILABLE = True
except ImportError:
    try:
        from pypdf import PdfWriter, PdfReader
        PASSWORD_PROTECTION_AVAILABLE = True
    except ImportError:
        PASSWORD_PROTECTION_AVAILABLE = False

# Watermark support
try:
    from PIL import Image as PILImage, ImageDraw, ImageFont
    import textwrap
    WATERMARK_AVAILABLE = True
except ImportError:
    WATERMARK_AVAILABLE = False

from dataclasses import dataclass, field
from enum import Enum


class PDFSecurityLevel(Enum):
    """PDF security levels."""
    NONE = "none"
    PASSWORD_ONLY = "password_only"
    DIGITAL_SIGNATURE = "digital_signature"
    FULL_SECURITY = "full_security"


@dataclass
class PDFSignatureConfig:
    """Configuration for PDF digital signatures."""
    certificate_path: Optional[str] = None
    private_key_path: Optional[str] = None
    password: Optional[str] = None
    reason: str = "Financial Report Authentication"
    location: str = "Professional Financial Analysis System"
    contact_info: str = "Generated by AI-Enhanced Financial Modeling"
    
    
@dataclass
class PDFWatermarkConfig:
    """Configuration for PDF watermarks."""
    text: str = "CONFIDENTIAL"
    opacity: float = 0.3
    rotation: int = 45
    font_size: int = 50
    color: str = "#CCCCCC"
    position: str = "center"  # center, diagonal, header, footer


@dataclass
class PDFLayoutConfig:
    """Configuration for PDF layout."""
    page_size: str = "A4"
    margins: Dict[str, float] = field(default_factory=lambda: {
        "top": 2.5, "bottom": 2.5, "left": 2.0, "right": 2.0
    })
    header_height: float = 1.5
    footer_height: float = 1.0
    font_family: str = "Helvetica"
    font_size: int = 10
    line_spacing: float = 1.2
    paragraph_spacing: float = 0.5


@dataclass
class PDFBrandingConfig:
    """Configuration for PDF branding."""
    company_name: str = "Professional Financial Analysis"
    logo_path: Optional[str] = None
    primary_color: str = "#2E86AB"
    secondary_color: str = "#A23B72"
    accent_color: str = "#F18F01"
    footer_text: str = "Confidential Financial Analysis Report"
    
    
class CustomTableOfContents(TableOfContents):
    """Custom table of contents with professional styling."""
    
    def __init__(self):
        super().__init__()
        self.levelStyles = [
            PS(name='TOCHeading1', fontSize=14, leftIndent=0, fontName='Helvetica-Bold'),
            PS(name='TOCHeading2', fontSize=12, leftIndent=20, fontName='Helvetica'),
            PS(name='TOCHeading3', fontSize=10, leftIndent=40, fontName='Helvetica'),
        ]


class WatermarkFlowable(Flowable):
    """Custom flowable for watermarks."""
    
    def __init__(self, text: str, config: PDFWatermarkConfig):
        super().__init__()
        self.text = text
        self.config = config
        self.width = 0
        self.height = 0
    
    def draw(self):
        """Draw watermark on the page."""
        canvas = self.canv
        canvas.saveState()
        
        # Set transparency
        canvas.setFillAlpha(self.config.opacity)
        
        # Set color
        canvas.setFillColor(colors.HexColor(self.config.color))
        
        # Set font
        canvas.setFont("Helvetica-Bold", self.config.font_size)
        
        # Get page dimensions
        page_width = canvas._pagesize[0]
        page_height = canvas._pagesize[1]
        
        # Position watermark
        if self.config.position == "center":
            x = page_width / 2
            y = page_height / 2
        elif self.config.position == "diagonal":
            x = page_width / 2
            y = page_height / 2
        else:  # header or footer
            x = page_width / 2
            y = page_height - 50 if self.config.position == "header" else 50
        
        # Draw rotated text
        canvas.rotate(self.config.rotation)
        canvas.drawCentredText(x, y, self.text)
        
        canvas.restoreState()


class HeaderFooterCanvas(Canvas):
    """Custom canvas with header and footer."""
    
    def __init__(self, *args, branding_config: PDFBrandingConfig, **kwargs):
        super().__init__(*args, **kwargs)
        self.branding_config = branding_config
        self.page_count = 0
    
    def showPage(self):
        """Override showPage to add header and footer."""
        self.page_count += 1
        self._draw_header()
        self._draw_footer()
        super().showPage()
    
    def _draw_header(self):
        """Draw header on each page."""
        self.saveState()
        
        # Header line
        self.setStrokeColor(colors.HexColor(self.branding_config.primary_color))
        self.setLineWidth(2)
        self.line(50, A4[1] - 50, A4[0] - 50, A4[1] - 50)
        
        # Company name
        self.setFont("Helvetica-Bold", 12)
        self.setFillColor(colors.HexColor(self.branding_config.primary_color))
        self.drawString(50, A4[1] - 35, self.branding_config.company_name)
        
        # Date
        self.setFont("Helvetica", 10)
        self.setFillColor(colors.black)
        date_str = datetime.now().strftime("%Y-%m-%d")
        self.drawRightString(A4[0] - 50, A4[1] - 35, date_str)
        
        self.restoreState()
    
    def _draw_footer(self):
        """Draw footer on each page."""
        self.saveState()
        
        # Footer line
        self.setStrokeColor(colors.HexColor(self.branding_config.primary_color))
        self.setLineWidth(1)
        self.line(50, 50, A4[0] - 50, 50)
        
        # Footer text
        self.setFont("Helvetica", 9)
        self.setFillColor(colors.grey)
        self.drawString(50, 35, self.branding_config.footer_text)
        
        # Page number
        self.drawRightString(A4[0] - 50, 35, f"Page {self.page_count}")
        
        self.restoreState()


class AdvancedPDFService:
    """Advanced PDF service with professional features."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Check available features
        self.pdf_available = PDF_AVAILABLE
        self.signature_available = DIGITAL_SIGNATURE_AVAILABLE
        self.password_available = PASSWORD_PROTECTION_AVAILABLE
        self.watermark_available = WATERMARK_AVAILABLE
        
        # Default configurations
        self.default_layout = PDFLayoutConfig()
        self.default_branding = PDFBrandingConfig()
        self.default_watermark = PDFWatermarkConfig()
        
        # Register custom fonts if available
        self._register_custom_fonts()
        
        self.logger.info(f"Advanced PDF service initialized - Features: PDF={self.pdf_available}, Signature={self.signature_available}, Password={self.password_available}, Watermark={self.watermark_available}")
    
    def _register_custom_fonts(self):
        """Register custom fonts for better typography."""
        try:
            # Register common fonts if available
            font_paths = [
                "/System/Library/Fonts/Helvetica.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
                "C:/Windows/Fonts/arial.ttf"  # Windows
            ]
            
            for font_path in font_paths:
                if Path(font_path).exists():
                    try:
                        pdfmetrics.registerFont(TTFont('CustomFont', font_path))
                        self.logger.info(f"Registered font: {font_path}")
                        break
                    except Exception as e:
                        self.logger.debug(f"Failed to register font {font_path}: {e}")
                        
        except Exception as e:
            self.logger.debug(f"Font registration failed: {e}")
    
    def generate_professional_pdf(self,
                                 content_data: Dict[str, Any],
                                 output_path: Path,
                                 layout_config: Optional[PDFLayoutConfig] = None,
                                 branding_config: Optional[PDFBrandingConfig] = None,
                                 watermark_config: Optional[PDFWatermarkConfig] = None,
                                 signature_config: Optional[PDFSignatureConfig] = None,
                                 password: Optional[str] = None,
                                 security_level: PDFSecurityLevel = PDFSecurityLevel.NONE) -> Path:
        """Generate professional PDF with advanced features."""
        
        if not self.pdf_available:
            raise ImportError("ReportLab is required for PDF generation")
        
        # Use defaults if not provided
        layout_config = layout_config or self.default_layout
        branding_config = branding_config or self.default_branding
        watermark_config = watermark_config or self.default_watermark
        
        try:
            # Create temporary PDF without security features
            temp_path = output_path.with_suffix('.tmp.pdf')
            
            # Generate base PDF
            self._generate_base_pdf(
                content_data, 
                temp_path, 
                layout_config, 
                branding_config, 
                watermark_config
            )
            
            # Apply security features
            final_path = self._apply_security_features(
                temp_path,
                output_path,
                signature_config,
                password,
                security_level
            )
            
            # Clean up temporary file
            if temp_path.exists():
                temp_path.unlink()
            
            self.logger.info(f"Professional PDF generated: {final_path}")
            return final_path
            
        except Exception as e:
            self.logger.error(f"PDF generation failed: {str(e)}")
            raise
    
    def _generate_base_pdf(self,
                          content_data: Dict[str, Any],
                          output_path: Path,
                          layout_config: PDFLayoutConfig,
                          branding_config: PDFBrandingConfig,
                          watermark_config: PDFWatermarkConfig):
        """Generate base PDF with content."""
        
        # Create document with custom canvas
        doc = SimpleDocTemplate(
            str(output_path),
            pagesize=A4,
            topMargin=layout_config.margins['top'] * cm,
            bottomMargin=layout_config.margins['bottom'] * cm,
            leftMargin=layout_config.margins['left'] * cm,
            rightMargin=layout_config.margins['right'] * cm
        )
        
        # Create story (content)
        story = []
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Create custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor(branding_config.primary_color),
            fontName='Helvetica-Bold'
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor(branding_config.primary_color),
            fontName='Helvetica-Bold'
        )
        
        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            parent=styles['Heading3'],
            fontSize=14,
            spaceAfter=10,
            textColor=colors.HexColor(branding_config.secondary_color),
            fontName='Helvetica-Bold'
        )
        
        body_style = ParagraphStyle(
            'CustomBody',
            parent=styles['Normal'],
            fontSize=layout_config.font_size,
            spaceAfter=layout_config.paragraph_spacing * cm,
            alignment=TA_JUSTIFY,
            fontName=layout_config.font_family
        )
        
        # Add cover page
        story.extend(self._create_cover_page(content_data, branding_config, title_style))
        
        # Add table of contents
        if layout_config.margins.get('include_toc', True):
            story.append(PageBreak())
            story.extend(self._create_table_of_contents())
        
        # Add executive summary
        story.append(PageBreak())
        story.extend(self._create_executive_summary(content_data, heading_style, body_style))
        
        # Add financial analysis
        story.append(PageBreak())
        story.extend(self._create_financial_analysis(content_data, heading_style, subheading_style, body_style))
        
        # Add charts section
        if content_data.get('charts'):
            story.append(PageBreak())
            story.extend(self._create_charts_section(content_data, heading_style, subheading_style))
        
        # Add AI analysis section
        if content_data.get('ai_analysis'):
            story.append(PageBreak())
            story.extend(self._create_ai_analysis_section(content_data, heading_style, subheading_style, body_style))
        
        # Add risk analysis
        story.append(PageBreak())
        story.extend(self._create_risk_analysis(content_data, heading_style, subheading_style, body_style))
        
        # Add appendices
        story.append(PageBreak())
        story.extend(self._create_appendices(content_data, heading_style, body_style))
        
        # Add watermark if configured
        if watermark_config.text:
            story.insert(0, WatermarkFlowable(watermark_config.text, watermark_config))
        
        # Build PDF with custom canvas
        doc.build(
            story,
            canvasmaker=lambda *args, **kwargs: HeaderFooterCanvas(
                *args, branding_config=branding_config, **kwargs
            )
        )
    
    def _create_cover_page(self,
                          content_data: Dict[str, Any],
                          branding_config: PDFBrandingConfig,
                          title_style: ParagraphStyle) -> List:
        """Create professional cover page."""
        
        cover_content = []
        
        # Add logo if available
        if branding_config.logo_path and Path(branding_config.logo_path).exists():
            try:
                logo = Image(branding_config.logo_path, width=2*inch, height=1*inch)
                logo.hAlign = 'CENTER'
                cover_content.append(logo)
                cover_content.append(Spacer(1, 20))
            except Exception as e:
                self.logger.warning(f"Could not add logo: {e}")
        
        # Title
        client_profile = content_data.get('client_profile', {})
        project_name = client_profile.get('project_name', 'Financial Analysis Project')
        
        cover_content.append(Paragraph("Professional Financial Analysis Report", title_style))
        cover_content.append(Spacer(1, 20))
        cover_content.append(Paragraph(f"<b>{project_name}</b>", title_style))
        cover_content.append(Spacer(1, 40))
        
        # Project details table
        project_details = [
            ['Client Company', client_profile.get('company_name', 'Not specified')],
            ['Project Capacity', f"{content_data.get('assumptions', {}).get('capacity_mw', 'N/A')} MW"],
            ['Technology', content_data.get('assumptions', {}).get('technology_type', 'Renewable Energy')],
            ['Location', client_profile.get('project_location', 'To be determined')],
            ['Analysis Date', datetime.now().strftime('%B %d, %Y')],
            ['Consultant', client_profile.get('consultant', 'Professional Analysis Team')]
        ]
        
        details_table = Table(project_details, colWidths=[2.5*inch, 3.5*inch])
        details_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(branding_config.primary_color)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP')
        ]))
        
        cover_content.append(details_table)
        cover_content.append(Spacer(1, 40))
        
        # Key metrics preview
        financial_results = content_data.get('financial_results', {})
        kpis = financial_results.get('kpis', {})
        
        if kpis:
            cover_content.append(Paragraph("Key Performance Indicators", title_style))
            cover_content.append(Spacer(1, 20))
            
            kpi_data = [
                ['Metric', 'Value', 'Assessment'],
                ['Project IRR', f"{kpis.get('IRR_project', 0):.1%}", self._get_kpi_assessment(kpis.get('IRR_project', 0), 'IRR')],
                ['NPV Project', f"€{kpis.get('NPV_project', 0)/1e6:.1f}M", self._get_kpi_assessment(kpis.get('NPV_project', 0), 'NPV')],
                ['LCOE', f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh", self._get_kpi_assessment(kpis.get('LCOE_eur_kwh', 0), 'LCOE')],
                ['Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}", self._get_kpi_assessment(kpis.get('Min_DSCR', 0), 'DSCR')]
            ]
            
            kpi_table = Table(kpi_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
            kpi_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(branding_config.secondary_color)),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP')
            ]))
            
            cover_content.append(kpi_table)
        
        cover_content.append(Spacer(1, 60))
        
        # Disclaimer
        disclaimer_text = """
        This report contains confidential and proprietary information. 
        It is intended solely for the use of the client and authorized personnel.
        Distribution or disclosure to third parties is prohibited without written consent.
        """
        
        disclaimer_style = ParagraphStyle(
            'Disclaimer',
            fontSize=9,
            textColor=colors.grey,
            alignment=TA_CENTER,
            spaceAfter=10
        )
        
        cover_content.append(Paragraph(disclaimer_text, disclaimer_style))
        
        return cover_content
    
    def _create_table_of_contents(self) -> List:
        """Create table of contents."""
        toc_content = []
        
        # TOC title
        toc_title = ParagraphStyle(
            'TOCTitle',
            fontSize=18,
            fontName='Helvetica-Bold',
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2E86AB')
        )
        
        toc_content.append(Paragraph("Table of Contents", toc_title))
        
        # TOC entries
        toc_entries = [
            ('1. Executive Summary', 'executive_summary'),
            ('2. Financial Analysis', 'financial_analysis'),
            ('3. Visual Analysis', 'charts_section'),
            ('4. AI Analysis & Insights', 'ai_analysis'),
            ('5. Risk Assessment', 'risk_analysis'),
            ('6. Appendices', 'appendices')
        ]
        
        toc_style = ParagraphStyle(
            'TOCEntry',
            fontSize=12,
            fontName='Helvetica',
            spaceAfter=8,
            leftIndent=20
        )
        
        for entry_text, entry_id in toc_entries:
            toc_content.append(Paragraph(entry_text, toc_style))
        
        return toc_content
    
    def _create_executive_summary(self,
                                 content_data: Dict[str, Any],
                                 heading_style: ParagraphStyle,
                                 body_style: ParagraphStyle) -> List:
        """Create executive summary section."""
        
        summary_content = []
        
        summary_content.append(Paragraph("Executive Summary", heading_style))
        
        # AI-generated summary if available
        ai_analysis = content_data.get('ai_analysis', {})
        if ai_analysis.get('executive_summary'):
            summary_content.append(Paragraph(ai_analysis['executive_summary'], body_style))
        else:
            # Default summary
            default_summary = """
            This financial analysis report provides a comprehensive evaluation of the renewable energy project,
            including detailed financial modeling, risk assessment, and strategic recommendations. The analysis
            employs industry-standard methodologies and incorporates AI-powered insights for enhanced decision-making.
            """
            summary_content.append(Paragraph(default_summary, body_style))
        
        # Key findings
        summary_content.append(Paragraph("Key Findings", heading_style))
        
        financial_results = content_data.get('financial_results', {})
        kpis = financial_results.get('kpis', {})
        
        if kpis:
            findings = []
            
            irr_project = kpis.get('IRR_project', 0)
            if irr_project > 0.12:
                findings.append("• Strong project returns with IRR above 12% threshold")
            elif irr_project > 0.08:
                findings.append("• Moderate project returns requiring optimization")
            else:
                findings.append("• Project returns below target, requiring restructuring")
            
            npv_project = kpis.get('NPV_project', 0)
            if npv_project > 0:
                findings.append(f"• Positive NPV of €{npv_project/1e6:.1f}M indicates value creation")
            else:
                findings.append("• Negative NPV indicates value destruction")
            
            lcoe = kpis.get('LCOE_eur_kwh', 0)
            if lcoe > 0:
                if lcoe < 0.045:
                    findings.append("• Competitive LCOE below 4.5 c€/kWh")
                else:
                    findings.append(f"• LCOE of {lcoe:.3f} €/kWh requires competitiveness review")
            
            findings_text = "\n".join(findings)
            summary_content.append(Paragraph(findings_text, body_style))
        
        return summary_content
    
    def _create_financial_analysis(self,
                                  content_data: Dict[str, Any],
                                  heading_style: ParagraphStyle,
                                  subheading_style: ParagraphStyle,
                                  body_style: ParagraphStyle) -> List:
        """Create detailed financial analysis section."""
        
        financial_content = []
        
        financial_content.append(Paragraph("Financial Analysis", heading_style))
        
        # Financial model overview
        financial_content.append(Paragraph("Financial Model Overview", subheading_style))
        
        overview_text = """
        The financial model employs discounted cash flow (DCF) methodology to evaluate project viability.
        Key assumptions include project capacity, technology specifications, capital expenditure, 
        operational expenses, financing structure, and market conditions.
        """
        
        financial_content.append(Paragraph(overview_text, body_style))
        
        # KPI table
        financial_results = content_data.get('financial_results', {})
        kpis = financial_results.get('kpis', {})
        
        if kpis:
            financial_content.append(Paragraph("Key Performance Indicators", subheading_style))
            
            kpi_data = [
                ['Metric', 'Value', 'Unit', 'Assessment'],
                ['Project IRR', f"{kpis.get('IRR_project', 0):.2%}", '%', self._get_kpi_assessment(kpis.get('IRR_project', 0), 'IRR')],
                ['Equity IRR', f"{kpis.get('IRR_equity', 0):.2%}", '%', self._get_kpi_assessment(kpis.get('IRR_equity', 0), 'IRR')],
                ['NPV Project', f"{kpis.get('NPV_project', 0)/1e6:.2f}", 'M EUR', self._get_kpi_assessment(kpis.get('NPV_project', 0), 'NPV')],
                ['NPV Equity', f"{kpis.get('NPV_equity', 0)/1e6:.2f}", 'M EUR', self._get_kpi_assessment(kpis.get('NPV_equity', 0), 'NPV')],
                ['LCOE', f"{kpis.get('LCOE_eur_kwh', 0):.4f}", 'EUR/kWh', self._get_kpi_assessment(kpis.get('LCOE_eur_kwh', 0), 'LCOE')],
                ['Min DSCR', f"{kpis.get('Min_DSCR', 0):.2f}", 'Ratio', self._get_kpi_assessment(kpis.get('Min_DSCR', 0), 'DSCR')],
                ['Payback Period', f"{kpis.get('Payback_Period', 0):.1f}", 'Years', self._get_kpi_assessment(kpis.get('Payback_Period', 0), 'Payback')]
            ]
            
            kpi_table = Table(kpi_data, colWidths=[1.5*inch, 1.2*inch, 1*inch, 2.3*inch])
            kpi_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2E86AB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP')
            ]))
            
            financial_content.append(kpi_table)
        
        # Financial assumptions
        assumptions = content_data.get('assumptions', {})
        if assumptions:
            financial_content.append(Paragraph("Key Assumptions", subheading_style))
            
            assumption_data = [
                ['Parameter', 'Value', 'Unit'],
                ['Project Capacity', f"{assumptions.get('capacity_mw', 'N/A')}", 'MW'],
                ['Technology Type', assumptions.get('technology_type', 'N/A'), ''],
                ['Project Life', f"{assumptions.get('project_life_years', 'N/A')}", 'Years'],
                ['CAPEX', f"{assumptions.get('capex_meur', 'N/A')}", 'M EUR'],
                ['OPEX (Year 1)', f"{assumptions.get('opex_meur_per_year', 'N/A')}", 'M EUR/year'],
                ['Equity Percentage', f"{assumptions.get('equity_percentage', 0):.1%}", '%'],
                ['Debt Interest Rate', f"{assumptions.get('debt_interest_rate', 0):.2%}", '%'],
                ['Discount Rate', f"{assumptions.get('discount_rate', 0):.2%}", '%']
            ]
            
            assumption_table = Table(assumption_data, colWidths=[2.5*inch, 2*inch, 1.5*inch])
            assumption_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#A23B72')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'TOP')
            ]))
            
            financial_content.append(assumption_table)
        
        return financial_content
    
    def _create_charts_section(self,
                              content_data: Dict[str, Any],
                              heading_style: ParagraphStyle,
                              subheading_style: ParagraphStyle) -> List:
        """Create charts section with embedded images."""
        
        charts_content = []
        
        charts_content.append(Paragraph("Visual Analysis", heading_style))
        
        charts = content_data.get('charts', {})
        
        if charts:
            chart_intro = """
            The following charts provide visual analysis of the project's financial performance,
            risk profile, and market positioning. Each chart is accompanied by AI-generated insights
            where available.
            """
            
            charts_content.append(Paragraph(chart_intro, heading_style))
            
            for chart_name, chart_data in charts.items():
                if chart_name.endswith('_ai_analysis'):
                    continue
                
                # Chart title
                chart_title = chart_name.replace('_', ' ').title()
                charts_content.append(Paragraph(chart_title, subheading_style))
                
                # Chart image
                try:
                    if isinstance(chart_data, str):
                        # Base64 encoded image
                        chart_bytes = base64.b64decode(chart_data)
                        img_buffer = io.BytesIO(chart_bytes)
                        img = Image(img_buffer, width=6*inch, height=4*inch)
                        charts_content.append(img)
                    elif isinstance(chart_data, bytes):
                        # Direct bytes
                        img_buffer = io.BytesIO(chart_data)
                        img = Image(img_buffer, width=6*inch, height=4*inch)
                        charts_content.append(img)
                    
                    # AI analysis for this chart
                    ai_analysis_key = f"{chart_name}_ai_analysis"
                    if charts.get(ai_analysis_key):
                        ai_analysis_text = f"AI Analysis: {charts[ai_analysis_key]}"
                        charts_content.append(Paragraph(ai_analysis_text, heading_style))
                    
                    charts_content.append(Spacer(1, 20))
                    
                except Exception as e:
                    self.logger.warning(f"Could not embed chart {chart_name}: {e}")
                    charts_content.append(Paragraph(f"Chart '{chart_title}' could not be displayed", heading_style))
        
        return charts_content
    
    def _create_ai_analysis_section(self,
                                   content_data: Dict[str, Any],
                                   heading_style: ParagraphStyle,
                                   subheading_style: ParagraphStyle,
                                   body_style: ParagraphStyle) -> List:
        """Create AI analysis section."""
        
        ai_content = []
        
        ai_content.append(Paragraph("AI Analysis & Insights", heading_style))
        
        ai_analysis = content_data.get('ai_analysis', {})
        
        if ai_analysis:
            # AI insights
            if ai_analysis.get('financial_insights'):
                ai_content.append(Paragraph("Financial Insights", subheading_style))
                ai_content.append(Paragraph(ai_analysis['financial_insights'], body_style))
            
            # AI recommendations
            if ai_analysis.get('recommendations'):
                ai_content.append(Paragraph("AI Recommendations", subheading_style))
                
                recommendations_text = ""
                for i, rec in enumerate(ai_analysis['recommendations'], 1):
                    recommendations_text += f"{i}. {rec}\n"
                
                ai_content.append(Paragraph(recommendations_text, body_style))
            
            # Model information
            ai_content.append(Paragraph("AI Model Information", subheading_style))
            
            model_info = f"""
            Model Used: {ai_analysis.get('model_used', 'N/A')}
            Confidence Score: {ai_analysis.get('confidence_score', 0):.1f}%
            Processing Time: {ai_analysis.get('processing_time', 0):.2f} seconds
            Analysis Timestamp: {ai_analysis.get('timestamp', 'N/A')}
            """
            
            ai_content.append(Paragraph(model_info, body_style))
        
        else:
            no_ai_text = """
            AI analysis was not available for this report. The analysis is based on
            traditional financial modeling methodologies and industry best practices.
            """
            ai_content.append(Paragraph(no_ai_text, body_style))
        
        return ai_content
    
    def _create_risk_analysis(self,
                             content_data: Dict[str, Any],
                             heading_style: ParagraphStyle,
                             subheading_style: ParagraphStyle,
                             body_style: ParagraphStyle) -> List:
        """Create risk analysis section."""
        
        risk_content = []
        
        risk_content.append(Paragraph("Risk Assessment", heading_style))
        
        # Risk categories
        risk_categories = [
            ("Financial Risk", "Interest rate changes, currency fluctuations, cost overruns"),
            ("Technical Risk", "Technology performance, equipment failures, maintenance issues"),
            ("Market Risk", "Electricity price volatility, demand changes, competition"),
            ("Regulatory Risk", "Policy changes, permit delays, grid connection issues"),
            ("Environmental Risk", "Weather patterns, climate change impacts, resource availability"),
            ("Operational Risk", "Performance degradation, O&M efficiency, grid stability")
        ]
        
        risk_content.append(Paragraph("Risk Categories", subheading_style))
        
        for risk_name, risk_description in risk_categories:
            risk_text = f"<b>{risk_name}:</b> {risk_description}"
            risk_content.append(Paragraph(risk_text, body_style))
        
        # Risk mitigation
        risk_content.append(Paragraph("Risk Mitigation Strategies", subheading_style))
        
        mitigation_text = """
        • Comprehensive insurance coverage for construction and operational phases
        • Long-term power purchase agreements to mitigate price risk
        • Performance guarantees from equipment suppliers
        • Regular maintenance and monitoring programs
        • Diversified technology and supplier base
        • Contingency reserves for unforeseen circumstances
        """
        
        risk_content.append(Paragraph(mitigation_text, body_style))
        
        return risk_content
    
    def _create_appendices(self,
                          content_data: Dict[str, Any],
                          heading_style: ParagraphStyle,
                          body_style: ParagraphStyle) -> List:
        """Create appendices section."""
        
        appendix_content = []
        
        appendix_content.append(Paragraph("Appendices", heading_style))
        
        # Appendix A: Methodology
        appendix_content.append(Paragraph("Appendix A: Methodology", heading_style))
        
        methodology_text = """
        This financial analysis employs industry-standard discounted cash flow (DCF) methodology
        combined with advanced AI-powered insights. The model incorporates detailed cash flow
        projections, sensitivity analysis, Monte Carlo simulations, and scenario planning to
        provide comprehensive risk assessment and investment recommendations.
        """
        
        appendix_content.append(Paragraph(methodology_text, body_style))
        
        # Appendix B: Assumptions
        appendix_content.append(Paragraph("Appendix B: Detailed Assumptions", heading_style))
        
        assumptions_text = """
        All financial projections are based on conservative assumptions derived from industry
        benchmarks, market research, and client specifications. Key assumptions include
        technology performance parameters, cost structures, financing terms, and market conditions.
        """
        
        appendix_content.append(Paragraph(assumptions_text, body_style))
        
        # Appendix C: Glossary
        appendix_content.append(Paragraph("Appendix C: Glossary", heading_style))
        
        glossary_terms = [
            ("DCF", "Discounted Cash Flow - A valuation method using projected cash flows"),
            ("IRR", "Internal Rate of Return - The discount rate that makes NPV equal to zero"),
            ("NPV", "Net Present Value - The present value of cash inflows minus outflows"),
            ("LCOE", "Levelized Cost of Energy - The lifetime cost per unit of energy produced"),
            ("DSCR", "Debt Service Coverage Ratio - The ratio of operating income to debt service"),
            ("CAPEX", "Capital Expenditure - The funds used for acquiring or upgrading assets"),
            ("OPEX", "Operating Expenditure - The ongoing costs of running the project")
        ]
        
        for term, definition in glossary_terms:
            glossary_text = f"<b>{term}:</b> {definition}"
            appendix_content.append(Paragraph(glossary_text, body_style))
        
        return appendix_content
    
    def _get_kpi_assessment(self, value: float, kpi_type: str) -> str:
        """Get assessment text for KPI value."""
        
        if kpi_type == 'IRR':
            if value > 0.15:
                return "Excellent"
            elif value > 0.10:
                return "Good"
            elif value > 0.08:
                return "Moderate"
            else:
                return "Poor"
        
        elif kpi_type == 'NPV':
            if value > 10000000:  # 10M
                return "Strong Value Creation"
            elif value > 0:
                return "Positive"
            else:
                return "Negative"
        
        elif kpi_type == 'LCOE':
            if value < 0.04:
                return "Highly Competitive"
            elif value < 0.06:
                return "Competitive"
            else:
                return "High Cost"
        
        elif kpi_type == 'DSCR':
            if value > 1.5:
                return "Strong Coverage"
            elif value > 1.2:
                return "Adequate Coverage"
            else:
                return "Weak Coverage"
        
        elif kpi_type == 'Payback':
            if value < 8:
                return "Quick Payback"
            elif value < 12:
                return "Reasonable"
            else:
                return "Long Payback"
        
        return "Standard"
    
    def _apply_security_features(self,
                                input_path: Path,
                                output_path: Path,
                                signature_config: Optional[PDFSignatureConfig],
                                password: Optional[str],
                                security_level: PDFSecurityLevel) -> Path:
        """Apply security features to PDF."""
        
        current_path = input_path
        
        # Apply password protection
        if password and self.password_available:
            try:
                password_path = output_path.with_suffix('.password.pdf')
                self._add_password_protection(current_path, password_path, password)
                current_path = password_path
            except Exception as e:
                self.logger.error(f"Password protection failed: {e}")
        
        # Apply digital signature
        if signature_config and self.signature_available:
            try:
                signature_path = output_path.with_suffix('.signed.pdf')
                self._add_digital_signature(current_path, signature_path, signature_config)
                current_path = signature_path
            except Exception as e:
                self.logger.error(f"Digital signature failed: {e}")
        
        # Move to final output path
        if current_path != output_path:
            current_path.rename(output_path)
        
        return output_path
    
    def _add_password_protection(self, input_path: Path, output_path: Path, password: str):
        """Add password protection to PDF."""
        
        if not self.password_available:
            raise ImportError("PyPDF2 or pypdf is required for password protection")
        
        try:
            with open(input_path, 'rb') as input_file:
                reader = PdfReader(input_file)
                writer = PdfWriter()
                
                # Copy all pages
                for page in reader.pages:
                    writer.add_page(page)
                
                # Add password protection
                writer.encrypt(password)
                
                # Write protected PDF
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
            
            self.logger.info(f"Password protection added to: {output_path}")
            
        except Exception as e:
            self.logger.error(f"Password protection failed: {e}")
            raise
    
    def _add_digital_signature(self,
                              input_path: Path,
                              output_path: Path,
                              signature_config: PDFSignatureConfig):
        """Add digital signature to PDF."""
        
        if not self.signature_available:
            raise ImportError("Cryptography library is required for digital signatures")
        
        try:
            # This is a placeholder for digital signature implementation
            # Full implementation would require additional libraries like endesive
            self.logger.info("Digital signature placeholder - would be implemented with endesive library")
            
            # For now, just copy the file
            import shutil
            shutil.copy2(input_path, output_path)
            
        except Exception as e:
            self.logger.error(f"Digital signature failed: {e}")
            raise
    
    def get_pdf_capabilities(self) -> Dict[str, Any]:
        """Get current PDF generation capabilities."""
        
        return {
            'pdf_generation': self.pdf_available,
            'digital_signatures': self.signature_available,
            'password_protection': self.password_available,
            'watermarks': self.watermark_available,
            'custom_fonts': True,
            'professional_layout': True,
            'charts_embedding': True,
            'table_of_contents': True,
            'header_footer': True,
            'version': '2.0'
        }
    
    def validate_pdf_config(self,
                           layout_config: PDFLayoutConfig,
                           branding_config: PDFBrandingConfig,
                           signature_config: Optional[PDFSignatureConfig] = None) -> Dict[str, Any]:
        """Validate PDF configuration."""
        
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate layout
        if layout_config.margins['top'] < 0:
            validation_result['errors'].append("Top margin must be non-negative")
            validation_result['valid'] = False
        
        if layout_config.font_size < 6 or layout_config.font_size > 72:
            validation_result['warnings'].append("Font size should be between 6 and 72 points")
        
        # Validate branding
        if branding_config.logo_path and not Path(branding_config.logo_path).exists():
            validation_result['warnings'].append("Logo file not found")
        
        # Validate signature config
        if signature_config:
            if signature_config.certificate_path and not Path(signature_config.certificate_path).exists():
                validation_result['errors'].append("Certificate file not found")
                validation_result['valid'] = False
            
            if signature_config.private_key_path and not Path(signature_config.private_key_path).exists():
                validation_result['errors'].append("Private key file not found")
                validation_result['valid'] = False
        
        return validation_result
    
    def create_pdf_template(self,
                           template_name: str,
                           template_config: Dict[str, Any],
                           output_path: Path) -> Path:
        """Create custom PDF template."""
        
        # This would create a custom template file
        # For now, create a configuration file
        
        template_data = {
            'name': template_name,
            'config': template_config,
            'created_at': datetime.now().isoformat(),
            'version': '2.0'
        }
        
        with open(output_path, 'w') as f:
            json.dump(template_data, f, indent=2)
        
        self.logger.info(f"PDF template created: {output_path}")
        return output_path