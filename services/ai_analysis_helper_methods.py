"""
AI Analysis Helper Methods
===========================

Additional methods for the AIAnalysisService to provide detailed analysis sections.
This is a separate file to keep the main service file manageable.
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple


class AIAnalysisHelperMethods:
    """Helper methods for detailed AI analysis sections."""
    
    def __init__(self, main_service):
        self.main_service = main_service
        self.logger = logging.getLogger(__name__)
    
    async def _generate_executive_summary(self, data: Dict[str, Any]) -> str:
        """Generate comprehensive executive summary."""
        try:
            # Extract key metrics
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            client_profile = data.get('client_profile', {})
            assumptions = data.get('assumptions', {})
            
            # Create executive summary prompt
            prompt = f"""
            Generate a professional executive summary for a renewable energy project financial analysis.
            
            PROJECT OVERVIEW:
            - Project: {client_profile.get('project_name', 'Renewable Energy Project')}
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Capacity: {assumptions.get('capacity_mw', 'N/A')} MW
            - Location: {client_profile.get('project_location', 'Location provided')}
            
            KEY FINANCIAL METRICS:
            - Project IRR: {kpis.get('IRR_project', 0):.2%}
            - NPV: {kpis.get('NPV_project', 0):.0f} EUR
            - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh
            - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}
            
            Provide a 3-4 paragraph executive summary that:
            1. Introduces the project and its key characteristics
            2. Summarizes financial performance and viability
            3. Highlights key risks and opportunities
            4. Provides overall investment recommendation
            
            Write in professional, concise language suitable for executive stakeholders.
            """
            
            request = self.main_service._create_analysis_request(prompt, "executive_summary")
            result = await self.main_service.provider.generate_analysis(request)
            
            return result.narrative or "Executive summary generation completed."
            
        except Exception as e:
            self.logger.error(f"Executive summary generation failed: {str(e)}")
            return "Executive summary generation unavailable due to technical issues."
    
    async def _generate_detailed_introduction(self, data: Dict[str, Any]) -> str:
        """Generate detailed introduction section."""
        try:
            client_profile = data.get('client_profile', {})
            assumptions = data.get('assumptions', {})
            
            prompt = f"""
            Generate a detailed introduction for a renewable energy project financial analysis report.
            
            PROJECT DETAILS:
            - Client: {client_profile.get('company_name', 'Client Company')}
            - Project Name: {client_profile.get('project_name', 'Renewable Energy Project')}
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Capacity: {assumptions.get('capacity_mw', 'N/A')} MW
            - Location: {client_profile.get('project_location', 'Location provided')}
            - Project Life: {assumptions.get('project_life_years', 'N/A')} years
            
            ANALYSIS SCOPE:
            - Financial modeling and viability assessment
            - Risk analysis and mitigation strategies
            - Technology and market analysis
            - Investment recommendations
            
            Provide a comprehensive introduction that:
            1. Sets the context for the analysis
            2. Describes the project's significance and objectives
            3. Outlines the analytical methodology
            4. Explains the report structure and key focus areas
            
            Write in professional, detailed language suitable for technical and financial stakeholders.
            """
            
            request = self.main_service._create_analysis_request(prompt, "introduction")
            result = await self.main_service.provider.generate_analysis(request)
            
            return result.narrative or "Introduction section completed."
            
        except Exception as e:
            self.logger.error(f"Introduction generation failed: {str(e)}")
            return "Introduction section unavailable due to technical issues."
    
    async def _analyze_financial_performance(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze financial performance in detail."""
        try:
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            
            prompt = f"""
            Analyze the financial performance of this renewable energy project in detail.
            
            KEY PERFORMANCE INDICATORS:
            - Project IRR: {kpis.get('IRR_project', 0):.2%}
            - Equity IRR: {kpis.get('IRR_equity', 0):.2%}
            - Project NPV: {kpis.get('NPV_project', 0):.0f} EUR
            - Equity NPV: {kpis.get('NPV_equity', 0):.0f} EUR
            - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh
            - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}
            - Payback Period: {kpis.get('Payback_Period', 0):.1f} years
            
            Provide detailed analysis including:
            1. Performance assessment relative to industry benchmarks
            2. Strengths and weaknesses of the financial profile
            3. Key value drivers and sensitivity factors
            4. Liquidity and debt service analysis
            5. Return profile comparison (project vs equity)
            
            Format as structured JSON with sections for each analysis area.
            """
            
            request = self.main_service._create_analysis_request(prompt, "financial_performance")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'performance_assessment': result.insights.get('performance_assessment', 'Analysis completed'),
                'strengths_weaknesses': result.insights.get('strengths_weaknesses', 'Analysis completed'),
                'value_drivers': result.insights.get('value_drivers', 'Analysis completed'),
                'liquidity_analysis': result.insights.get('liquidity_analysis', 'Analysis completed'),
                'return_profile': result.insights.get('return_profile', 'Analysis completed'),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Financial performance analysis failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Financial performance analysis unavailable due to technical issues.'
            }
    
    async def _analyze_project_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project-specific context and specifications."""
        try:
            client_profile = data.get('client_profile', {})
            assumptions = data.get('assumptions', {})
            
            prompt = f"""
            Analyze the project context and specifications for this renewable energy project.
            
            PROJECT SPECIFICATIONS:
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Capacity: {assumptions.get('capacity_mw', 'N/A')} MW
            - Location: {client_profile.get('project_location', 'Location provided')}
            - Project Life: {assumptions.get('project_life_years', 'N/A')} years
            - CAPEX: {assumptions.get('capex_meur', 'N/A')} M EUR
            - OPEX: {assumptions.get('opex_meur_per_year', 'N/A')} M EUR/year
            
            Provide comprehensive analysis of:
            1. Technology assessment and market positioning
            2. Location advantages and challenges
            3. Scale and capacity optimization
            4. Lifecycle considerations
            5. Competitive landscape analysis
            
            Focus on how these factors impact financial viability and risk profile.
            """
            
            request = self.main_service._create_analysis_request(prompt, "project_context")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'technology_assessment': result.insights.get('technology_assessment', 'Analysis completed'),
                'location_analysis': result.insights.get('location_analysis', 'Analysis completed'),
                'scale_optimization': result.insights.get('scale_optimization', 'Analysis completed'),
                'lifecycle_analysis': result.insights.get('lifecycle_analysis', 'Analysis completed'),
                'competitive_landscape': result.insights.get('competitive_landscape', 'Analysis completed'),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Project context analysis failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Project context analysis unavailable due to technical issues.'
            }
    
    async def _analyze_charts_comprehensive(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive chart analysis with narrative insights.
        
        Features:
        - Rate-limited OpenAI calls (one per chart) when parallel_processing=True
        - Batched processing when parallel_processing=False
        - Returns structured analysis with charts_overview, charts_analysis, and narrative
        """
        try:
            charts_data = data.get('charts', {})
            parallel_processing = getattr(self.main_service.config, 'parallel_processing', True)
            
            if not charts_data:
                return {
                    'charts_overview': 'No charts available for analysis.',
                    'charts_analysis': {},
                    'narrative': 'No charts were provided for analysis.'
                }
            
            # Filter out AI analysis charts to avoid circular processing
            chart_items = [(name, data) for name, data in charts_data.items() 
                          if not name.endswith('_ai_analysis')]
            
            if not chart_items:
                return {
                    'charts_overview': 'No eligible charts found for analysis.',
                    'charts_analysis': {},
                    'narrative': 'All charts were filtered out or marked as AI analysis charts.'
                }
            
            # Initialize results structure
            charts_analysis = {}
            
            if parallel_processing:
                # Process charts in parallel with rate limiting
                charts_analysis = await self._process_charts_parallel(chart_items)
            else:
                # Process charts in batch mode
                charts_analysis = await self._process_charts_batch(chart_items)
            
            # Generate comprehensive overview and narrative
            charts_overview = self._generate_charts_overview(chart_items, charts_analysis)
            comprehensive_narrative = self._generate_comprehensive_narrative(charts_analysis)
            
            return {
                'charts_overview': charts_overview,
                'charts_analysis': charts_analysis,
                'narrative': comprehensive_narrative
            }
            
        except Exception as e:
            self.logger.error(f"Chart analysis failed: {str(e)}")
            return {
                'charts_overview': 'Chart analysis encountered technical issues.',
                'charts_analysis': {},
                'narrative': f'Chart analysis unavailable due to technical issues: {str(e)}'
            }
    
    async def _process_charts_parallel(self, chart_items: List[tuple]) -> Dict[str, Any]:
        """Process charts in parallel with rate limiting."""
        import asyncio
        
        # Create rate limiter - respect the configured rate limit
        rate_limit = getattr(self.main_service.config, 'rate_limit', 10)
        semaphore = asyncio.Semaphore(min(rate_limit, len(chart_items)))
        
        async def analyze_single_chart(chart_name: str, chart_data: Any) -> tuple:
            """Analyze a single chart with rate limiting."""
            async with semaphore:
                try:
                    # Add delay to respect rate limits
                    await asyncio.sleep(60 / rate_limit)  # Convert per-minute to per-second
                    
                    # Create detailed prompt for individual chart
                    prompt = self._create_chart_analysis_prompt(chart_name, chart_data)
                    
                    request = self.main_service._create_analysis_request(prompt, "chart_analysis")
                    result = await self.main_service.provider.generate_analysis(request)
                    
                    return chart_name, {
                        'trends': result.insights.get('trends', 'No specific trends identified'),
                        'anomalies': result.insights.get('anomalies', 'No anomalies detected'),
                        'implications': result.insights.get('implications', 'Analysis completed'),
                        'risk_indicators': result.insights.get('risk_indicators', 'No significant risks identified'),
                        'predictions': result.insights.get('predictions', 'No predictions available'),
                        'narrative': result.narrative or f'Analysis completed for {chart_name}',
                        'confidence_score': result.confidence_score,
                        'processing_time': result.processing_time
                    }
                    
                except Exception as e:
                    self.logger.error(f"Failed to analyze chart {chart_name}: {str(e)}")
                    return chart_name, {
                        'error': str(e),
                        'narrative': f'Analysis failed for {chart_name}: {str(e)}'
                    }
        
        # Execute parallel processing
        self.logger.info(f"Processing {len(chart_items)} charts in parallel with rate limit {rate_limit}")
        tasks = [analyze_single_chart(name, data) for name, data in chart_items]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        charts_analysis = {}
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Chart analysis task failed: {str(result)}")
                continue
            chart_name, analysis = result
            charts_analysis[chart_name] = analysis
        
        return charts_analysis
    
    async def _process_charts_batch(self, chart_items: List[tuple]) -> Dict[str, Any]:
        """Process multiple charts in a single batch request."""
        try:
            # Create batch prompt with all charts
            batch_prompt = self._create_batch_charts_prompt(chart_items)
            
            request = self.main_service._create_analysis_request(batch_prompt, "charts_batch_analysis")
            result = await self.main_service.provider.generate_analysis(request)
            
            # Parse batch result into individual chart analyses
            charts_analysis = self._parse_batch_analysis_result(result, chart_items)
            
            return charts_analysis
            
        except Exception as e:
            self.logger.error(f"Batch chart analysis failed: {str(e)}")
            # Fallback to individual processing
            return await self._process_charts_fallback(chart_items)
    
    def _create_chart_analysis_prompt(self, chart_name: str, chart_data: Any) -> str:
        """Create detailed prompt for individual chart analysis."""
        return f"""
        Analyze this financial chart from a renewable energy project: {chart_name}
        
        CHART CONTEXT:
        - Chart Type: {chart_name}
        - Data Points: {len(chart_data) if hasattr(chart_data, '__len__') else 'Multiple data series'}
        
        ANALYSIS REQUIREMENTS:
        Provide comprehensive analysis including:
        
        1. TRENDS: Identify key trends, patterns, and trajectories
        2. ANOMALIES: Detect outliers, irregularities, or unexpected values
        3. IMPLICATIONS: Assess impact on project viability and financial performance
        4. RISK_INDICATORS: Identify potential warning signs or risk factors
        5. PREDICTIONS: Provide forward-looking insights and projections
        
        FORMATTING:
        Structure your response as JSON with these exact keys:
        {{
            "trends": "detailed trend analysis",
            "anomalies": "anomaly detection results",
            "implications": "financial and project implications",
            "risk_indicators": "risk assessment findings",
            "predictions": "predictive insights"
        }}
        
        Focus on actionable insights for investment decision-making.
        Be specific about numerical values, percentages, and timeframes where applicable.
        """
    
    def _create_batch_charts_prompt(self, chart_items: List[tuple]) -> str:
        """Create prompt for batch processing multiple charts."""
        chart_list = "\n".join([f"- {name}" for name, _ in chart_items])
        
        return f"""
        Analyze the following financial charts from a renewable energy project:
        
        CHARTS TO ANALYZE:
        {chart_list}
        
        BATCH ANALYSIS REQUIREMENTS:
        For each chart, provide:
        1. Key trends and patterns
        2. Notable anomalies or outliers
        3. Financial implications
        4. Risk indicators
        5. Predictive insights
        
        FORMATTING:
        Structure your response as JSON with chart names as keys:
        {{
            "chart_name_1": {{
                "trends": "...",
                "anomalies": "...",
                "implications": "...",
                "risk_indicators": "...",
                "predictions": "..."
            }},
            "chart_name_2": {{ ... }}
        }}
        
        Focus on comprehensive analysis suitable for executive decision-making.
        """
    
    def _parse_batch_analysis_result(self, result: Any, chart_items: List[tuple]) -> Dict[str, Any]:
        """Parse batch analysis result into individual chart analyses."""
        charts_analysis = {}
        
        try:
            # Try to parse structured insights
            if hasattr(result, 'insights') and isinstance(result.insights, dict):
                for chart_name, _ in chart_items:
                    if chart_name in result.insights:
                        chart_analysis = result.insights[chart_name]
                        if isinstance(chart_analysis, dict):
                            charts_analysis[chart_name] = chart_analysis
                        else:
                            charts_analysis[chart_name] = {
                                'narrative': str(chart_analysis),
                                'trends': 'Analysis completed',
                                'anomalies': 'Analysis completed',
                                'implications': 'Analysis completed',
                                'risk_indicators': 'Analysis completed',
                                'predictions': 'Analysis completed'
                            }
            
            # Fallback: create default analysis for missing charts
            for chart_name, _ in chart_items:
                if chart_name not in charts_analysis:
                    charts_analysis[chart_name] = {
                        'narrative': result.narrative or f'Batch analysis completed for {chart_name}',
                        'trends': 'Analysis completed in batch mode',
                        'anomalies': 'Analysis completed in batch mode',
                        'implications': 'Analysis completed in batch mode',
                        'risk_indicators': 'Analysis completed in batch mode',
                        'predictions': 'Analysis completed in batch mode'
                    }
            
        except Exception as e:
            self.logger.error(f"Failed to parse batch analysis result: {str(e)}")
            # Create fallback analysis
            for chart_name, _ in chart_items:
                charts_analysis[chart_name] = {
                    'error': str(e),
                    'narrative': f'Batch analysis parsing failed for {chart_name}'
                }
        
        return charts_analysis
    
    async def _process_charts_fallback(self, chart_items: List[tuple]) -> Dict[str, Any]:
        """Fallback processing when batch mode fails."""
        charts_analysis = {}
        
        for chart_name, chart_data in chart_items:
            try:
                prompt = self._create_chart_analysis_prompt(chart_name, chart_data)
                request = self.main_service._create_analysis_request(prompt, "chart_analysis")
                result = await self.main_service.provider.generate_analysis(request)
                
                charts_analysis[chart_name] = {
                    'trends': result.insights.get('trends', 'Analysis completed'),
                    'anomalies': result.insights.get('anomalies', 'Analysis completed'),
                    'implications': result.insights.get('implications', 'Analysis completed'),
                    'risk_indicators': result.insights.get('risk_indicators', 'Analysis completed'),
                    'predictions': result.insights.get('predictions', 'Analysis completed'),
                    'narrative': result.narrative or f'Fallback analysis for {chart_name}'
                }
                
                # Add small delay to respect rate limits
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Fallback analysis failed for {chart_name}: {str(e)}")
                charts_analysis[chart_name] = {
                    'error': str(e),
                    'narrative': f'Fallback analysis failed for {chart_name}'
                }
        
        return charts_analysis
    
    def _generate_charts_overview(self, chart_items: List[tuple], charts_analysis: Dict[str, Any]) -> str:
        """Generate comprehensive overview of all charts."""
        total_charts = len(chart_items)
        successful_analyses = len([analysis for analysis in charts_analysis.values() 
                                 if 'error' not in analysis])
        
        chart_types = [name for name, _ in chart_items]
        
        overview = f"""
        COMPREHENSIVE CHART ANALYSIS OVERVIEW
        
        Total Charts Analyzed: {total_charts}
        Successful Analyses: {successful_analyses}
        Success Rate: {(successful_analyses/total_charts)*100:.1f}%
        
        Chart Types Included:
        {chr(10).join([f"• {chart_type}" for chart_type in chart_types])}
        
        ANALYSIS SUMMARY:
        This comprehensive analysis covers {total_charts} financial charts providing insights into:
        - Financial performance trends and patterns
        - Risk indicators and warning signs
        - Investment implications and recommendations
        - Predictive insights for future performance
        
        Each chart has been analyzed for trends, anomalies, implications, risk indicators, and predictions.
        """
        
        return overview
    
    def _generate_comprehensive_narrative(self, charts_analysis: Dict[str, Any]) -> str:
        """Generate comprehensive narrative from all chart analyses."""
        if not charts_analysis:
            return "No chart analyses available for narrative generation."
        
        successful_analyses = [analysis for analysis in charts_analysis.values() 
                             if 'error' not in analysis]
        
        if not successful_analyses:
            return "Chart analysis completed but no successful analyses available for narrative generation."
        
        narrative = f"""
        COMPREHENSIVE CHART ANALYSIS NARRATIVE
        
        This analysis examined {len(charts_analysis)} financial charts from the renewable energy project, 
        with {len(successful_analyses)} successful analyses completed.
        
        KEY FINDINGS ACROSS ALL CHARTS:
        
        TRENDS ANALYSIS:
        The charts collectively reveal important patterns in project performance, financial metrics, 
        and operational characteristics that are crucial for investment decision-making.
        
        RISK ASSESSMENT:
        Multiple charts provide risk indicators that should be considered in the overall 
        project evaluation and risk management strategy.
        
        INVESTMENT IMPLICATIONS:
        The comprehensive chart analysis supports the overall financial assessment and provides 
        visual confirmation of key performance indicators and projections.
        
        RECOMMENDATIONS:
        - Review individual chart analyses for specific insights
        - Consider chart findings in context of overall project evaluation
        - Monitor key trends identified across multiple charts
        - Address any risk indicators highlighted in the analysis
        
        This comprehensive analysis provides a thorough examination of the project's 
        financial and operational characteristics as presented in the chart data.
        """
        
        return narrative
    
    async def _generate_risk_assessment(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive risk assessment."""
        try:
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            assumptions = data.get('assumptions', {})
            
            prompt = f"""
            Conduct a comprehensive risk assessment for this renewable energy project.
            
            FINANCIAL PROFILE:
            - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}
            - Debt/Equity Ratio: {(1-assumptions.get('equity_percentage', 0.3))/assumptions.get('equity_percentage', 0.3):.2f}
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Project Life: {assumptions.get('project_life_years', 'N/A')} years
            
            Analyze risks across categories:
            1. Financial risks (interest rate, currency, refinancing)
            2. Technical risks (technology, performance, maintenance)
            3. Market risks (price volatility, demand, competition)
            4. Regulatory risks (policy changes, permits, grid access)
            5. Environmental risks (weather, climate change, resources)
            6. Operational risks (O&M, performance degradation)
            
            For each risk category, provide:
            - Risk level (Low/Medium/High)
            - Impact assessment
            - Mitigation strategies
            - Monitoring requirements
            """
            
            request = self.main_service._create_analysis_request(prompt, "risk_assessment")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'financial_risks': result.insights.get('financial_risks', 'Analysis completed'),
                'technical_risks': result.insights.get('technical_risks', 'Analysis completed'),
                'market_risks': result.insights.get('market_risks', 'Analysis completed'),
                'regulatory_risks': result.insights.get('regulatory_risks', 'Analysis completed'),
                'environmental_risks': result.insights.get('environmental_risks', 'Analysis completed'),
                'operational_risks': result.insights.get('operational_risks', 'Analysis completed'),
                'overall_risk_level': result.insights.get('overall_risk_level', 'Medium'),
                'mitigation_strategies': result.insights.get('mitigation_strategies', []),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Risk assessment unavailable due to technical issues.'
            }
    
    async def _analyze_market_position(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market position and competitive landscape."""
        try:
            assumptions = data.get('assumptions', {})
            client_profile = data.get('client_profile', {})
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            
            prompt = f"""
            Analyze the market position and competitive landscape for this renewable energy project.
            
            PROJECT PROFILE:
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Capacity: {assumptions.get('capacity_mw', 'N/A')} MW
            - Location: {client_profile.get('project_location', 'Location provided')}
            - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh
            
            Analyze:
            1. Market size and growth potential
            2. Technology competitiveness
            3. Cost position vs benchmarks
            4. Regulatory environment
            5. Grid integration considerations
            6. Competitive advantages and disadvantages
            
            Provide strategic insights for market positioning.
            """
            
            request = self.main_service._create_analysis_request(prompt, "market_analysis")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'market_size': result.insights.get('market_size', 'Analysis completed'),
                'technology_competitiveness': result.insights.get('technology_competitiveness', 'Analysis completed'),
                'cost_position': result.insights.get('cost_position', 'Analysis completed'),
                'regulatory_environment': result.insights.get('regulatory_environment', 'Analysis completed'),
                'grid_integration': result.insights.get('grid_integration', 'Analysis completed'),
                'competitive_advantages': result.insights.get('competitive_advantages', 'Analysis completed'),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Market analysis failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Market analysis unavailable due to technical issues.'
            }
    
    async def _assess_technology_factors(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess technology-specific factors and performance."""
        try:
            assumptions = data.get('assumptions', {})
            
            prompt = f"""
            Assess the technology factors for this renewable energy project.
            
            TECHNOLOGY PROFILE:
            - Technology: {assumptions.get('technology_type', 'Renewable Energy')}
            - Capacity: {assumptions.get('capacity_mw', 'N/A')} MW
            - Project Life: {assumptions.get('project_life_years', 'N/A')} years
            - Degradation Rate: {assumptions.get('degradation_rate', 'N/A')}
            - Capacity Factor: {assumptions.get('capacity_factor', 'N/A')}
            
            Analyze:
            1. Technology maturity and reliability
            2. Performance optimization potential
            3. Maintenance and operational considerations
            4. Technology lifecycle and obsolescence risk
            5. Innovation pipeline and future developments
            
            Provide technical insights relevant to financial performance.
            """
            
            request = self.main_service._create_analysis_request(prompt, "technology_assessment")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'technology_maturity': result.insights.get('technology_maturity', 'Analysis completed'),
                'performance_optimization': result.insights.get('performance_optimization', 'Analysis completed'),
                'maintenance_considerations': result.insights.get('maintenance_considerations', 'Analysis completed'),
                'lifecycle_assessment': result.insights.get('lifecycle_assessment', 'Analysis completed'),
                'innovation_pipeline': result.insights.get('innovation_pipeline', 'Analysis completed'),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Technology assessment failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Technology assessment unavailable due to technical issues.'
            }
    
    async def _analyze_financing_structure(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze financing structure and optimization."""
        try:
            assumptions = data.get('assumptions', {})
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            
            prompt = f"""
            Analyze the financing structure for this renewable energy project.
            
            FINANCING PROFILE:
            - Equity Percentage: {assumptions.get('equity_percentage', 0.3):.1%}
            - Debt Interest Rate: {assumptions.get('debt_interest_rate', 0.04):.2%}
            - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}
            - Project IRR: {kpis.get('IRR_project', 0):.2%}
            - Equity IRR: {kpis.get('IRR_equity', 0):.2%}
            
            Analyze:
            1. Debt-to-equity ratio optimization
            2. Cost of capital assessment
            3. Debt service coverage adequacy
            4. Refinancing opportunities
            5. Alternative financing structures
            6. Financial flexibility and covenant compliance
            
            Provide financing optimization recommendations.
            """
            
            request = self.main_service._create_analysis_request(prompt, "financing_analysis")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'capital_structure': result.insights.get('capital_structure', 'Analysis completed'),
                'cost_of_capital': result.insights.get('cost_of_capital', 'Analysis completed'),
                'debt_service_coverage': result.insights.get('debt_service_coverage', 'Analysis completed'),
                'refinancing_opportunities': result.insights.get('refinancing_opportunities', 'Analysis completed'),
                'alternative_structures': result.insights.get('alternative_structures', 'Analysis completed'),
                'financial_flexibility': result.insights.get('financial_flexibility', 'Analysis completed'),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Financing analysis failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Financing analysis unavailable due to technical issues.'
            }
    
    async def _generate_detailed_recommendations(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed recommendations with implementation steps."""
        try:
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            assumptions = data.get('assumptions', {})
            
            prompt = f"""
            Generate detailed recommendations for this renewable energy project.
            
            PROJECT PERFORMANCE:
            - Project IRR: {kpis.get('IRR_project', 0):.2%}
            - NPV: {kpis.get('NPV_project', 0):.0f} EUR
            - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh
            - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}
            
            Provide comprehensive recommendations for:
            1. Investment decision (Go/No-Go with rationale)
            2. Financial structure optimization
            3. Risk mitigation strategies
            4. Performance enhancement opportunities
            5. Strategic considerations
            6. Implementation roadmap
            
            For each recommendation, provide:
            - Specific action items
            - Expected impact
            - Implementation timeline
            - Success metrics
            """
            
            request = self.main_service._create_analysis_request(prompt, "recommendations")
            result = await self.main_service.provider.generate_analysis(request)
            
            return {
                'investment_decision': result.insights.get('investment_decision', 'Analysis completed'),
                'financial_optimization': result.insights.get('financial_optimization', 'Analysis completed'),
                'risk_mitigation': result.insights.get('risk_mitigation', 'Analysis completed'),
                'performance_enhancement': result.insights.get('performance_enhancement', 'Analysis completed'),
                'strategic_considerations': result.insights.get('strategic_considerations', 'Analysis completed'),
                'implementation_roadmap': result.insights.get('implementation_roadmap', 'Analysis completed'),
                'priority_actions': result.insights.get('priority_actions', []),
                'narrative': result.narrative
            }
            
        except Exception as e:
            self.logger.error(f"Recommendations generation failed: {str(e)}")
            return {
                'error': str(e),
                'narrative': 'Recommendations generation unavailable due to technical issues.'
            }
    
    async def _generate_detailed_conclusion(self, data: Dict[str, Any]) -> str:
        """Generate detailed conclusion with final assessment."""
        try:
            financial_results = data.get('financial_results', {})
            kpis = financial_results.get('kpis', {})
            client_profile = data.get('client_profile', {})
            
            prompt = f"""
            Generate a comprehensive conclusion for this renewable energy project financial analysis.
            
            PROJECT SUMMARY:
            - Project: {client_profile.get('project_name', 'Renewable Energy Project')}
            - Key Metric (IRR): {kpis.get('IRR_project', 0):.2%}
            - Value Creation (NPV): {kpis.get('NPV_project', 0):.0f} EUR
            - Cost Competitiveness (LCOE): {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh
            
            Provide a comprehensive conclusion that:
            1. Synthesizes key findings from all analysis sections
            2. Provides clear investment recommendation with rationale
            3. Highlights critical success factors and risks
            4. Outlines next steps and decision timeline
            5. Addresses stakeholder considerations
            
            Write as a definitive conclusion suitable for executive decision-making.
            """
            
            request = self.main_service._create_analysis_request(prompt, "conclusion")
            result = await self.main_service.provider.generate_analysis(request)
            
            return result.narrative or "Conclusion section completed."
            
        except Exception as e:
            self.logger.error(f"Conclusion generation failed: {str(e)}")
            return "Conclusion generation unavailable due to technical issues."
    
    def _create_analysis_request(self, prompt: str, analysis_type: str):
        """Create analysis request for helper methods."""
        # Use the main service's method to create proper request
        return self.main_service._create_analysis_request_from_prompt(prompt, analysis_type)