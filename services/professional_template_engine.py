"""
Professional Template Engine
============================

Advanced template engine for customizable professional reports with branding,
layout flexibility, and multi-format support.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import jinja2
from jinja2 import Environment, FileSystemLoader, select_autoescape
import yaml

# Import for advanced formatting
from babel.numbers import format_currency, format_percent
from babel.dates import format_date, format_datetime
import re


@dataclass
class BrandingConfig:
    """Configuration for company branding."""
    company_name: str = "Professional Financial Analysis"
    logo_path: Optional[str] = None
    primary_color: str = "#2E86AB"
    secondary_color: str = "#A23B72"
    accent_color: str = "#F18F01"
    font_family: str = "Arial, sans-serif"
    font_size_base: int = 12
    footer_text: str = "Confidential Financial Analysis"
    watermark_text: Optional[str] = None
    custom_css: Optional[str] = None
    
    # Professional themes
    theme: str = "corporate"  # corporate, modern, minimal, financial
    
    # Custom styling
    header_style: Dict[str, str] = field(default_factory=dict)
    table_style: Dict[str, str] = field(default_factory=dict)
    chart_style: Dict[str, str] = field(default_factory=dict)


@dataclass
class LayoutConfig:
    """Configuration for document layout."""
    page_size: str = "A4"  # A4, Letter, Legal
    orientation: str = "portrait"  # portrait, landscape
    margins: Dict[str, str] = field(default_factory=lambda: {
        "top": "2.5cm", "bottom": "2.5cm", "left": "2cm", "right": "2cm"
    })
    header_height: str = "1.5cm"
    footer_height: str = "1cm"
    
    # Content sections
    include_toc: bool = True
    include_executive_summary: bool = True
    include_charts: bool = True
    include_appendices: bool = True
    
    # Layout options
    columns: int = 1
    column_gap: str = "1cm"
    line_height: float = 1.5
    paragraph_spacing: str = "0.5cm"


@dataclass
class ContentConfig:
    """Configuration for content generation."""
    language: str = "en"  # en, fr, es, de, ar
    currency: str = "EUR"
    date_format: str = "%Y-%m-%d"
    number_format: str = "european"  # european, american, international
    
    # Content options
    include_disclaimers: bool = True
    include_assumptions: bool = True
    include_methodology: bool = True
    include_glossary: bool = True
    
    # AI-generated content
    enable_ai_insights: bool = True
    ai_analysis_depth: str = "comprehensive"  # basic, standard, comprehensive
    ai_language_style: str = "professional"  # professional, technical, executive
    
    # Sections customization
    section_order: List[str] = field(default_factory=lambda: [
        "cover_page",
        "executive_summary",
        "project_overview",
        "financial_analysis",
        "risk_assessment",
        "charts_analysis",
        "recommendations",
        "appendices"
    ])


class TemplateManager:
    """Manages template loading and customization."""
    
    def __init__(self, templates_dir: Path):
        self.templates_dir = templates_dir
        self.logger = logging.getLogger(__name__)
        
        # Initialize Jinja2 environment
        self.env = Environment(
            loader=FileSystemLoader(str(templates_dir)),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Register custom filters
        self._register_filters()
        
        # Load built-in templates
        self._ensure_builtin_templates()
    
    def _register_filters(self):
        """Register custom Jinja2 filters."""
        
        @self.env.filter('currency')
        def currency_filter(value, currency_code='EUR'):
            """Format currency with proper locale."""
            if value is None:
                return "N/A"
            try:
                return format_currency(value, currency_code, locale='en_US')
            except:
                return f"{currency_code} {value:,.2f}"
        
        @self.env.filter('percentage')
        def percentage_filter(value, decimals=2):
            """Format percentage with proper locale."""
            if value is None:
                return "N/A"
            try:
                return format_percent(value, format=f'#,##0.{"0" * decimals}%')
            except:
                return f"{value * 100:.{decimals}f}%"
        
        @self.env.filter('number')
        def number_filter(value, decimals=2):
            """Format number with proper locale."""
            if value is None:
                return "N/A"
            try:
                return f"{value:,.{decimals}f}"
            except:
                return str(value)
        
        @self.env.filter('date')
        def date_filter(value, format='%Y-%m-%d'):
            """Format date with proper locale."""
            if isinstance(value, str):
                try:
                    value = datetime.fromisoformat(value)
                except:
                    return value
            
            if isinstance(value, datetime):
                return format_date(value, format='medium', locale='en_US')
            return str(value)
        
        @self.env.filter('risk_level')
        def risk_level_filter(value):
            """Convert risk score to level."""
            if value is None:
                return "Unknown"
            
            if value >= 8:
                return "High"
            elif value >= 5:
                return "Medium"
            else:
                return "Low"
        
        @self.env.filter('status_icon')
        def status_icon_filter(value):
            """Convert status to icon."""
            status_map = {
                'excellent': '🟢',
                'good': '🟡',
                'poor': '🔴',
                'positive': '✅',
                'negative': '❌',
                'warning': '⚠️'
            }
            return status_map.get(str(value).lower(), '○')
        
        @self.env.filter('format_millions')
        def format_millions_filter(value):
            """Format large numbers in millions."""
            if value is None:
                return "N/A"
            try:
                return f"€{value / 1_000_000:.1f}M"
            except:
                return str(value)
        
        @self.env.filter('sanitize_html')
        def sanitize_html_filter(value):
            """Sanitize HTML content."""
            if not value:
                return ""
            # Remove dangerous tags
            dangerous_tags = ['script', 'style', 'iframe', 'object', 'embed']
            for tag in dangerous_tags:
                value = re.sub(f'<{tag}.*?</{tag}>', '', value, flags=re.DOTALL | re.IGNORECASE)
            return value
        
        @self.env.filter('capitalize_words')
        def capitalize_words_filter(value):
            """Capitalize each word properly."""
            if not value:
                return ""
            return ' '.join(word.capitalize() for word in str(value).split())
    
    def _ensure_builtin_templates(self):
        """Ensure built-in templates exist."""
        builtin_templates = {
            'html_report.html': self._get_html_template(),
            'pdf_report.html': self._get_pdf_template(),
            'docx_report.xml': self._get_docx_template(),
            'executive_summary.html': self._get_executive_summary_template(),
            'cover_page.html': self._get_cover_page_template(),
            'financial_section.html': self._get_financial_section_template(),
            'charts_section.html': self._get_charts_section_template(),
            'risk_analysis.html': self._get_risk_analysis_template(),
            'appendix.html': self._get_appendix_template()
        }
        
        for template_name, template_content in builtin_templates.items():
            template_path = self.templates_dir / template_name
            if not template_path.exists():
                template_path.parent.mkdir(parents=True, exist_ok=True)
                template_path.write_text(template_content, encoding='utf-8')
                self.logger.info(f"Created built-in template: {template_name}")
    
    def load_template(self, template_name: str) -> jinja2.Template:
        """Load template by name."""
        try:
            return self.env.get_template(template_name)
        except jinja2.TemplateNotFound:
            self.logger.error(f"Template not found: {template_name}")
            raise
    
    def render_template(self, 
                       template_name: str, 
                       context: Dict[str, Any],
                       branding: BrandingConfig,
                       layout: LayoutConfig,
                       content_config: ContentConfig) -> str:
        """Render template with context and configuration."""
        
        template = self.load_template(template_name)
        
        # Enhanced context with configurations
        enhanced_context = {
            **context,
            'branding': branding,
            'layout': layout,
            'content': content_config,
            'render_time': datetime.now(),
            'template_version': '2.0'
        }
        
        return template.render(enhanced_context)
    
    def _get_html_template(self) -> str:
        """Get comprehensive HTML report template."""
        return '''<!DOCTYPE html>
<html lang="{{ content.language }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ client_profile.project_name }} - Financial Analysis Report</title>
    <style>
        :root {
            --primary-color: {{ branding.primary_color }};
            --secondary-color: {{ branding.secondary_color }};
            --accent-color: {{ branding.accent_color }};
            --font-family: {{ branding.font_family }};
            --font-size-base: {{ branding.font_size_base }}px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: {{ layout.line_height }};
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 10px;
            margin-bottom: 30px;
            {% if branding.header_style %}
            {% for key, value in branding.header_style.items() %}
            {{ key }}: {{ value }};
            {% endfor %}
            {% endif %}
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid var(--primary-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .section h2 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }
        
        .section h3 {
            color: var(--secondary-color);
            font-size: 1.4em;
            margin-bottom: 15px;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .kpi-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .kpi-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .kpi-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .kpi-status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .kpi-status.excellent {
            background: #d4edda;
            color: #155724;
        }
        
        .kpi-status.good {
            background: #fff3cd;
            color: #856404;
        }
        
        .kpi-status.poor {
            background: #f8d7da;
            color: #721c24;
        }
        
        .professional-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            {% if branding.table_style %}
            {% for key, value in branding.table_style.items() %}
            {{ key }}: {{ value }};
            {% endfor %}
            {% endif %}
        }
        
        .professional-table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .professional-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }
        
        .professional-table tr:hover td {
            background-color: #f5f5f5;
        }
        
        .professional-table tr:last-child td {
            border-bottom: none;
        }
        
        .chart-container {
            margin: 25px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .chart-container h4 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .ai-insights {
            background: linear-gradient(135deg, #e3f2fd, #f1f8e9);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }
        
        .ai-insights::before {
            content: "🤖";
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1.5em;
        }
        
        .ai-insights h4 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .ai-insights p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .recommendations {
            background: #f8f9fa;
            border-left: 4px solid var(--accent-color);
            padding: 20px;
            margin: 20px 0;
        }
        
        .recommendations h4 {
            color: var(--accent-color);
            margin-bottom: 15px;
        }
        
        .recommendations ul {
            list-style: none;
            padding-left: 0;
        }
        
        .recommendations li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .recommendations li:before {
            content: "✓";
            color: var(--accent-color);
            font-weight: bold;
            margin-right: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: var(--primary-color);
            color: white;
            border-radius: 8px;
            font-size: 0.9em;
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 6em;
            color: rgba(0,0,0,0.05);
            pointer-events: none;
            z-index: -1;
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; margin: 0; }
            .section { page-break-inside: avoid; }
            .chart-container { page-break-inside: avoid; }
        }
        
        @media (max-width: 768px) {
            .container { margin: 10px; padding: 15px; }
            .header h1 { font-size: 2em; }
            .kpi-grid { grid-template-columns: 1fr; }
            .professional-table { font-size: 0.9em; }
        }
        
        {{ branding.custom_css|safe }}
    </style>
</head>
<body>
    {% if branding.watermark_text %}
    <div class="watermark">{{ branding.watermark_text }}</div>
    {% endif %}
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ client_profile.project_name }}</h1>
            <div class="subtitle">Financial Analysis Report</div>
            <div class="subtitle">Generated on {{ render_time | date }}</div>
        </div>
        
        <!-- Executive Summary -->
        {% if layout.include_executive_summary %}
        <div class="section">
            <h2>📊 Executive Summary</h2>
            {% if ai_analysis and ai_analysis.executive_summary %}
            <div class="ai-insights">
                <h4>AI-Generated Executive Summary</h4>
                {{ ai_analysis.executive_summary | safe }}
            </div>
            {% endif %}
            
            <div class="kpi-grid">
                {% for kpi_name, kpi_value in financial_results.kpis.items() %}
                <div class="kpi-card">
                    <div class="kpi-value">
                        {% if 'IRR' in kpi_name %}
                            {{ kpi_value | percentage }}
                        {% elif 'NPV' in kpi_name %}
                            {{ kpi_value | format_millions }}
                        {% elif 'LCOE' in kpi_name %}
                            {{ kpi_value | currency }}
                        {% else %}
                            {{ kpi_value | number }}
                        {% endif %}
                    </div>
                    <div class="kpi-label">{{ kpi_name.replace('_', ' ').title() }}</div>
                    <div class="kpi-status {{ kpi_name | get_status_class }}">
                        {{ kpi_value | get_status_text }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Financial Analysis -->
        <div class="section">
            <h2>💰 Financial Analysis</h2>
            {% if ai_analysis and ai_analysis.financial_insights %}
            <div class="ai-insights ai-financial-analysis">
                <h4>AI Financial Analysis</h4>
                <div class="ai-content">
                    {{ ai_analysis.financial_insights | safe }}
                </div>
            </div>
            {% endif %}
            
            <h3>Key Performance Indicators</h3>
            <table class="professional-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                        <th>Status</th>
                        <th>Benchmark</th>
                    </tr>
                </thead>
                <tbody>
                    {% for kpi_name, kpi_value in financial_results.kpis.items() %}
                    <tr>
                        <td>{{ kpi_name.replace('_', ' ').title() }}</td>
                        <td>
                            {% if 'IRR' in kpi_name %}
                                {{ kpi_value | percentage }}
                            {% elif 'NPV' in kpi_name %}
                                {{ kpi_value | format_millions }}
                            {% elif 'LCOE' in kpi_name %}
                                {{ kpi_value | currency }}
                            {% else %}
                                {{ kpi_value | number }}
                            {% endif %}
                        </td>
                        <td>{{ kpi_value | status_icon }} {{ kpi_value | get_status_text }}</td>
                        <td>{{ kpi_value | get_benchmark }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Charts Section -->
        {% if layout.include_charts and charts %}
        <div class="section">
            <h2>📈 Visual Analysis</h2>
            {% for chart_name, chart_data in charts.items() %}
            <div class="chart-container">
                <h4>{{ chart_name.replace('_', ' ').title() }}</h4>
                <img src="data:image/png;base64,{{ chart_data | b64encode }}" 
                     alt="{{ chart_name.replace('_', ' ').title() }}">
                
                {% if ai_analysis and ai_analysis.charts_analysis and chart_name in ai_analysis.charts_analysis %}
                <div class="ai-insights ai-chart-analysis">
                    <h4>AI Chart Analysis</h4>
                    <div class="ai-content">
                        {{ ai_analysis.charts_analysis[chart_name] | safe }}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Risk Analysis -->
        <div class="section">
            <h2>⚠️ Risk Analysis</h2>
            {% if ai_analysis and ai_analysis.risk_assessment %}
            <div class="ai-insights ai-risk-assessment">
                <h4>AI Risk Assessment</h4>
                <div class="ai-content">
                    {{ ai_analysis.risk_assessment | safe }}
                </div>
            </div>
            {% endif %}
            
            <!-- Risk factors would be displayed here -->
        </div>
        
        <!-- Recommendations -->
        {% if ai_analysis and ai_analysis.recommendations %}
        <div class="recommendations">
            <h4>🎯 AI-Generated Recommendations</h4>
            <ul>
                {% for recommendation in ai_analysis.recommendations %}
                <li>{{ recommendation }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ branding.company_name }}</strong></p>
            <p>{{ branding.footer_text }}</p>
            <p>Generated: {{ render_time | date }} | Template Version: {{ template_version }}</p>
        </div>
    </div>
</body>
</html>'''
    
    def _get_pdf_template(self) -> str:
        """Get PDF-optimized template."""
        return '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ client_profile.project_name }} - Financial Report</title>
    <style>
        @page {
            size: {{ layout.page_size }} {{ layout.orientation }};
            margin: {{ layout.margins.top }} {{ layout.margins.right }} 
                   {{ layout.margins.bottom }} {{ layout.margins.left }};
        }
        
        body {
            font-family: {{ branding.font_family }};
            font-size: {{ branding.font_size_base }}px;
            line-height: {{ layout.line_height }};
            color: #333;
        }
        
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 2px solid {{ branding.primary_color }};
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: {{ branding.primary_color }};
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: {{ branding.primary_color }};
            font-size: 1.5em;
            border-bottom: 1px solid {{ branding.primary_color }};
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .kpi-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .kpi-table th {
            background: {{ branding.primary_color }};
            color: white;
            padding: 10px;
            text-align: left;
            font-weight: bold;
        }
        
        .kpi-table td {
            padding: 8px 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .kpi-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .chart-container {
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
        }
        
        .chart-container h4 {
            color: {{ branding.primary_color }};
            margin-bottom: 10px;
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
        }
        
        .ai-insights {
            background: #f8f9fa;
            border-left: 4px solid {{ branding.secondary_color }};
            padding: 15px;
            margin: 15px 0;
        }
        
        .ai-insights h4 {
            color: {{ branding.secondary_color }};
            margin-bottom: 10px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- Content similar to HTML template but optimized for PDF -->
    <div class="header">
        <h1>{{ client_profile.project_name }}</h1>
        <p>Financial Analysis Report</p>
        <p>{{ render_time | date }}</p>
    </div>
    
    <!-- Executive Summary -->
    <div class="section">
        <h2>Executive Summary</h2>
        {% if ai_analysis and ai_analysis.executive_summary %}
        <div class="ai-insights">
            <h4>AI Executive Summary</h4>
            <div class="ai-content">
                {{ ai_analysis.executive_summary | safe }}
            </div>
        </div>
        {% endif %}
        
        <table class="kpi-table">
            <thead>
                <tr>
                    <th>Key Performance Indicator</th>
                    <th>Value</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for kpi_name, kpi_value in financial_results.kpis.items() %}
                <tr>
                    <td>{{ kpi_name.replace('_', ' ').title() }}</td>
                    <td>
                        {% if 'IRR' in kpi_name %}
                            {{ kpi_value | percentage }}
                        {% elif 'NPV' in kpi_name %}
                            {{ kpi_value | format_millions }}
                        {% elif 'LCOE' in kpi_name %}
                            {{ kpi_value | currency }}
                        {% else %}
                            {{ kpi_value | number }}
                        {% endif %}
                    </td>
                    <td>{{ kpi_value | get_status_text }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Charts Section -->
    {% if layout.include_charts and charts %}
    <div class="page-break"></div>
    <div class="section">
        <h2>Visual Analysis</h2>
        {% for chart_name, chart_data in charts.items() %}
        <div class="chart-container">
            <h4>{{ chart_name.replace('_', ' ').title() }}</h4>
            <img src="data:image/png;base64,{{ chart_data | b64encode }}" 
                 alt="{{ chart_name.replace('_', ' ').title() }}">
            
            {% if ai_analysis and ai_analysis.charts_analysis and chart_name in ai_analysis.charts_analysis %}
            <div class="ai-insights ai-chart-analysis">
                <h4>Analysis</h4>
                <div class="ai-content">
                    {{ ai_analysis.charts_analysis[chart_name] | safe }}
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Risk Analysis -->
    <div class="section">
        <h2>Risk Analysis</h2>
        {% if ai_analysis and ai_analysis.risk_assessment %}
        <div class="ai-insights ai-risk-assessment">
            <h4>AI Risk Assessment</h4>
            <div class="ai-content">
                {{ ai_analysis.risk_assessment | safe }}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <p><strong>{{ branding.company_name }}</strong></p>
        <p>{{ branding.footer_text }}</p>
        <p>Generated: {{ render_time | date }}</p>
    </div>
</body>
</html>'''
    
    def _get_executive_summary_template(self) -> str:
        """Get executive summary template."""
        return '''<div class="executive-summary">
    <h1>Executive Summary</h1>
    
    {% if ai_analysis and ai_analysis.executive_summary %}
    <div class="ai-generated-summary">
        <h2>🤖 AI-Generated Executive Summary</h2>
        <div class="ai-content">
            {{ ai_analysis.executive_summary | safe }}
        </div>
    </div>
    {% endif %}
    
    <div class="key-highlights">
        <h2>Key Financial Highlights</h2>
        <div class="highlights-grid">
            {% for kpi_name, kpi_value in financial_results.kpis.items() %}
            <div class="highlight-item">
                <div class="highlight-value">
                    {% if 'IRR' in kpi_name %}
                        {{ kpi_value | percentage }}
                    {% elif 'NPV' in kpi_name %}
                        {{ kpi_value | format_millions }}
                    {% elif 'LCOE' in kpi_name %}
                        {{ kpi_value | currency }}
                    {% else %}
                        {{ kpi_value | number }}
                    {% endif %}
                </div>
                <div class="highlight-label">{{ kpi_name.replace('_', ' ').title() }}</div>
                <div class="highlight-status">{{ kpi_value | status_icon }} {{ kpi_value | get_status_text }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    {% if ai_analysis and ai_analysis.recommendations %}
    <div class="executive-recommendations">
        <h2>Key Recommendations</h2>
        <ul class="recommendations-list">
            {% for recommendation in ai_analysis.recommendations[:5] %}
            <li>{{ recommendation }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <div class="investment-summary">
        <h2>Investment Summary</h2>
        <table class="investment-table">
            <tr>
                <td><strong>Total Investment</strong></td>
                <td>{{ assumptions.capex_meur | currency }} M</td>
            </tr>
            <tr>
                <td><strong>Project IRR</strong></td>
                <td>{{ financial_results.kpis.IRR_project | percentage }}</td>
            </tr>
            <tr>
                <td><strong>NPV</strong></td>
                <td>{{ financial_results.kpis.NPV_project | format_millions }}</td>
            </tr>
            <tr>
                <td><strong>Payback Period</strong></td>
                <td>{{ financial_results.kpis.Payback_Period | number }} years</td>
            </tr>
        </table>
    </div>
</div>'''
    
    def _get_cover_page_template(self) -> str:
        """Get cover page template."""
        return '''<div class="cover-page">
    <div class="cover-header">
        {% if branding.logo_path %}
        <img src="{{ branding.logo_path }}" alt="Company Logo" class="company-logo">
        {% endif %}
        <h1 class="company-name">{{ branding.company_name }}</h1>
    </div>
    
    <div class="cover-content">
        <div class="report-title">
            <h1>Financial Analysis Report</h1>
            <h2>{{ client_profile.project_name }}</h2>
        </div>
        
        <div class="project-details">
            <div class="detail-item">
                <span class="label">Client:</span>
                <span class="value">{{ client_profile.company_name }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Project:</span>
                <span class="value">{{ client_profile.project_name }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Capacity:</span>
                <span class="value">{{ assumptions.capacity_mw }} MW</span>
            </div>
            <div class="detail-item">
                <span class="label">Location:</span>
                <span class="value">{{ client_profile.project_location or 'To be determined' }}</span>
            </div>
            <div class="detail-item">
                <span class="label">Report Date:</span>
                <span class="value">{{ render_time | date }}</span>
            </div>
        </div>
        
        <div class="cover-summary">
            <h3>Executive Summary</h3>
            <div class="summary-kpis">
                <div class="kpi-item">
                    <div class="kpi-value">{{ financial_results.kpis.IRR_project | percentage }}</div>
                    <div class="kpi-label">Project IRR</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value">{{ financial_results.kpis.NPV_project | format_millions }}</div>
                    <div class="kpi-label">NPV</div>
                </div>
                <div class="kpi-item">
                    <div class="kpi-value">{{ financial_results.kpis.LCOE_eur_kwh | currency }}</div>
                    <div class="kpi-label">LCOE</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="cover-footer">
        <p>{{ branding.footer_text }}</p>
        <p>Confidential and Proprietary</p>
    </div>
</div>'''
    
    def _get_financial_section_template(self) -> str:
        """Get financial section template."""
        return '''<div class="financial-section">
    <h2>Financial Analysis</h2>
    
    {% if ai_analysis and ai_analysis.financial_insights %}
    <div class="ai-financial-analysis">
        <h3>🤖 AI Financial Analysis</h3>
        <div class="ai-content">
            {{ ai_analysis.financial_insights | safe }}
        </div>
    </div>
    {% endif %}
    
    <div class="financial-overview">
        <h3>Key Performance Indicators</h3>
        <div class="kpi-dashboard">
            {% for kpi_name, kpi_value in financial_results.kpis.items() %}
            <div class="kpi-card">
                <div class="kpi-header">
                    <h4>{{ kpi_name.replace('_', ' ').title() }}</h4>
                    <span class="kpi-icon">{{ kpi_value | status_icon }}</span>
                </div>
                <div class="kpi-value">
                    {% if 'IRR' in kpi_name %}
                        {{ kpi_value | percentage }}
                    {% elif 'NPV' in kpi_name %}
                        {{ kpi_value | format_millions }}
                    {% elif 'LCOE' in kpi_name %}
                        {{ kpi_value | currency }}
                    {% else %}
                        {{ kpi_value | number }}
                    {% endif %}
                </div>
                <div class="kpi-status {{ kpi_value | get_status_class }}">
                    {{ kpi_value | get_status_text }}
                </div>
                <div class="kpi-benchmark">
                    Benchmark: {{ kpi_value | get_benchmark }}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="financial-details">
        <h3>Detailed Financial Metrics</h3>
        <table class="financial-table">
            <thead>
                <tr>
                    <th>Metric</th>
                    <th>Value</th>
                    <th>Unit</th>
                    <th>Status</th>
                    <th>Benchmark</th>
                    <th>Variance</th>
                </tr>
            </thead>
            <tbody>
                {% for kpi_name, kpi_value in financial_results.kpis.items() %}
                <tr>
                    <td>{{ kpi_name.replace('_', ' ').title() }}</td>
                    <td class="numeric">
                        {% if 'IRR' in kpi_name %}
                            {{ kpi_value | percentage }}
                        {% elif 'NPV' in kpi_name %}
                            {{ kpi_value | format_millions }}
                        {% elif 'LCOE' in kpi_name %}
                            {{ kpi_value | currency }}
                        {% else %}
                            {{ kpi_value | number }}
                        {% endif %}
                    </td>
                    <td>
                        {% if 'IRR' in kpi_name %}%
                        {% elif 'NPV' in kpi_name %}M€
                        {% elif 'LCOE' in kpi_name %}€/kWh
                        {% else %}{{ kpi_value | get_unit }}
                        {% endif %}
                    </td>
                    <td class="status">
                        {{ kpi_value | status_icon }} {{ kpi_value | get_status_text }}
                    </td>
                    <td>{{ kpi_value | get_benchmark }}</td>
                    <td class="variance">{{ kpi_value | get_variance }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>'''
    
    def _get_charts_section_template(self) -> str:
        """Get charts section template."""
        return '''<div class="charts-section">
    <h2>Visual Analysis & Charts</h2>
    
    {% if ai_analysis and ai_analysis.charts_overview %}
    <div class="charts-overview">
        <h3>🤖 AI Charts Overview</h3>
        <div class="ai-content">
            {{ ai_analysis.charts_overview | safe }}
        </div>
    </div>
    {% endif %}
    
    <div class="charts-grid">
        {% for chart_name, chart_data in charts.items() %}
        <div class="chart-item">
            <div class="chart-header">
                <h3>{{ chart_name.replace('_', ' ').title() }}</h3>
                <div class="chart-type">{{ chart_name | get_chart_type }}</div>
            </div>
            
            <div class="chart-container">
                <img src="data:image/png;base64,{{ chart_data | b64encode }}" 
                     alt="{{ chart_name.replace('_', ' ').title() }}"
                     class="chart-image">
            </div>
            
            {% if ai_analysis and ai_analysis.charts_analysis and chart_name in ai_analysis.charts_analysis %}
            <div class="chart-analysis">
                <h4>🤖 AI Analysis</h4>
                <div class="analysis-content">
                    {{ ai_analysis.charts_analysis[chart_name] | safe }}
                </div>
            </div>
            {% endif %}
            
            <div class="chart-insights">
                <h4>Key Insights</h4>
                <ul class="insights-list">
                    {% for insight in chart_name | get_chart_insights %}
                    <li>{{ insight }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="charts-summary">
        <h3>Visual Analysis Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <h4>Performance Trends</h4>
                <p>Analysis of financial performance trends over time...</p>
            </div>
            <div class="summary-item">
                <h4>Risk Indicators</h4>
                <p>Visual identification of risk factors and mitigation strategies...</p>
            </div>
            <div class="summary-item">
                <h4>Market Position</h4>
                <p>Comparative analysis against industry benchmarks...</p>
            </div>
        </div>
    </div>
</div>'''
    
    def _get_risk_analysis_template(self) -> str:
        """Get risk analysis template."""
        return '''<div class="risk-analysis">
    <h2>Risk Analysis & Assessment</h2>
    
    {% if ai_analysis and ai_analysis.risk_assessment %}
    <div class="ai-risk-assessment">
        <h3>🤖 AI Risk Assessment</h3>
        <div class="ai-content">
            {{ ai_analysis.risk_assessment | safe }}
        </div>
    </div>
    {% endif %}
    
    <div class="risk-overview">
        <h3>Risk Overview</h3>
        <div class="risk-dashboard">
            <div class="risk-score">
                <div class="score-value">{{ overall_risk_score | number }}</div>
                <div class="score-label">Overall Risk Score</div>
                <div class="score-level">{{ overall_risk_score | risk_level }}</div>
            </div>
            
            <div class="risk-categories">
                {% for category, score in risk_categories.items() %}
                <div class="risk-category">
                    <h4>{{ category.replace('_', ' ').title() }}</h4>
                    <div class="risk-bar">
                        <div class="risk-fill" style="width: {{ score * 10 }}%"></div>
                    </div>
                    <div class="risk-level">{{ score | risk_level }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="risk-details">
        <h3>Detailed Risk Analysis</h3>
        <table class="risk-table">
            <thead>
                <tr>
                    <th>Risk Factor</th>
                    <th>Impact</th>
                    <th>Probability</th>
                    <th>Risk Score</th>
                    <th>Mitigation Strategy</th>
                </tr>
            </thead>
            <tbody>
                {% for risk in risk_factors %}
                <tr>
                    <td>{{ risk.name }}</td>
                    <td class="impact {{ risk.impact | lower }}">
                        {{ risk.impact | capitalize }}
                    </td>
                    <td class="probability">{{ risk.probability | percentage }}</td>
                    <td class="risk-score">
                        <span class="score-value">{{ risk.score | number }}</span>
                        <span class="score-level">{{ risk.score | risk_level }}</span>
                    </td>
                    <td class="mitigation">{{ risk.mitigation }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="risk-recommendations">
        <h3>Risk Mitigation Recommendations</h3>
        {% if ai_analysis and ai_analysis.risk_recommendations %}
        <div class="ai-recommendations">
            <h4>🤖 AI-Generated Recommendations</h4>
            <ul>
                {% for recommendation in ai_analysis.risk_recommendations %}
                <li>{{ recommendation }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
        
        <div class="mitigation-strategies">
            <h4>Mitigation Strategies</h4>
            <div class="strategies-grid">
                {% for strategy in mitigation_strategies %}
                <div class="strategy-item">
                    <h5>{{ strategy.name }}</h5>
                    <p>{{ strategy.description }}</p>
                    <div class="strategy-effectiveness">
                        Effectiveness: {{ strategy.effectiveness | percentage }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>'''
    
    def _get_appendix_template(self) -> str:
        """Get appendix template."""
        return '''<div class="appendix">
    <h2>Appendices</h2>
    
    <div class="appendix-section">
        <h3>A. Methodology</h3>
        <div class="methodology-content">
            <h4>Financial Modeling Approach</h4>
            <p>The financial analysis employs industry-standard DCF (Discounted Cash Flow) methodology...</p>
            
            <h4>AI Analysis Integration</h4>
            <p>AI-powered insights are generated using advanced language models to provide:</p>
            <ul>
                <li>Pattern recognition in financial data</li>
                <li>Risk assessment and recommendation generation</li>
                <li>Narrative explanations of complex financial metrics</li>
                <li>Comparative analysis against industry benchmarks</li>
            </ul>
            
            <h4>Data Sources</h4>
            <ul>
                <li>Project financial assumptions</li>
                <li>Industry benchmarks and market data</li>
                <li>Historical performance data</li>
                <li>Regulatory and policy information</li>
            </ul>
        </div>
    </div>
    
    <div class="appendix-section">
        <h3>B. Assumptions</h3>
        <table class="assumptions-table">
            <thead>
                <tr>
                    <th>Category</th>
                    <th>Parameter</th>
                    <th>Value</th>
                    <th>Source</th>
                </tr>
            </thead>
            <tbody>
                {% for assumption in detailed_assumptions %}
                <tr>
                    <td>{{ assumption.category }}</td>
                    <td>{{ assumption.parameter }}</td>
                    <td>{{ assumption.value }}</td>
                    <td>{{ assumption.source }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="appendix-section">
        <h3>C. Sensitivity Analysis Details</h3>
        {% if sensitivity_analysis %}
        <div class="sensitivity-details">
            <h4>Parameter Sensitivity</h4>
            <table class="sensitivity-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>-20%</th>
                        <th>-10%</th>
                        <th>Base</th>
                        <th>+10%</th>
                        <th>+20%</th>
                    </tr>
                </thead>
                <tbody>
                    {% for param, values in sensitivity_analysis.items() %}
                    <tr>
                        <td>{{ param }}</td>
                        {% for value in values %}
                        <td>{{ value | percentage }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
    
    <div class="appendix-section">
        <h3>D. Glossary</h3>
        <div class="glossary">
            <dl>
                <dt>DCF</dt>
                <dd>Discounted Cash Flow - A valuation method that estimates the value of an investment based on its expected future cash flows</dd>
                
                <dt>IRR</dt>
                <dd>Internal Rate of Return - The discount rate that makes the net present value of all cash flows equal to zero</dd>
                
                <dt>NPV</dt>
                <dd>Net Present Value - The difference between the present value of cash inflows and outflows over a period of time</dd>
                
                <dt>LCOE</dt>
                <dd>Levelized Cost of Energy - The per-unit cost of building and operating a generating plant over its financial life</dd>
                
                <dt>DSCR</dt>
                <dd>Debt Service Coverage Ratio - A measure of the cash flow available to pay current debt obligations</dd>
                
                <dt>AI Analysis</dt>
                <dd>Machine learning-powered analysis providing insights, patterns, and recommendations based on financial data</dd>
            </dl>
        </div>
    </div>
    
    <div class="appendix-section">
        <h3>E. Technical Specifications</h3>
        <div class="technical-specs">
            <h4>Report Generation</h4>
            <table class="specs-table">
                <tr>
                    <td>Template Engine</td>
                    <td>Jinja2 Professional Template System</td>
                </tr>
                <tr>
                    <td>AI Provider</td>
                    <td>{{ ai_provider | default('Not configured') }}</td>
                </tr>
                <tr>
                    <td>Data Protection</td>
                    <td>{{ 'Enabled' if privacy_mode else 'Disabled' }}</td>
                </tr>
                <tr>
                    <td>Generation Time</td>
                    <td>{{ render_time | date }}</td>
                </tr>
                <tr>
                    <td>Template Version</td>
                    <td>{{ template_version }}</td>
                </tr>
            </table>
        </div>
    </div>
</div>'''
    
    def _get_docx_template(self) -> str:
        """Get DOCX template (simplified XML structure)."""
        return '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
    <w:body>
        <!-- Document content structure for DOCX -->
        <w:p>
            <w:pPr>
                <w:pStyle w:val="Title"/>
            </w:pPr>
            <w:r>
                <w:t>{{ client_profile.project_name }} - Financial Analysis Report</w:t>
            </w:r>
        </w:p>
        
        <!-- Content sections would be generated here -->
        
    </w:body>
</w:document>'''


class ProfessionalTemplateEngine:
    """Main template engine for professional reports."""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        if templates_dir is None:
            templates_dir = Path("templates")
        
        self.templates_dir = templates_dir
        self.templates_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        self.template_manager = TemplateManager(self.templates_dir)
        
        # Default configurations
        self.default_branding = BrandingConfig()
        self.default_layout = LayoutConfig()
        self.default_content = ContentConfig()
        
        self.logger.info(f"Professional template engine initialized with templates in: {templates_dir}")
    
    def render_report(self, 
                     template_name: str,
                     data: Dict[str, Any],
                     branding: Optional[BrandingConfig] = None,
                     layout: Optional[LayoutConfig] = None,
                     content: Optional[ContentConfig] = None,
                     ai_analysis: Optional[Dict[str, Any]] = None) -> str:
        """Render a professional report using the specified template."""
        
        # Use defaults if not provided
        branding = branding or self.default_branding
        layout = layout or self.default_layout
        content = content or self.default_content
        
        # Enhanced context with AI analysis
        context = {
            **data,
            'ai_analysis': ai_analysis,
            'ai_provider': ai_analysis.get('provider', 'Not configured') if ai_analysis else 'Not configured',
            'privacy_mode': content.enable_ai_insights,
            'render_timestamp': datetime.now().isoformat()
        }
        
        try:
            rendered = self.template_manager.render_template(
                template_name, 
                context, 
                branding, 
                layout, 
                content
            )
            
            self.logger.info(f"Successfully rendered template: {template_name}")
            return rendered
            
        except Exception as e:
            self.logger.error(f"Template rendering failed: {str(e)}")
            raise
    
    def create_custom_template(self, 
                              template_name: str,
                              template_content: str,
                              template_type: str = "html") -> Path:
        """Create a custom template."""
        
        template_path = self.templates_dir / f"{template_name}.{template_type}"
        
        try:
            template_path.write_text(template_content, encoding='utf-8')
            self.logger.info(f"Created custom template: {template_path}")
            return template_path
            
        except Exception as e:
            self.logger.error(f"Failed to create custom template: {str(e)}")
            raise
    
    def get_available_templates(self) -> List[str]:
        """Get list of available templates."""
        templates = []
        
        for template_file in self.templates_dir.glob("*.html"):
            templates.append(template_file.stem)
        
        return sorted(templates)
    
    def validate_template(self, template_name: str) -> Dict[str, Any]:
        """Validate template syntax and structure."""
        
        try:
            template = self.template_manager.load_template(template_name)
            
            # Test render with minimal context
            test_context = {
                'client_profile': {'project_name': 'Test Project'},
                'financial_results': {'kpis': {}},
                'assumptions': {},
                'charts': {},
                'branding': self.default_branding,
                'layout': self.default_layout,
                'content': self.default_content
            }
            
            template.render(test_context)
            
            return {
                'valid': True,
                'template': template_name,
                'message': 'Template validation successful'
            }
            
        except Exception as e:
            return {
                'valid': False,
                'template': template_name,
                'error': str(e),
                'message': 'Template validation failed'
            }
    
    def update_branding(self, branding_config: BrandingConfig):
        """Update default branding configuration."""
        self.default_branding = branding_config
        self.logger.info("Updated default branding configuration")
    
    def update_layout(self, layout_config: LayoutConfig):
        """Update default layout configuration."""
        self.default_layout = layout_config
        self.logger.info("Updated default layout configuration")
    
    def update_content(self, content_config: ContentConfig):
        """Update default content configuration."""
        self.default_content = content_config
        self.logger.info("Updated default content configuration")
    
    def export_configuration(self, output_path: Path):
        """Export current configuration to file."""
        config_data = {
            'branding': self.default_branding.__dict__,
            'layout': self.default_layout.__dict__,
            'content': self.default_content.__dict__,
            'export_timestamp': datetime.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Configuration exported to: {output_path}")
    
    def import_configuration(self, config_path: Path):
        """Import configuration from file."""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        if 'branding' in config_data:
            self.default_branding = BrandingConfig(**config_data['branding'])
        
        if 'layout' in config_data:
            self.default_layout = LayoutConfig(**config_data['layout'])
        
        if 'content' in config_data:
            self.default_content = ContentConfig(**config_data['content'])
        
        self.logger.info(f"Configuration imported from: {config_path}")
    
    def get_template_info(self, template_name: str) -> Dict[str, Any]:
        """Get information about a template."""
        template_path = self.templates_dir / f"{template_name}.html"
        
        if not template_path.exists():
            return {'exists': False, 'template': template_name}
        
        stat = template_path.stat()
        
        return {
            'exists': True,
            'template': template_name,
            'path': str(template_path),
            'size': stat.st_size,
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'validation': self.validate_template(template_name)
        }