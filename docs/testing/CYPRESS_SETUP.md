# Cypress E2E Testing Setup

## Overview
This document provides setup instructions and usage guidelines for the Cypress end-to-end testing framework in the Hiel RnE Modeler application.

## Prerequisites

### Software Requirements
- Node.js (v16 or higher)
- npm or yarn package manager
- Python 3.8+ (for running the Flet application)
- Chrome or Firefox browser

### Application Setup
1. **Start the Hiel RnE Modeler application:**
   ```bash
   python main.py
   ```

2. **Verify the application is running:**
   - Open browser to `http://localhost:8550`
   - Confirm the application loads successfully

## Installation

### 1. Install Dependencies
```bash
npm install
```

### 2. Verify Cypress Installation
```bash
npx cypress verify
```

## Running Tests

### Interactive Mode (Cypress Test Runner)
```bash
npm run cypress:open
```

### Headless Mode (CI/CD)
```bash
npm run cypress:run
```

### Specific Test Suites
```bash
# Run accessibility tests
npm run test:accessibility

# Run location comparison tests
npm run test:location-comparison

# Run all E2E tests
npm run test:e2e
```

### Browser-Specific Testing
```bash
# Run tests in Chrome
npm run cypress:run:chrome

# Run tests in Firefox
npm run cypress:run:firefox
```

## Test Structure

### Directory Layout
```
cypress/
├── e2e/
│   ├── create_project_location_comparison.cy.js
│   └── accessibility_location_selector.cy.js
├── fixtures/
│   └── test-data.json
├── support/
│   ├── commands.js
│   └── e2e.js
├── screenshots/
└── videos/
```

### Test Files

#### 1. create_project_location_comparison.cy.js
- **Purpose:** End-to-end workflow testing
- **Scenario:** Create project in Ouarzazate → Select Marrakech & Agadir → Verify radar chart shows 3 series
- **Coverage:** Complete user journey from project creation to location comparison

#### 2. accessibility_location_selector.cy.js
- **Purpose:** Accessibility compliance testing
- **Coverage:** 
  - WCAG 2.1 Level AA compliance
  - Keyboard navigation
  - Screen reader compatibility
  - High contrast mode support
  - ARIA attributes validation

## Custom Commands

### Authentication
```javascript
cy.login() // Uses default admin password
cy.login('custom-password') // Custom password
```

### Project Management
```javascript
cy.createProject({
  name: 'Test Project',
  location: 'Ouarzazate',
  companyName: 'Test Company'
})
```

### Location Comparison
```javascript
cy.selectLocations(['Marrakech', 'Agadir'])
cy.runLocationComparison()
cy.validateRadarChart(3) // Expects 3 series
```

### Accessibility Testing
```javascript
cy.checkAccessibility('[data-cy=location-selector]')
cy.testLocationSelectorKeyboard()
cy.validateFormAccessibility('[data-cy=form]')
```

## Test Data Management

### Fixtures
Test data is stored in `cypress/fixtures/`:
- `test-data.json`: Sample project data
- `locations.json`: Available locations list
- `validation-messages.json`: Expected validation messages

### Example Usage
```javascript
cy.fixture('test-data').then((data) => {
  cy.createProject(data.sampleProject)
})
```

## Accessibility Testing

### WCAG 2.1 Compliance
The accessibility tests validate compliance with:
- **Level A:** Basic accessibility features
- **Level AA:** Standard compliance (target level)
- **Level AAA:** Enhanced accessibility (aspirational)

### Tested Components
- Location selector checkboxes
- Navigation tabs
- Form validation messages
- Loading states
- Error handling

### Screen Reader Testing
Tests validate announcements for:
- Selection changes
- Validation feedback
- Loading states
- Error messages
- Success confirmations

## Continuous Integration

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: cypress-io/github-action@v5
        with:
          start: python main.py
          wait-on: 'http://localhost:8550'
          browser: chrome
```

### Test Reports
- **Screenshots:** Captured on test failure
- **Videos:** Recorded for all test runs
- **Accessibility Reports:** WCAG compliance summary
- **Coverage Reports:** Test coverage metrics

## Best Practices

### Test Organization
1. **Descriptive Test Names:** Use clear, descriptive test names
2. **Page Object Pattern:** Use custom commands for reusable actions
3. **Data-Cy Attributes:** Use `data-cy` attributes for reliable element selection
4. **Assertions:** Include meaningful assertions with custom messages

### Performance Considerations
1. **Selective Testing:** Run relevant tests based on code changes
2. **Parallel Execution:** Use Cypress Dashboard for parallel test runs
3. **Test Isolation:** Ensure tests are independent and can run in any order
4. **Cleanup:** Clean up test data after each test run

### Accessibility Testing
1. **Automated Checks:** Use cypress-axe for automated accessibility testing
2. **Manual Validation:** Complement automated tests with manual accessibility reviews
3. **Real Users:** Involve users with disabilities in testing when possible
4. **Documentation:** Document accessibility features and testing procedures

## Troubleshooting

### Common Issues

#### Application Not Starting
```bash
# Check if port is in use
netstat -an | grep 8550

# Kill process using port
kill -9 $(lsof -t -i:8550)
```

#### Test Failures
```bash
# Clear Cypress cache
npx cypress cache clear

# Verify installation
npx cypress verify

# Run tests with debug output
DEBUG=cypress:* npm run cypress:run
```

#### Accessibility Test Failures
1. **Check ARIA attributes:** Verify all interactive elements have proper ARIA labels
2. **Test keyboard navigation:** Ensure all elements are keyboard accessible
3. **Validate color contrast:** Check color contrast ratios meet WCAG guidelines
4. **Review error messages:** Ensure error messages are announced to screen readers

### Debug Mode
```bash
# Run with debug output
DEBUG=cypress:* npm run cypress:open

# Run specific test with debug
npx cypress run --spec "cypress/e2e/accessibility_location_selector.cy.js" --headed
```

## Contributing

### Adding New Tests
1. Create test file in `cypress/e2e/`
2. Follow existing naming conventions
3. Include accessibility tests for new components
4. Update this documentation

### Code Review Checklist
- [ ] Test covers happy path and error scenarios
- [ ] Accessibility compliance validated
- [ ] Custom commands used appropriately
- [ ] Test data properly managed
- [ ] Documentation updated

## Resources

### Documentation
- [Cypress Documentation](https://docs.cypress.io/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Flet Documentation](https://flet.dev/)

### Tools
- [cypress-axe](https://github.com/component-driven/cypress-axe) - Accessibility testing
- [cypress-real-events](https://github.com/dmtrKovalenko/cypress-real-events) - Real browser events
- [Cypress Dashboard](https://dashboard.cypress.io/) - Test analytics and parallelization

---

**Document Version:** 1.0  
**Last Updated:** July 17, 2024  
**Maintainer:** Development Team
