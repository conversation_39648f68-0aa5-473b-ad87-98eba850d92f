"""
Cashflow Charts
===============

Chart components for cashflow analysis and visualization.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import pandas as pd

from .chart_factory import ChartFactory


class CashflowCharts:
    """Chart components for cashflow visualization."""
    
    def __init__(self):
        self.chart_factory = ChartFactory()
    
    def create_cashflow_timeline_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create cashflow timeline visualization."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Sample years for visualization (every 5 years)
        sample_years = [1, 5, 10, 15, 20, 25]
        available_years = [year for year in sample_years if year in df.index]
        
        timeline_content = ft.Column([
            ft.Text("💰 Cash Flow Timeline", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10),
            
            # Header row
            ft.Row([
                ft.Text("Year", size=12, weight=ft.FontWeight.BOLD, expand=1),
                ft.Text("Revenue", size=12, weight=ft.FontWeight.BOLD, expand=2),
                ft.Text("Equity CF", size=12, weight=ft.FontWeight.BOLD, expand=2)
            ]),
            ft.Divider(height=5),
            
            # Data rows
            *[
                ft.Row([
                    ft.Text(f"{year}", size=12, expand=1),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                width=max(5, (df.loc[year, 'Revenue'] / 1e6) * 15),
                                height=15,
                                bgcolor=ft.Colors.GREEN_400,
                                border_radius=3
                            ),
                            ft.Text(f"€{df.loc[year, 'Revenue'] / 1e6:.1f}M", size=10)
                        ]),
                        expand=2
                    ),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                width=max(5, abs(df.loc[year, 'Equity_CF'] / 1e6) * 20),
                                height=15,
                                bgcolor=ft.Colors.BLUE_400 if df.loc[year, 'Equity_CF'] > 0 else ft.Colors.RED_400,
                                border_radius=3
                            ),
                            ft.Text(f"€{df.loc[year, 'Equity_CF'] / 1e6:.1f}M", size=10)
                        ]),
                        expand=2
                    )
                ]) for year in available_years
            ]
        ])
        
        return ft.Container(
            content=timeline_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_dscr_timeline_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create DSCR timeline visualization."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Get debt years (typically first 15 years)
        debt_years = [year for year in range(1, min(16, len(df))) if year in df.index]
        
        dscr_content = ft.Column([
            ft.Text("📊 DSCR Timeline", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Text("Minimum DSCR: 1.20 (Covenant)", 
                   size=12, color=ft.Colors.GREY_600,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10),
            
            *[
                ft.Row([
                    ft.Text(f"Year {year}", size=12, expand=1),
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                width=max(20, df.loc[year, 'DSCR'] * 50),
                                height=15,
                                bgcolor=self._get_dscr_color(df.loc[year, 'DSCR']),
                                border_radius=3
                            ),
                            ft.Text(f"{df.loc[year, 'DSCR']:.2f}", size=10, weight=ft.FontWeight.BOLD)
                        ]),
                        expand=2
                    ),
                    ft.Icon(
                        ft.Icons.CHECK_CIRCLE if df.loc[year, 'DSCR'] >= 1.20 else ft.Icons.WARNING,
                        color=ft.Colors.GREEN if df.loc[year, 'DSCR'] >= 1.20 else ft.Colors.ORANGE,
                        size=16
                    )
                ]) for year in debt_years[:10]  # Show first 10 years
            ],
            
            ft.Divider(height=10),
            ft.Row([
                ft.Text("Covenant Line (1.20)", size=10, color=ft.Colors.GREY_600),
                ft.Container(width=60, height=2, bgcolor=ft.Colors.RED)
            ], alignment=ft.MainAxisAlignment.CENTER)
        ])
        
        return ft.Container(
            content=dscr_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _get_dscr_color(self, dscr_value: float) -> str:
        """Get color based on DSCR value."""
        if dscr_value >= 1.35:
            return ft.Colors.GREEN
        elif dscr_value >= 1.20:
            return ft.Colors.ORANGE
        else:
            return ft.Colors.RED
    
    def create_cashflow_waterfall(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create cashflow waterfall chart."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Calculate waterfall components
        total_revenue = df['Revenue'].sum() / 1e6
        total_opex = abs(df['OPEX'].sum()) / 1e6
        total_capex = abs(df['Capex'].sum()) / 1e6
        total_grants = df['Grants'].sum() / 1e6
        total_debt_service = abs(df['Debt_Service'].sum()) / 1e6 if 'Debt_Service' in df.columns else 0
        net_cashflow = df['Equity_CF'].sum() / 1e6
        
        categories = ["Revenue", "OPEX", "CAPEX", "Grants", "Debt Service", "Net CF"]
        values = [total_revenue, -total_opex, -total_capex, total_grants, -total_debt_service, net_cashflow]
        
        return self.chart_factory.create_waterfall_chart(categories, values, "Project Cashflow Waterfall (M EUR)")
    
    def create_annual_cashflow_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create annual cashflow chart."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Prepare data for line chart
        df_chart = df.copy()
        df_chart['Year'] = df_chart.index
        df_chart['Revenue_M'] = df_chart['Revenue'] / 1e6
        df_chart['OPEX_M'] = df_chart['OPEX'] / 1e6
        df_chart['Equity_CF_M'] = df_chart['Equity_CF'] / 1e6
        
        return self.chart_factory.create_line_chart(
            df_chart,
            "Annual Cashflow Analysis",
            'Year',
            ['Revenue_M', 'OPEX_M', 'Equity_CF_M'],
            "Year",
            "Million EUR"
        )
    
    def create_cumulative_cashflow_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create cumulative cashflow chart."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Calculate cumulative cashflows
        df_chart = df.copy()
        df_chart['Year'] = df_chart.index
        df_chart['Cumulative_Equity_CF'] = df_chart['Equity_CF'].cumsum() / 1e6
        df_chart['Cumulative_Project_CF'] = df_chart['Project_CF'].cumsum() / 1e6 if 'Project_CF' in df_chart.columns else 0
        
        return self.chart_factory.create_line_chart(
            df_chart,
            "Cumulative Cashflow Analysis",
            'Year',
            ['Cumulative_Equity_CF', 'Cumulative_Project_CF'],
            "Year",
            "Million EUR"
        )
    
    def create_cashflow_summary_visual(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create visual cashflow summary."""
        if not financial_results:
            return ft.Container()
        
        cashflow_data = financial_results.get('cashflow')
        if cashflow_data is None:
            return ft.Container()
        
        # Convert to DataFrame if needed
        if isinstance(cashflow_data, dict):
            df = pd.DataFrame(cashflow_data)
        else:
            df = cashflow_data.copy()
        
        # Calculate summary metrics
        total_revenue = df['Revenue'].sum() / 1e6
        total_opex = abs(df['OPEX'].sum()) / 1e6
        total_capex = abs(df['Capex'].sum()) / 1e6
        total_grants = df['Grants'].sum() / 1e6
        
        summary_cards = ft.Row([
            self._create_summary_metric_card(
                "Total Revenue",
                f"€{total_revenue:.1f}M",
                ft.Colors.GREEN,
                ft.Icons.TRENDING_UP
            ),
            self._create_summary_metric_card(
                "Total OPEX",
                f"€{total_opex:.1f}M",
                ft.Colors.RED,
                ft.Icons.TRENDING_DOWN
            ),
            self._create_summary_metric_card(
                "Total CAPEX",
                f"€{total_capex:.1f}M",
                ft.Colors.BLUE,
                ft.Icons.BUILD
            ),
            self._create_summary_metric_card(
                "Total Grants",
                f"€{total_grants:.1f}M",
                ft.Colors.PURPLE,
                ft.Icons.CARD_GIFTCARD
            )
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Project Financial Summary", 
                       size=16, weight=ft.FontWeight.BOLD,
                       text_align=ft.TextAlign.CENTER),
                ft.Divider(height=10),
                summary_cards
            ]),
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _create_summary_metric_card(self, title: str, value: str, color: str, icon: str) -> ft.Container:
        """Create a summary metric card."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=color, size=30),
                ft.Text(title, size=12, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER),
                ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=color, text_align=ft.TextAlign.CENTER)
            ], alignment=ft.MainAxisAlignment.CENTER),
            width=120,
            height=100,
            padding=10,
            bgcolor=f"{color}10",  # Light background
            border_radius=8,
            border=ft.border.all(1, color)
        )
