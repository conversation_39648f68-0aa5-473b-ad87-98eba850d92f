"""
KPI Charts
==========

Chart components for Key Performance Indicators visualization.
"""

import flet as ft
from typing import Dict, Any, Optional
import pandas as pd

from .chart_factory import ChartFactory


class KPICharts:
    """Chart components for KPI visualization."""
    
    def __init__(self):
        self.chart_factory = ChartFactory()
    
    def create_kpi_gauge_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create KPI gauge chart with multiple gauges."""
        if not financial_results:
            return ft.Container()
        
        kpis = financial_results.get('kpis', {})
        
        # Create individual gauges
        gauges = ft.Row([
            self.chart_factory.create_kpi_gauge(
                "Project IRR",
                kpis.get('IRR_project', 0) * 100,
                12.0,  # Target 12%
                25.0,  # Max 25%
                "%"
            ),
            self.chart_factory.create_kpi_gauge(
                "Equity IRR",
                kpis.get('IRR_equity', 0) * 100,
                15.0,  # Target 15%
                30.0,  # Max 30%
                "%"
            ),
            self.chart_factory.create_kpi_gauge(
                "Min DSCR",
                kpis.get('Min_DSCR', 0),
                1.25,  # Target 1.25
                3.0,   # Max 3.0
                ""
            ),
            self.chart_factory.create_kpi_gauge(
                "LCOE",
                kpis.get('LCOE_eur_kwh', 0) * 1000,  # Convert to c€/kWh
                45.0,  # Target 4.5 c€/kWh
                80.0,  # Max 8.0 c€/kWh
                " c€/kWh"
            )
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        
        return ft.Container(
            content=ft.Column([
                ft.Text("Key Performance Indicators", 
                       size=16, weight=ft.FontWeight.BOLD,
                       text_align=ft.TextAlign.CENTER),
                gauges
            ]),
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_irr_comparison_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create IRR comparison chart."""
        if not financial_results:
            return ft.Container()
        
        kpis = financial_results.get('kpis', {})
        irr_project = kpis.get('IRR_project', 0) * 100
        irr_equity = kpis.get('IRR_equity', 0) * 100
        
        # Industry benchmarks
        benchmark_project = 12.0
        benchmark_equity = 15.0
        
        comparison_content = ft.Column([
            ft.Text("IRR vs Industry Benchmarks", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10),
            
            # Project IRR comparison
            ft.Column([
                ft.Text("Project IRR", size=14, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Text("Current:", size=12, expand=1),
                    ft.Container(
                        width=max(10, irr_project * 8),
                        height=20,
                        bgcolor=ft.Colors.GREEN if irr_project >= benchmark_project else ft.Colors.ORANGE,
                        border_radius=3
                    ),
                    ft.Text(f"{irr_project:.1f}%", size=12, weight=ft.FontWeight.BOLD)
                ]),
                ft.Row([
                    ft.Text("Benchmark:", size=10, color=ft.Colors.GREY_600, expand=1),
                    ft.Container(
                        width=benchmark_project * 8,
                        height=15,
                        bgcolor=ft.Colors.GREY_400,
                        border_radius=3
                    ),
                    ft.Text(f"{benchmark_project:.1f}%", size=10, color=ft.Colors.GREY_600)
                ])
            ]),
            
            ft.Divider(height=15),
            
            # Equity IRR comparison
            ft.Column([
                ft.Text("Equity IRR", size=14, weight=ft.FontWeight.BOLD),
                ft.Row([
                    ft.Text("Current:", size=12, expand=1),
                    ft.Container(
                        width=max(10, irr_equity * 6),
                        height=20,
                        bgcolor=ft.Colors.BLUE if irr_equity >= benchmark_equity else ft.Colors.ORANGE,
                        border_radius=3
                    ),
                    ft.Text(f"{irr_equity:.1f}%", size=12, weight=ft.FontWeight.BOLD)
                ]),
                ft.Row([
                    ft.Text("Benchmark:", size=10, color=ft.Colors.GREY_600, expand=1),
                    ft.Container(
                        width=benchmark_equity * 6,
                        height=15,
                        bgcolor=ft.Colors.GREY_400,
                        border_radius=3
                    ),
                    ft.Text(f"{benchmark_equity:.1f}%", size=10, color=ft.Colors.GREY_600)
                ])
            ])
        ])
        
        return ft.Container(
            content=comparison_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_kpi_summary_table(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create KPI summary table."""
        if not financial_results:
            return ft.Container()
        
        kpis = financial_results.get('kpis', {})
        
        # Prepare table data
        table_data = [
            {"Metric": "Project IRR", "Value": f"{kpis.get('IRR_project', 0):.1%}", "Target": "≥12%", "Status": "✓" if kpis.get('IRR_project', 0) >= 0.12 else "⚠"},
            {"Metric": "Equity IRR", "Value": f"{kpis.get('IRR_equity', 0):.1%}", "Target": "≥15%", "Status": "✓" if kpis.get('IRR_equity', 0) >= 0.15 else "⚠"},
            {"Metric": "NPV Project", "Value": f"€{kpis.get('NPV_project', 0)/1e6:.1f}M", "Target": ">0", "Status": "✓" if kpis.get('NPV_project', 0) > 0 else "✗"},
            {"Metric": "NPV Equity", "Value": f"€{kpis.get('NPV_equity', 0)/1e6:.1f}M", "Target": ">0", "Status": "✓" if kpis.get('NPV_equity', 0) > 0 else "✗"},
            {"Metric": "LCOE", "Value": f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh", "Target": "≤0.045", "Status": "✓" if kpis.get('LCOE_eur_kwh', 0) <= 0.045 else "⚠"},
            {"Metric": "Min DSCR", "Value": f"{kpis.get('Min_DSCR', 0):.2f}", "Target": "≥1.25", "Status": "✓" if kpis.get('Min_DSCR', 0) >= 1.25 else "⚠"},
            {"Metric": "Payback", "Value": f"{kpis.get('Payback_years', 0):.1f} years", "Target": "≤10 years", "Status": "✓" if kpis.get('Payback_years', 0) <= 10 else "⚠"}
        ]
        
        return self.chart_factory.create_comparison_table(table_data, "KPI Summary")
    
    def create_financial_ratios_chart(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create financial ratios visualization."""
        if not financial_results:
            return ft.Container()
        
        kpis = financial_results.get('kpis', {})
        assumptions = financial_results.get('assumptions', {})
        
        # Calculate additional ratios
        debt_ratio = assumptions.get('debt_ratio', 0)
        equity_ratio = 1 - debt_ratio
        
        # Create pie chart for financing structure
        financing_data = {
            "Debt": debt_ratio * 100,
            "Equity": equity_ratio * 100
        }
        
        financing_chart = self.chart_factory.create_pie_chart(
            financing_data, 
            "Financing Structure"
        )
        
        # Create ratios summary
        ratios_content = ft.Column([
            ft.Text("Financial Ratios", size=16, weight=ft.FontWeight.BOLD),
            ft.Divider(height=10),
            ft.Row([
                ft.Text("Debt/Equity Ratio:", expand=1),
                ft.Text(f"{debt_ratio/(1-debt_ratio):.2f}" if debt_ratio < 1 else "∞")
            ]),
            ft.Row([
                ft.Text("Equity Multiplier:", expand=1),
                ft.Text(f"{1/(1-debt_ratio):.2f}" if debt_ratio < 1 else "∞")
            ]),
            ft.Row([
                ft.Text("Interest Coverage:", expand=1),
                ft.Text(f"{kpis.get('Avg_DSCR', 0):.2f}")
            ])
        ])
        
        return ft.Container(
            content=ft.Row([
                ft.Container(content=financing_chart, expand=1),
                ft.Container(content=ratios_content, expand=1, padding=20)
            ]),
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def create_performance_dashboard(self, financial_results: Dict[str, Any]) -> ft.Container:
        """Create comprehensive performance dashboard."""
        if not financial_results:
            return ft.Container()
        
        # Combine multiple KPI visualizations
        dashboard_content = ft.Column([
            ft.Text("Performance Dashboard", 
                   size=18, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=20),
            
            # Top row: Gauges
            self.create_kpi_gauge_chart(financial_results),
            
            ft.Divider(height=20),
            
            # Middle row: Comparisons and ratios
            ft.Row([
                ft.Container(
                    content=self.create_irr_comparison_chart(financial_results),
                    expand=1
                ),
                ft.Container(
                    content=self.create_financial_ratios_chart(financial_results),
                    expand=1
                )
            ]),
            
            ft.Divider(height=20),
            
            # Bottom row: Summary table
            self.create_kpi_summary_table(financial_results)
        ])
        
        return ft.Container(
            content=dashboard_content,
            padding=20,
            bgcolor=ft.Colors.GREY_50,
            border_radius=10
        )
