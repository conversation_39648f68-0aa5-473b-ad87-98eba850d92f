"""
Comparison Charts
=================

Chart components for location and scenario comparisons.
Refactored to consume pre-generated analytics with fallback to on-demand fetch.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import pandas as pd
import logging

from components.charts.chart_factory import ChartFactory
from models.project_analytics import ProjectAnalytics
from services.project_service import ProjectService
from services.location_service import LocationComparisonService


class ComparisonCharts:
    """Chart components for comparison analysis."""
    
    def __init__(self, project_service: Optional[ProjectService] = None):
        self.chart_factory = ChartFactory()
        self.project_service = project_service or ProjectService()
        self.location_service = LocationComparisonService()
        self.logger = logging.getLogger(__name__)
    
    def create_location_comparison_chart(self, 
                                       project_id: Optional[str] = None,
                                       comparison_results: Optional[Dict[str, Any]] = None) -> ft.Container:
        """Create location comparison chart.
        
        Args:
            project_id: Project ID to fetch pre-generated analytics
            comparison_results: Fallback comparison results if analytics not available
            
        Returns:
            ft.Container: Chart container
        """
        # Try to get pre-generated analytics first
        analytics_data = None
        if project_id:
            analytics_data = self._get_analytics_location_comparisons(project_id)
        
        # Use pre-generated data if available, otherwise fall back to provided data
        final_data = analytics_data or comparison_results
        
        if not final_data:
            return self._create_no_data_container("No comparison data available")
        
        analysis = final_data.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return self._create_no_data_container("No comparison matrix available")
        
        # Extract data for chart
        locations = [item['Location'] for item in comparison_matrix]
        irr_values = [item.get('IRR_Project', 0) * 100 for item in comparison_matrix]
        
        irr_data = dict(zip(locations, irr_values))
        
        return self.chart_factory.create_bar_chart(
            irr_data,
            "Project IRR by Location",
            "Location",
            "IRR (%)"
        )
    
    def _get_analytics_location_comparisons(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get location comparisons from pre-generated analytics."""
        try:
            analytics = self.project_service.get_project_analytics(project_id)
            if analytics and analytics.location_comparisons:
                self.logger.info(f"Using pre-generated location comparisons for project {project_id}")
                return analytics.location_comparisons
        except Exception as e:
            self.logger.warning(f"Failed to load analytics for project {project_id}: {e}")
        
        return None
    
    def _create_no_data_container(self, message: str) -> ft.Container:
        """Create a container for no data scenarios."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.BAR_CHART_OUTLINED, size=48, color=ft.Colors.GREY_400),
                ft.Text(message, size=14, color=ft.Colors.GREY_600, text_align=ft.TextAlign.CENTER)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=10),
            alignment=ft.alignment.center,
            padding=40,
            bgcolor=ft.Colors.GREY_50,
            border_radius=8
        )

    def create_location_ranking_chart(self, comparison_results: Dict[str, Any]) -> ft.Container:
        """Create location ranking visualization."""
        if not comparison_results:
            return ft.Container()
        
        analysis = comparison_results.get('analysis', {})
        rankings = analysis.get('rankings', {})
        
        # Create ranking display for multiple metrics
        ranking_content = ft.Column([
            ft.Text("Location Rankings", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10)
        ])
        
        metrics = [
            ("Best Project IRR", rankings.get('best_irr_project', [])),
            ("Best Equity IRR", rankings.get('best_irr_equity', [])),
            ("Lowest LCOE", rankings.get('lowest_lcoe', []))
        ]
        
        for metric_name, ranking_data in metrics:
            if ranking_data:
                metric_content = ft.Column([
                    ft.Text(metric_name, size=14, weight=ft.FontWeight.BOLD),
                    *[
                        ft.Row([
                            ft.Container(
                                content=ft.Text(str(item['rank']), 
                                               color=ft.Colors.WHITE, 
                                               weight=ft.FontWeight.BOLD),
                                width=25,
                                height=25,
                                bgcolor=self._get_rank_color(item['rank']),
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(item['location'], expand=1),
                            ft.Text(f"{item['value']:.3f}" if isinstance(item['value'], float) else str(item['value']))
                        ]) for item in ranking_data[:3]  # Top 3
                    ]
                ])
                ranking_content.controls.append(metric_content)
                ranking_content.controls.append(ft.Divider(height=10))
        
        return ft.Container(
            content=ranking_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _get_rank_color(self, rank: int) -> str:
        """Get color based on ranking."""
        if rank == 1:
            return ft.Colors.AMBER
        elif rank == 2:
            return ft.Colors.GREY_400  # Silver
        elif rank == 3:
            return ft.Colors.BROWN_700  # Bronze
        else:
            return ft.Colors.GREY_600
    
    def create_scenario_comparison_chart(self, scenario_results: Dict[str, Any]) -> ft.Container:
        """Create scenario comparison chart."""
        if not scenario_results:
            return ft.Container()
        
        # Extract scenario data
        scenario_data = {}
        for scenario_name, results in scenario_results.items():
            if 'kpis' in results:
                irr = results['kpis'].get('IRR_project', 0) * 100
                scenario_data[scenario_name] = irr
        
        if not scenario_data:
            return ft.Container(
                content=ft.Text("No scenario data available"),
                alignment=ft.alignment.center
            )
        
        return self.chart_factory.create_bar_chart(
            scenario_data,
            "Project IRR by Scenario",
            "Scenario",
            "IRR (%)"
        )
    
    def create_sensitivity_tornado_chart(self, sensitivity_results: pd.DataFrame) -> ft.Container:
        """Create tornado chart for sensitivity analysis."""
        if sensitivity_results is None or sensitivity_results.empty:
            return ft.Container(
                content=ft.Text("No sensitivity data available"),
                alignment=ft.alignment.center
            )
        
        # Placeholder for tornado chart
        return ft.Container(
            content=ft.Text("Sensitivity tornado chart would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def create_monte_carlo_distribution_chart(self, monte_carlo_results: Dict[str, Any]) -> ft.Container:
        """Create Monte Carlo distribution chart."""
        if not monte_carlo_results:
            return ft.Container()
        
        # Placeholder for Monte Carlo distribution
        return ft.Container(
            content=ft.Text("Monte Carlo distribution chart would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def create_comparison_matrix_heatmap(self, comparison_data: List[Dict[str, Any]]) -> ft.Container:
        """Create comparison matrix heatmap."""
        if not comparison_data:
            return ft.Container()
        
        # Placeholder for heatmap
        return ft.Container(
            content=ft.Text("Comparison matrix heatmap would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def create_location_comparison_radar(self, 
                                       project_id: Optional[str] = None,
                                       comparison_results: Optional[Dict[str, Any]] = None,
                                       metrics: Optional[List[str]] = None) -> ft.Container:
        """Create radar chart for location comparison.
        
        This method provides a unified rendering path for both the Project Dashboard
        and the dedicated Location Comparison screen.
        
        Args:
            project_id: Project ID to fetch pre-generated analytics
            comparison_results: Fallback comparison results if analytics not available
            metrics: List of metrics to include in radar chart
            
        Returns:
            ft.Container: Radar chart container
        """
        # Try to get pre-generated analytics first
        analytics_data = None
        if project_id:
            analytics_data = self._get_analytics_location_comparisons(project_id)
        
        # Use pre-generated data if available, otherwise fall back to provided data
        final_data = analytics_data or comparison_results
        
        if not final_data:
            return self._create_no_data_container("No comparison data available for radar chart")
        
        analysis = final_data.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return self._create_no_data_container("No comparison matrix available for radar chart")
        
        # Default metrics for radar chart
        if not metrics:
            metrics = ['IRR_Project', 'IRR_Equity', 'NPV_Project_MEUR', 'Min_DSCR', 'Capacity_Factor']
        
        # Extract data for radar chart
        radar_data = self._prepare_radar_data(comparison_matrix, metrics)
        
        # Create radar chart using chart factory
        return self.chart_factory.create_radar_chart(
            radar_data,
            "Location Comparison Radar",
            metrics
        )
    
    def _prepare_radar_data(self, comparison_matrix: List[Dict[str, Any]], metrics: List[str]) -> Dict[str, List[float]]:
        """Prepare data for radar chart."""
        radar_data = {}
        
        for location_data in comparison_matrix:
            location = location_data['Location']
            values = []
            
            for metric in metrics:
                # Get the value and normalize it for radar chart
                value = location_data.get(metric, 0)
                
                # Normalize values based on metric type
                if metric in ['IRR_Project', 'IRR_Equity']:
                    # IRR: normalize to percentage (0-100)
                    normalized_value = value * 100
                elif metric == 'NPV_Project_MEUR':
                    # NPV: normalize to positive range (assuming max possible NPV)
                    normalized_value = max(0, value)
                elif metric == 'Min_DSCR':
                    # DSCR: normalize to 0-10 range
                    normalized_value = min(10, max(0, value))
                elif metric == 'Capacity_Factor':
                    # Capacity Factor: normalize to percentage (0-100)
                    normalized_value = value * 100
                elif metric == 'LCOE_EUR_kWh':
                    # LCOE: invert so lower is better (higher on radar)
                    normalized_value = max(0, 0.1 - value) * 1000  # Scale for visibility
                else:
                    normalized_value = value
                
                values.append(normalized_value)
            
            radar_data[location] = values
        
        return radar_data
    
    def create_unified_location_comparison(self, 
                                         project_id: Optional[str] = None,
                                         comparison_results: Optional[Dict[str, Any]] = None,
                                         chart_type: str = 'bar',
                                         include_rankings: bool = True) -> ft.Container:
        """Create unified location comparison visualization.
        
        This method provides a single entry point for location comparison charts,
        ensuring both dashboard and dedicated comparison screens use the same rendering logic.
        
        Args:
            project_id: Project ID to fetch pre-generated analytics
            comparison_results: Fallback comparison results if analytics not available
            chart_type: Type of chart ('bar', 'radar', 'heatmap')
            include_rankings: Whether to include rankings alongside the chart
            
        Returns:
            ft.Container: Unified comparison visualization container
        """
        # Try to get pre-generated analytics first
        analytics_data = None
        if project_id:
            analytics_data = self._get_analytics_location_comparisons(project_id)
        
        # Use pre-generated data if available, otherwise fall back to provided data
        final_data = analytics_data or comparison_results
        
        if not final_data:
            return self._create_no_data_container("No comparison data available")
        
        # Create main chart based on type
        if chart_type == 'bar':
            main_chart = self.create_location_comparison_chart(project_id, final_data)
        elif chart_type == 'radar':
            main_chart = self.create_location_comparison_radar(project_id, final_data)
        elif chart_type == 'heatmap':
            analysis = final_data.get('analysis', {})
            comparison_matrix = analysis.get('comparison_matrix', [])
            main_chart = self.create_comparison_matrix_heatmap(comparison_matrix)
        else:
            main_chart = self.create_location_comparison_chart(project_id, final_data)
        
        # Create container with main chart
        components = [main_chart]
        
        # Add rankings if requested
        if include_rankings:
            rankings_chart = self.create_location_ranking_chart(final_data)
            if rankings_chart:
                components.append(ft.Container(height=20))  # Spacing
                components.append(rankings_chart)
        
        return ft.Container(
            content=ft.Column(components),
            padding=10,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
