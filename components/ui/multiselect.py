"""
Multiselect UI Component
========================

A multiselect component for Flet applications that provides MUI Autocomplete-like functionality.
"""

import flet as ft
from typing import List, Dict, Callable, Optional, Any


class MultiSelectOption:
    """Represents a single option in the multiselect."""
    
    def __init__(self, key: str, text: str, data: Optional[Dict[str, Any]] = None):
        self.key = key
        self.text = text
        self.data = data or {}
    
    def __str__(self):
        return self.text


class MultiSelectControl:
    """A multiselect dropdown control."""
    
    def __init__(
        self,
        label: str,
        options: List[MultiSelectOption],
        selected_keys: List[str] = None,
        on_change: Optional[Callable[[List[str]], None]] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        disabled: bool = False,
        max_height: int = 200,
        hint_text: str = "Select items...",
        **kwargs
    ):
        # Initialize control content
        self._control_content = None
        
        self.label = label
        self.options = options
        self.selected_keys = selected_keys or []
        self.on_change = on_change
        self.width = width
        self.height = height
        self.disabled = disabled
        self.max_height = max_height
        self.hint_text = hint_text
        
        # UI components
        self.dropdown_open = False
        self.search_text = ""
        
        # Create option lookup
        self.option_lookup = {option.key: option for option in options}
        
        # Main container
        self.container = ft.Container(
            width=width,
            height=height,
            border=ft.border.all(1, ft.Colors.OUTLINE),
            border_radius=4,
            padding=5,
            bgcolor=ft.Colors.SURFACE_VARIANT if disabled else ft.Colors.SURFACE,
            on_click=self._toggle_dropdown if not disabled else None
        )
        
        # Selected items display
        self.selected_display = ft.Column(
            controls=[],
            spacing=2,
            scroll=ft.ScrollMode.AUTO
        )
        
        # Search field
        self.search_field = ft.TextField(
            hint_text="Search...",
            on_change=self._on_search_change,
            visible=False,
            dense=True,
            content_padding=5
        )
        
        # Dropdown container
        self.dropdown_container = ft.Container(
            visible=False,
            bgcolor=ft.Colors.SURFACE,
            border=ft.border.all(1, ft.Colors.OUTLINE),
            border_radius=4,
            padding=5,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=3,
                color=ft.Colors.with_opacity(0.3, ft.Colors.SHADOW),
                offset=ft.Offset(0, 2)
            )
        )
        
        # Options list
        self.options_list = ft.Column(
            controls=[],
            spacing=2,
            scroll=ft.ScrollMode.AUTO,
            height=max_height
        )
        
        self.dropdown_container.content = ft.Column([
            self.search_field,
            self.options_list
        ])
        
        self._build_ui()
    
    def _build_ui(self):
        """Build the UI components."""
        # Label
        label_control = ft.Text(
            self.label,
            size=12,
            color=ft.Colors.PRIMARY,
            weight=ft.FontWeight.BOLD
        )
        
        # Main input area
        input_area = ft.Column([
            self.selected_display,
            ft.Container(
                content=ft.Text(
                    self.hint_text if not self.selected_keys else f"{len(self.selected_keys)} item(s) selected",
                    size=12,
                    color=ft.Colors.ON_SURFACE_VARIANT
                ),
                padding=ft.padding.symmetric(vertical=5)
            )
        ])
        
        # Dropdown arrow
        arrow = ft.Icon(
            ft.Icons.ARROW_DROP_DOWN,
            size=20,
            color=ft.Colors.ON_SURFACE_VARIANT
        )
        
        self.container.content = ft.Row([
            ft.Container(content=input_area, expand=True),
            arrow
        ])
        
        # Update selected items display
        self._update_selected_display()
        self._update_options_list()
    
    def _update_selected_display(self):
        """Update the display of selected items."""
        selected_chips = []
        
        for key in self.selected_keys:
            if key in self.option_lookup:
                option = self.option_lookup[key]
                chip = ft.Container(
                    content=ft.Row([
                        ft.Text(option.text, size=12),
                        ft.IconButton(
                            ft.Icons.CLOSE,
                            icon_size=12,
                            on_click=lambda e, k=key: self._remove_selection(k),
                            tooltip="Remove"
                        )
                    ], spacing=2, tight=True),
                    bgcolor=ft.Colors.PRIMARY_CONTAINER,
                    border_radius=12,
                    padding=ft.padding.symmetric(horizontal=8, vertical=4),
                    margin=ft.margin.only(right=4, bottom=2)
                )
                selected_chips.append(chip)
        
        if selected_chips:
            # Group chips in rows to handle overflow
            self.selected_display.controls = [
                ft.Row(
                    selected_chips,
                    wrap=True,
                    spacing=0,
                    run_spacing=0
                )
            ]
        else:
            self.selected_display.controls = []
    
    def _update_options_list(self):
        """Update the options list based on search text."""
        filtered_options = []
        
        for option in self.options:
            # Filter based on search text
            if self.search_text.lower() in option.text.lower():
                # Check if already selected
                is_selected = option.key in self.selected_keys
                
                option_item = ft.Container(
                    content=ft.Row([
                        ft.Checkbox(
                            value=is_selected,
                            on_change=lambda e, k=option.key: self._toggle_selection(k)
                        ),
                        ft.Container(
                            content=ft.Text(
                                option.text,
                                size=12,
                                color=ft.Colors.PRIMARY if is_selected else ft.Colors.ON_SURFACE
                            ),
                            expand=True
                        )
                    ]),
                    padding=ft.padding.symmetric(horizontal=4, vertical=2),
                    border_radius=4,
                    on_click=lambda e, k=option.key: self._toggle_selection(k)
                )
                filtered_options.append(option_item)
        
        self.options_list.controls = filtered_options
    
    def _toggle_dropdown(self, e):
        """Toggle the dropdown visibility."""
        self.dropdown_open = not self.dropdown_open
        self.dropdown_container.visible = self.dropdown_open
        self.search_field.visible = self.dropdown_open
        
        if self.dropdown_open:
            self.search_field.focus()
        else:
            self.search_text = ""
            self.search_field.value = ""
            self._update_options_list()
        
        if hasattr(self, 'page') and self.page:
            self.page.update()
    
    def _on_search_change(self, e):
        """Handle search text change."""
        self.search_text = e.control.value
        self._update_options_list()
        if hasattr(self, 'page') and self.page:
            self.page.update()
    
    def _toggle_selection(self, key: str):
        """Toggle selection of an option."""
        if key in self.selected_keys:
            self.selected_keys.remove(key)
        else:
            self.selected_keys.append(key)
        
        self._update_selected_display()
        self._update_options_list()
        
        if self.on_change:
            self.on_change(self.selected_keys)
        
        if hasattr(self, 'page') and self.page:
            self.page.update()
    
    def _remove_selection(self, key: str):
        """Remove a selected option."""
        if key in self.selected_keys:
            self.selected_keys.remove(key)
            self._update_selected_display()
            self._update_options_list()
            
            if self.on_change:
                self.on_change(self.selected_keys)
            
            if hasattr(self, 'page') and self.page:
                self.page.update()
    
    def set_selected_keys(self, keys: List[str]):
        """Set the selected keys programmatically."""
        self.selected_keys = keys
        self._update_selected_display()
        self._update_options_list()
        if hasattr(self, 'page') and self.page:
            self.page.update()
    
    def get_selected_keys(self) -> List[str]:
        """Get the currently selected keys."""
        return self.selected_keys.copy()
    
    def get_selected_options(self) -> List[MultiSelectOption]:
        """Get the currently selected options."""
        return [self.option_lookup[key] for key in self.selected_keys if key in self.option_lookup]
    
    def _get_control_name(self):
        """Return the control name for Flet."""
        return "multiselect"
    
    def build(self):
        """Build the control."""
        self._control_content = ft.Column([
            ft.Text(self.label, size=14, weight=ft.FontWeight.BOLD),
            self.container,
            self.dropdown_container
        ])
        return self._control_content
