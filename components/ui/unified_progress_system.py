"""
Unified Modern Progress System
==============================

A single, comprehensive progress system that replaces all existing progress components.
Thread-safe, modern UI, and supports various display modes.

Author: <PERSON>elhalim <PERSON>
Company: Agevolami SRL
"""

import flet as ft
import asyncio
import threading
import time
import logging
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from enum import Enum
import math


class ProgressDisplayMode(Enum):
    """Display modes for progress indicator."""
    OVERLAY = "overlay"           # Modal overlay (blocking)
    BOTTOM_SHEET = "bottom_sheet" # Non-blocking bottom sheet
    SIDEBAR = "sidebar"           # Non-blocking sidebar
    INLINE = "inline"             # Inline progress bar
    MINIMAL = "minimal"           # Minimal status bar progress


class ProgressStatus(Enum):
    """Progress status states."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressStep:
    """Individual progress step."""
    id: str
    name: str
    weight: float = 1.0
    status: ProgressStatus = ProgressStatus.PENDING
    progress: float = 0.0
    message: str = ""
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    def duration(self) -> float:
        """Get step duration."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return 0.0


@dataclass
class ProgressOperation:
    """Complete progress operation."""
    id: str
    name: str
    steps: List[ProgressStep]
    status: ProgressStatus = ProgressStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    callback: Optional[Callable] = None
    cancellation_token: Optional[threading.Event] = None
    
    def total_weight(self) -> float:
        """Get total weight of all steps."""
        return sum(step.weight for step in self.steps)
    
    def current_progress(self) -> float:
        """Calculate current progress percentage."""
        if not self.steps:
            return 0.0
        
        total_weight = self.total_weight()
        if total_weight == 0:
            return 0.0
        
        completed_weight = 0.0
        for step in self.steps:
            if step.status == ProgressStatus.COMPLETED:
                completed_weight += step.weight
            elif step.status == ProgressStatus.RUNNING:
                completed_weight += step.weight * (step.progress / 100.0)
        
        return (completed_weight / total_weight) * 100.0


class ModernProgressBar:
    """Modern animated progress bar component."""
    
    def __init__(self, width: int = 300, height: int = 8, color: str = None):
        self.width = width
        self.height = height
        self.color = color or ft.Colors.BLUE_600
        self.value = 0.0
        
        # Create progress bar with modern styling
        self.progress_bar = ft.ProgressBar(
            width=width,
            height=height,
            value=0,
            color=self.color,
            bgcolor=ft.Colors.GREY_200,
            border_radius=height // 2
        )
        
        # Container with glow effect
        self.container = ft.Container(
            content=self.progress_bar,
            border_radius=height // 2,
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.3, self.color),
                offset=ft.Offset(0, 0)
            )
        )
    
    def update_value(self, value: float):
        """Update progress value (0-100)."""
        self.value = max(0, min(100, value))
        self.progress_bar.value = self.value / 100.0
        
        # Update glow effect based on progress
        if 0 < self.value < 100:
            glow_intensity = 0.3 + (0.2 * math.sin(self.value * math.pi / 100))
            self.container.shadow = ft.BoxShadow(
                spread_radius=1,
                blur_radius=12,
                color=ft.Colors.with_opacity(glow_intensity, self.color),
                offset=ft.Offset(0, 0)
            )
        else:
            self.container.shadow = ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, self.color),
                offset=ft.Offset(0, 0)
            )


class UnifiedProgressSystem:
    """Unified progress system with multiple display modes."""
    
    def __init__(self, page: ft.Page, mode: ProgressDisplayMode = ProgressDisplayMode.BOTTOM_SHEET):
        self.page = page
        self.mode = mode
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        
        # State
        self.current_operation: Optional[ProgressOperation] = None
        self.is_visible = False
        self.start_time: Optional[float] = None
        
        # UI Components
        self._create_ui_components()
        
        # Callbacks
        self.on_cancel: Optional[Callable] = None
        self.on_complete: Optional[Callable] = None
        
        # Track if added to page
        self._added_to_page = False
    
    def _create_ui_components(self):
        """Create UI components based on display mode."""
        # Modern progress bar
        self.progress_bar = ModernProgressBar(
            width=350 if self.mode in [ProgressDisplayMode.OVERLAY, ProgressDisplayMode.BOTTOM_SHEET] else 250,
            height=8
        )
        
        # Progress text components
        self.title_text = ft.Text(
            "Processing...",
            size=18,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.GREY_800
        )
        
        self.progress_text = ft.Text(
            "Starting...",
            size=14,
            color=ft.Colors.GREY_600
        )
        
        self.percentage_text = ft.Text(
            "0%",
            size=16,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE_600
        )
        
        self.step_text = ft.Text(
            "",
            size=12,
            color=ft.Colors.GREY_500
        )
        
        self.time_text = ft.Text(
            "",
            size=12,
            color=ft.Colors.GREY_500
        )
        
        # Animated icon
        self.status_icon = ft.Icon(
            ft.Icons.REFRESH,
            size=32,
            color=ft.Colors.BLUE_600,
            animate_rotation=ft.Animation(1000, ft.AnimationCurve.LINEAR)
        )
        
        # Action buttons
        self.cancel_button = ft.TextButton(
            "Cancel",
            on_click=self._on_cancel_click,
            style=ft.ButtonStyle(
                color=ft.Colors.RED_600,
                overlay_color=ft.Colors.with_opacity(0.1, ft.Colors.RED)
            )
        )
        
        self.close_button = ft.IconButton(
            ft.Icons.CLOSE,
            on_click=self._on_close_click,
            icon_size=20,
            tooltip="Close"
        )
        
        # Create layout based on mode
        if self.mode == ProgressDisplayMode.OVERLAY:
            self._create_overlay_layout()
        elif self.mode == ProgressDisplayMode.BOTTOM_SHEET:
            self._create_bottom_sheet_layout()
        elif self.mode == ProgressDisplayMode.SIDEBAR:
            self._create_sidebar_layout()
        elif self.mode == ProgressDisplayMode.INLINE:
            self._create_inline_layout()
        else:  # MINIMAL
            self._create_minimal_layout()
    
    def _create_overlay_layout(self):
        """Create modal overlay layout."""
        content = ft.Column([
            ft.Row([
                self.status_icon,
                ft.Column([
                    self.title_text,
                    self.progress_text
                ], expand=True, spacing=4)
            ], spacing=16),
            
            ft.Container(height=20),
            
            ft.Row([
                ft.Text("Progress:", size=12, color=ft.Colors.GREY_600),
                self.percentage_text
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            self.progress_bar.container,
            
            ft.Container(height=10),
            
            ft.Row([
                self.step_text,
                self.time_text
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            
            ft.Container(height=20),
            
            ft.Row([
                ft.Container(expand=True),
                self.cancel_button
            ])
        ], spacing=8, horizontal_alignment=ft.CrossAxisAlignment.STRETCH)
        
        # Card container
        card = ft.Card(
            content=ft.Container(
                content=content,
                padding=24,
                width=450
            ),
            elevation=12
        )
        
        # Backdrop
        self.backdrop = ft.Container(
            bgcolor=ft.Colors.with_opacity(0.6, ft.Colors.BLACK),
            expand=True,
            on_click=lambda _: None  # Prevent background clicks
        )
        
        # Main container
        self.main_container = ft.Container(
            content=ft.Stack([
                self.backdrop,
                ft.Container(
                    content=card,
                    alignment=ft.alignment.center,
                    expand=True
                )
            ]),
            visible=False,
            expand=True
        )
    
    def _create_bottom_sheet_layout(self):
        """Create bottom sheet layout."""
        content = ft.Column([
            # Drag handle
            ft.Container(
                content=ft.Container(
                    width=40,
                    height=4,
                    bgcolor=ft.Colors.GREY_400,
                    border_radius=2
                ),
                alignment=ft.alignment.center,
                padding=ft.padding.only(top=8, bottom=8)
            ),
            
            # Main content
            ft.Container(
                content=ft.Column([
                    ft.Row([
                        self.status_icon,
                        ft.Column([
                            self.title_text,
                            self.progress_text
                        ], expand=True, spacing=4),
                        self.close_button
                    ], spacing=12),
                    
                    ft.Container(height=12),
                    
                    ft.Row([
                        ft.Text("Progress:", size=12, color=ft.Colors.GREY_600),
                        self.percentage_text
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    
                    self.progress_bar.container,
                    
                    ft.Container(height=8),
                    
                    ft.Row([
                        self.step_text,
                        self.time_text
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    
                    ft.Container(height=12),
                    
                    ft.Row([
                        ft.Container(expand=True),
                        self.cancel_button
                    ])
                ], spacing=6),
                padding=16
            )
        ], spacing=0, tight=True)
        
        self.main_container = ft.Container(
            content=content,
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(top_left=16, top_right=16),
            border=ft.border.all(1, ft.Colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=20,
                color=ft.Colors.with_opacity(0.15, ft.Colors.BLACK),
                offset=ft.Offset(0, -4)
            ),
            height=200,  # Fixed height for better visibility
            bottom=0,
            left=0,
            right=0,
            visible=False,
            animate_position=ft.Animation(400, ft.AnimationCurve.EASE_OUT)
        )
    
    def _create_sidebar_layout(self):
        """Create sidebar layout."""
        content = ft.Column([
            # Header
            ft.Container(
                content=ft.Row([
                    ft.Text("Progress", size=14, weight=ft.FontWeight.BOLD),
                    self.close_button
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                padding=12,
                bgcolor=ft.Colors.GREY_50,
                border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
            ),
            
            # Content
            ft.Container(
                content=ft.Column([
                    ft.Container(
                        content=self.status_icon,
                        alignment=ft.alignment.center,
                        height=60
                    ),
                    
                    self.title_text,
                    self.progress_text,
                    
                    ft.Container(height=12),
                    
                    self.percentage_text,
                    self.progress_bar.container,
                    
                    ft.Container(height=8),
                    
                    self.step_text,
                    self.time_text,
                    
                    ft.Container(height=16),
                    
                    self.cancel_button
                ], spacing=6, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=16,
                expand=True
            )
        ], spacing=0, tight=True)
        
        self.main_container = ft.Container(
            content=content,
            width=280,
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=15,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(-2, 0)
            ),
            right=0,
            top=0,
            bottom=0,
            visible=False,
            animate_position=ft.Animation(400, ft.AnimationCurve.EASE_OUT)
        )
    
    def _create_inline_layout(self):
        """Create inline layout."""
        self.main_container = ft.Container(
            content=ft.Column([
                ft.Row([
                    self.status_icon,
                    ft.Column([
                        self.progress_text,
                        self.step_text
                    ], expand=True, spacing=2),
                    self.percentage_text
                ], spacing=12),
                
                ft.Container(height=8),
                
                self.progress_bar.container,
                
                ft.Container(height=4),
                
                self.time_text
            ], spacing=4),
            padding=16,
            bgcolor=ft.Colors.BLUE_50,
            border=ft.border.all(1, ft.Colors.BLUE_200),
            border_radius=8,
            visible=False
        )
    
    def _create_minimal_layout(self):
        """Create minimal layout."""
        self.main_container = ft.Container(
            content=ft.Row([
                self.status_icon,
                self.progress_text,
                ft.Container(expand=True),
                self.percentage_text,
                self.progress_bar.container
            ], spacing=8),
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            bgcolor=ft.Colors.GREY_100,
            visible=False
        )
    
    async def show(self, operation: ProgressOperation):
        """Show progress for operation."""
        with self._lock:
            self.current_operation = operation
            self.is_visible = True
            self.start_time = time.time()
            
            # Update UI
            self.title_text.value = operation.name
            self.progress_text.value = "Starting..."
            self.percentage_text.value = "0%"
            self.step_text.value = ""
            self.time_text.value = ""
            self.progress_bar.update_value(0)
            
            # Reset icon
            self.status_icon.name = ft.Icons.REFRESH
            self.status_icon.color = ft.Colors.BLUE_600
            
            # Add to page if needed
            if not self._added_to_page:
                self.page.overlay.append(self.main_container)
                self._added_to_page = True
                self.logger.info(f"Progress system added to page overlay (mode: {self.mode.value})")
            
            # Show with animation
            if self.mode == ProgressDisplayMode.BOTTOM_SHEET:
                # Simply show the bottom sheet without complex animation
                self.main_container.visible = True
                self.logger.info(f"Bottom sheet set to visible: {self.main_container.visible}")
            elif self.mode == ProgressDisplayMode.SIDEBAR:
                self.main_container.visible = True
                self.main_container.right = -300  # Start offscreen
                self.page.update()
                await asyncio.sleep(0.1)
                self.main_container.right = 0  # Animate to position
            else:
                self.main_container.visible = True
            
            # Start icon animation
            self._start_icon_animation()
            
            self.page.update()
            self.logger.info(f"Progress shown for operation: {operation.name}, visible: {self.main_container.visible}")
    
    def update_progress(self, progress: float, message: str = "", current_step: int = None, total_steps: int = None):
        """Update progress (thread-safe)."""
        try:
            with self._lock:
                if not self.is_visible or not self.current_operation:
                    return
                
                # Clamp progress
                progress = max(0, min(100, progress))
                
                # Update UI components
                self.percentage_text.value = f"{int(progress)}%"
                self.progress_text.value = message
                self.progress_bar.update_value(progress)
                
                # Update step indicator
                if current_step and total_steps:
                    self.step_text.value = f"Step {current_step} of {total_steps}"
                
                # Update time estimation
                if self.start_time and progress > 0:
                    elapsed = time.time() - self.start_time
                    if progress >= 100:
                        self.time_text.value = f"Completed in {self._format_time(elapsed)}"
                    else:
                        estimated_total = (elapsed / progress) * 100
                        remaining = max(0, estimated_total - elapsed)
                        self.time_text.value = f"~{self._format_time(remaining)} remaining"
                
                # Update page
                self.page.update()
                
        except Exception as e:
            self.logger.error(f"Error updating progress: {e}")
    
    async def hide(self, final_message: str = "Completed!", success: bool = True):
        """Hide progress with animation."""
        try:
            with self._lock:
                if not self.is_visible:
                    return
                
                self.is_visible = False
                
                # Stop icon animation
                self._stop_icon_animation()
                
                # Update final state
                if success:
                    self.status_icon.name = ft.Icons.CHECK_CIRCLE
                    self.status_icon.color = ft.Colors.GREEN_600
                    self.progress_text.value = final_message
                    self.percentage_text.value = "100%"
                    self.progress_bar.update_value(100)
                else:
                    self.status_icon.name = ft.Icons.ERROR
                    self.status_icon.color = ft.Colors.RED_600
                    self.progress_text.value = final_message
                
                self.page.update()
                
                # Hold final state briefly
                await asyncio.sleep(1.5)
                
                # Animate out
                if self.mode == ProgressDisplayMode.BOTTOM_SHEET:
                    self.main_container.bottom = -200
                elif self.mode == ProgressDisplayMode.SIDEBAR:
                    self.main_container.right = -300
                else:
                    self.main_container.visible = False
                
                self.page.update()
                await asyncio.sleep(0.4)
                
                # Remove from page
                if self._added_to_page:
                    self.main_container.visible = False
                    if self.main_container in self.page.overlay:
                        self.page.overlay.remove(self.main_container)
                    self._added_to_page = False
                
                self.page.update()
                self.current_operation = None
                
                # Call completion callback
                if self.on_complete:
                    self.on_complete(success)
                
                self.logger.info(f"Progress hidden with message: {final_message}")
                
        except Exception as e:
            self.logger.error(f"Error hiding progress: {e}")
    
    def _start_icon_animation(self):
        """Start icon rotation animation."""
        if self.status_icon.name == ft.Icons.REFRESH:
            self.status_icon.rotate = 0
            self.page.run_task(self._animate_icon)
    
    def _stop_icon_animation(self):
        """Stop icon animation."""
        self.status_icon.rotate = None
    
    async def _animate_icon(self):
        """Animate icon rotation."""
        try:
            rotation = 0
            while self.is_visible and self.status_icon.name == ft.Icons.REFRESH:
                rotation += 0.1
                if rotation >= 2 * 3.14159:
                    rotation = 0
                self.status_icon.rotate = rotation
                self.page.update()
                await asyncio.sleep(0.05)
        except Exception as e:
            self.logger.error(f"Error in icon animation: {e}")
    
    def _format_time(self, seconds: float) -> str:
        """Format time duration."""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"
    
    def _on_cancel_click(self, e):
        """Handle cancel button click."""
        if self.on_cancel:
            self.on_cancel()
    
    def _on_close_click(self, e):
        """Handle close button click."""
        if self.is_visible:
            self.page.run_task(lambda: self.hide("Cancelled", False))
    
    def set_cancel_callback(self, callback: Callable):
        """Set cancel callback."""
        self.on_cancel = callback
    
    def set_complete_callback(self, callback: Callable):
        """Set completion callback."""
        self.on_complete = callback


# Convenience functions for different progress modes
def create_progress_overlay(page: ft.Page) -> UnifiedProgressSystem:
    """Create modal progress overlay."""
    return UnifiedProgressSystem(page, ProgressDisplayMode.OVERLAY)


def create_progress_bottom_sheet(page: ft.Page) -> UnifiedProgressSystem:
    """Create bottom sheet progress."""
    return UnifiedProgressSystem(page, ProgressDisplayMode.BOTTOM_SHEET)


def create_progress_sidebar(page: ft.Page) -> UnifiedProgressSystem:
    """Create sidebar progress."""
    return UnifiedProgressSystem(page, ProgressDisplayMode.SIDEBAR)


def create_progress_inline(page: ft.Page) -> UnifiedProgressSystem:
    """Create inline progress."""
    return UnifiedProgressSystem(page, ProgressDisplayMode.INLINE)


def create_progress_minimal(page: ft.Page) -> UnifiedProgressSystem:
    """Create minimal progress."""
    return UnifiedProgressSystem(page, ProgressDisplayMode.MINIMAL)
