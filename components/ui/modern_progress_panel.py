"""
Modern Progress Panel
====================

A modern, non-blocking progress UI component that can be displayed as a sidebar panel
or bottom sheet instead of a modal dialog.
"""

import flet as ft
import asyncio
from typing import Optional, Dict, Any, Callable
import time
import threading
import logging
from enum import Enum

from components.ui.enhanced_ui_animations_fixed import (
    AnimatedProgressBar, PulsingIcon, LoadingDots, AnimationController
)


class ProgressPanelPosition(Enum):
    """Position options for the progress panel."""
    BOTTOM_SHEET = "bottom_sheet"
    RIGHT_SIDEBAR = "right_sidebar"
    LEFT_SIDEBAR = "left_sidebar"
    TOP_BANNER = "top_banner"


class ModernProgressPanel:
    """Modern progress panel with flexible positioning and smooth animations."""
    
    def __init__(self, page: ft.Page, position: ProgressPanelPosition = ProgressPanelPosition.BOTTOM_SHEET):
        self.page = page
        self.position = position
        self.logger = logging.getLogger(__name__)
        self.start_time = None
        self.progress_value = 0
        self.is_visible = False
        self._lock = threading.RLock()
        
        # Animation controller
        self.animation_controller = AnimationController()
        
        # Animated progress bar
        self.progress_bar = AnimatedProgressBar(
            width=300 if position in [ProgressPanelPosition.BOTTOM_SHEET, ProgressPanelPosition.TOP_BANNER] else 200,
            height=6,
            color=ft.Colors.BLUE,
            bgcolor=ft.Colors.GREY_300
        )

        # Pulsing icon
        self.pulsing_icon = PulsingIcon(
            icon=ft.Icons.ANALYTICS,
            size=24,
            color=ft.Colors.BLUE
        )

        # Loading dots for sub-tasks
        self.loading_dots = LoadingDots(size=4, color=ft.Colors.BLUE)
        
        # Progress text
        self.progress_text = ft.Text(
            "Starting...",
            size=12,
            weight=ft.FontWeight.W_500,
            color=ft.Colors.GREY_800,
            animate_opacity=300
        )
        
        # Percentage text
        self.percentage_text = ft.Text(
            "0%",
            size=16,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.BLUE,
            animate_scale=300
        )
        
        # Step indicator
        self.step_text = ft.Text(
            "",
            size=11,
            color=ft.Colors.GREY_600,
            animate_opacity=300
        )
        
        # Time estimation
        self.time_text = ft.Text(
            "Estimating...",
            size=10,
            color=ft.Colors.GREY_600
        )
        
        # Minimize/maximize button
        self.minimize_button = ft.IconButton(
            icon=ft.Icons.KEYBOARD_ARROW_DOWN,
            icon_size=16,
            on_click=self._toggle_minimize,
            tooltip="Minimize"
        )
        
        # Close button
        self.close_button = ft.IconButton(
            icon=ft.Icons.CLOSE,
            icon_size=16,
            on_click=self._on_close,
            tooltip="Close"
        )
        
        # Main content based on position
        self.content = self._create_content()
        
        # Panel container
        self.panel_container = self._create_panel_container()
        
        # Track if panel has been added to page
        self._added_to_page = False
        
        # State tracking
        self.is_minimized = False
        self.can_close = True
        
        # Callbacks
        self.on_close: Optional[Callable[[], None]] = None
        self.on_minimize: Optional[Callable[[], None]] = None
    
    def _create_content(self) -> ft.Container:
        """Create content based on panel position."""
        if self.position == ProgressPanelPosition.BOTTOM_SHEET:
            return self._create_bottom_sheet_content()
        elif self.position == ProgressPanelPosition.RIGHT_SIDEBAR:
            return self._create_sidebar_content()
        elif self.position == ProgressPanelPosition.LEFT_SIDEBAR:
            return self._create_sidebar_content()
        elif self.position == ProgressPanelPosition.TOP_BANNER:
            return self._create_banner_content()
        else:
            return self._create_bottom_sheet_content()
    
    def _create_bottom_sheet_content(self) -> ft.Container:
        """Create bottom sheet style content."""
        return ft.Container(
            content=ft.Column([
                # Header with drag handle
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Container(
                                bgcolor=ft.Colors.GREY_400,
                                width=40,
                                height=4,
                                border_radius=2
                            ),
                            alignment=ft.alignment.center,
                            expand=True
                        ),
                        self.close_button
                    ], tight=True),
                    height=30,
                    padding=ft.padding.only(top=8, bottom=8)
                ),
                
                # Progress content
                ft.Container(
                    content=ft.Column([
                        ft.Row([
                            self.pulsing_icon.container,
                            ft.Column([
                                ft.Row([
                                    self.progress_text,
                                    self.percentage_text
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                                self.progress_bar.glow_container,
                                ft.Row([
                                    self.step_text,
                                    self.time_text
                                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                            ], expand=True, spacing=5)
                        ], spacing=10),
                        
                        # Loading dots
                        ft.Container(
                            content=self.loading_dots.container,
                            alignment=ft.alignment.center,
                            height=20
                        )
                    ], spacing=10),
                    padding=ft.padding.all(15)
                )
            ], spacing=0, tight=True),
            bgcolor=ft.Colors.WHITE,
            border_radius=ft.border_radius.only(top_left=12, top_right=12),
            border=ft.border.all(1, ft.Colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, -2)
            )
        )
    
    def _create_sidebar_content(self) -> ft.Container:
        """Create sidebar style content."""
        return ft.Container(
            content=ft.Column([
                # Header
                ft.Container(
                    content=ft.Row([
                        ft.Text("Progress", size=14, weight=ft.FontWeight.BOLD),
                        ft.Row([
                            self.minimize_button,
                            self.close_button
                        ], spacing=5)
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    padding=ft.padding.all(10),
                    bgcolor=ft.Colors.GREY_50,
                    border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_300))
                ),
                
                # Progress content
                ft.Container(
                    content=ft.Column([
                        ft.Container(
                            content=self.pulsing_icon.container,
                            alignment=ft.alignment.center,
                            height=40
                        ),
                        
                        ft.Container(
                            content=self.percentage_text,
                            alignment=ft.alignment.center
                        ),
                        
                        self.progress_bar.glow_container,
                        
                        ft.Container(height=10),
                        
                        self.progress_text,
                        self.step_text,
                        self.time_text,
                        
                        ft.Container(height=10),
                        
                        ft.Container(
                            content=self.loading_dots.container,
                            alignment=ft.alignment.center
                        )
                    ], spacing=5, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                    padding=ft.padding.all(15),
                    expand=True
                )
            ], spacing=0, tight=True),
            width=280,
            bgcolor=ft.Colors.WHITE,
            border=ft.border.all(1, ft.Colors.GREY_300),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(-2 if self.position == ProgressPanelPosition.RIGHT_SIDEBAR else 2, 0)
            )
        )
    
    def _create_banner_content(self) -> ft.Container:
        """Create top banner style content."""
        return ft.Container(
            content=ft.Row([
                self.pulsing_icon.container,
                ft.Column([
                    ft.Row([
                        self.progress_text,
                        self.percentage_text
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    self.progress_bar.glow_container,
                    ft.Row([
                        self.step_text,
                        self.time_text
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
                ], expand=True, spacing=3),
                self.loading_dots.container,
                self.close_button
            ], spacing=10, alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.all(12),
            bgcolor=ft.Colors.BLUE_50,
            border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.BLUE_200))
        )
    
    def _create_panel_container(self) -> ft.Container:
        """Create the main panel container based on position."""
        if self.position == ProgressPanelPosition.BOTTOM_SHEET:
            return ft.Container(
                content=self.content,
                bottom=0,
                left=0,
                right=0,
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                animate_opacity=300,
                visible=False
            )
        elif self.position == ProgressPanelPosition.RIGHT_SIDEBAR:
            return ft.Container(
                content=self.content,
                right=0,
                top=0,
                bottom=0,
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                animate_opacity=300,
                visible=False
            )
        elif self.position == ProgressPanelPosition.LEFT_SIDEBAR:
            return ft.Container(
                content=self.content,
                left=0,
                top=0,
                bottom=0,
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                animate_opacity=300,
                visible=False
            )
        elif self.position == ProgressPanelPosition.TOP_BANNER:
            return ft.Container(
                content=self.content,
                top=0,
                left=0,
                right=0,
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                animate_opacity=300,
                visible=False
            )
        else:
            return ft.Container(
                content=self.content,
                bottom=0,
                left=0,
                right=0,
                animate_position=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT),
                animate_opacity=300,
                visible=False
            )
    
    async def show(self, initial_message: str = "Starting analysis..."):
        """Show the progress panel with animation."""
        with self._lock:
            self.is_visible = True
            self.start_time = time.time()
            self.progress_value = 0
            
            # Reset UI state
            self.progress_text.value = initial_message
            self.percentage_text.value = "0%"
            self.time_text.value = "Estimating..."
            self.step_text.value = ""
            
            # Reset progress bar
            try:
                await self.progress_bar.set_value(0)
            except:
                pass
            
            # Add to page if needed
            if not self._added_to_page:
                self.page.overlay.append(self.panel_container)
                self._added_to_page = True
                self.logger.info(f"Progress panel added to page overlay at position: {self.position.value}")
            
            # Show with animation
            self.panel_container.visible = True
            self.panel_container.opacity = 0
            
            # Set initial position (offscreen)
            if self.position == ProgressPanelPosition.BOTTOM_SHEET:
                self.panel_container.bottom = -200
            elif self.position == ProgressPanelPosition.RIGHT_SIDEBAR:
                self.panel_container.right = -300
            elif self.position == ProgressPanelPosition.LEFT_SIDEBAR:
                self.panel_container.left = -300
            elif self.position == ProgressPanelPosition.TOP_BANNER:
                self.panel_container.top = -100
            
            # Force initial update
            try:
                self.page.update()
            except Exception as e:
                self.logger.error(f"Error in initial page update: {e}")
            
            # Animate in
            await asyncio.sleep(0.05)
            self.panel_container.opacity = 1
            
            # Animate to final position
            if self.position == ProgressPanelPosition.BOTTOM_SHEET:
                self.panel_container.bottom = 0
            elif self.position == ProgressPanelPosition.RIGHT_SIDEBAR:
                self.panel_container.right = 0
            elif self.position == ProgressPanelPosition.LEFT_SIDEBAR:
                self.panel_container.left = 0
            elif self.position == ProgressPanelPosition.TOP_BANNER:
                self.panel_container.top = 0
            
            # Start animations
            try:
                self.page.run_task(self.pulsing_icon.start_pulsing)
                self.page.run_task(self.loading_dots.start_animation)
            except Exception as e:
                self.logger.error(f"Error starting animations: {e}")
            
            # Update page
            try:
                self.page.update()
                self.logger.info(f"Progress panel shown: {initial_message}")
            except Exception as e:
                self.logger.error(f"Error showing progress panel: {e}")
    
    def update_progress(self, progress: float, message: str, current_step: int = None, total_steps: int = None):
        """Update progress (thread-safe)."""
        try:
            with self._lock:
                if not self.is_visible:
                    return
                
                # Clamp progress
                progress = max(0, min(100, progress))
                self.progress_value = progress
                
                # Update UI elements
                self.percentage_text.value = f"{int(progress)}%"
                self.progress_text.value = message
                
                # Update step indicator
                if current_step and total_steps:
                    self.step_text.value = f"Step {current_step} of {total_steps}"
                
                # Update time estimation
                if self.start_time and progress > 0:
                    elapsed_time = time.time() - self.start_time
                    
                    if progress >= 100:
                        self.time_text.value = f"Completed in {self._format_time(elapsed_time)}"
                    else:
                        estimated_total_time = (elapsed_time / progress) * 100
                        remaining_time = max(0, estimated_total_time - elapsed_time)
                        self.time_text.value = f"~{self._format_time(remaining_time)} remaining"
                
                # Update progress bar
                try:
                    if hasattr(self.progress_bar, 'progress_bar'):
                        self.progress_bar.progress_bar.value = progress / 100.0
                except:
                    pass
                
                # Update page
                try:
                    self.page.update()
                except Exception as e:
                    self.logger.error(f"Error updating progress panel: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error in update_progress: {e}")
    
    async def hide(self, final_message: str = None):
        """Hide the progress panel with animation."""
        try:
            with self._lock:
                self.is_visible = False
                
                # Stop animations
                try:
                    self.pulsing_icon.stop_pulsing()
                    self.loading_dots.stop_animation()
                except Exception as e:
                    self.logger.error(f"Error stopping animations: {e}")
                
                # Show final message briefly
                if final_message:
                    self.progress_text.value = final_message
                    self.page.update()
                    await asyncio.sleep(0.5)
                
                # Animate out
                self.panel_container.opacity = 0
                
                # Animate to offscreen position
                if self.position == ProgressPanelPosition.BOTTOM_SHEET:
                    self.panel_container.bottom = -200
                elif self.position == ProgressPanelPosition.RIGHT_SIDEBAR:
                    self.panel_container.right = -300
                elif self.position == ProgressPanelPosition.LEFT_SIDEBAR:
                    self.panel_container.left = -300
                elif self.position == ProgressPanelPosition.TOP_BANNER:
                    self.panel_container.top = -100
                
                self.page.update()
                await asyncio.sleep(0.3)
                
                # Hide and remove from page
                self.panel_container.visible = False
                if self._added_to_page and self.panel_container in self.page.overlay:
                    self.page.overlay.remove(self.panel_container)
                    self._added_to_page = False
                    self.logger.info("Progress panel removed from page overlay")
                
                self.page.update()
                
        except Exception as e:
            self.logger.error(f"Error hiding progress panel: {e}")
    
    def _toggle_minimize(self, e):
        """Toggle minimize state."""
        self.is_minimized = not self.is_minimized
        
        if self.is_minimized:
            self.minimize_button.icon = ft.Icons.KEYBOARD_ARROW_UP
            # Implement minimize logic here
        else:
            self.minimize_button.icon = ft.Icons.KEYBOARD_ARROW_DOWN
            # Implement restore logic here
        
        if self.on_minimize:
            self.on_minimize()
        
        self.page.update()
    
    def _on_close(self, e):
        """Handle close button click."""
        if self.can_close:
            if self.on_close:
                self.on_close()
            else:
                # Default close behavior
                self.page.run_task(self.hide)
    
    def _format_time(self, seconds: float) -> str:
        """Format time in a human-readable way."""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            minutes = int(seconds / 60)
            secs = int(seconds % 60)
            return f"{minutes}m {secs}s"
        else:
            hours = int(seconds / 3600)
            minutes = int((seconds % 3600) / 60)
            return f"{hours}h {minutes}m"
    
    def set_close_callback(self, callback: Callable[[], None]):
        """Set the callback for close button."""
        self.on_close = callback
    
    def set_minimize_callback(self, callback: Callable[[], None]):
        """Set the callback for minimize button."""
        self.on_minimize = callback
    
    def set_closable(self, closable: bool):
        """Set whether the panel can be closed."""
        self.can_close = closable
        self.close_button.visible = closable
        self.page.update()
