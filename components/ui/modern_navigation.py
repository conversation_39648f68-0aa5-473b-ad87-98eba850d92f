"""
Modern Navigation System
=======================

Enhanced navigation components with modern UX patterns and improved usability.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass

from components.ui.modern_theme_system import get_theme


class NavigationState(Enum):
    """Navigation sidebar states."""
    COLLAPSED = "collapsed"
    EXPANDED = "expanded"
    MINI = "mini"


@dataclass
class NavigationItem:
    """Navigation item configuration."""
    id: str
    label: str
    icon: str
    route: Optional[str] = None
    badge: Optional[str] = None
    badge_variant: str = "default"
    children: Optional[List['NavigationItem']] = None
    disabled: bool = False
    tooltip: Optional[str] = None


class ModernSidebar:
    """Modern collapsible sidebar navigation."""
    
    def __init__(self,
                 navigation_items: List[NavigationItem],
                 on_navigate: Optional[Callable[[str], None]] = None,
                 on_state_change: Optional[Callable[[NavigationState], None]] = None,
                 initial_state: NavigationState = NavigationState.EXPANDED,
                 show_user_profile: bool = True,
                 user_name: Optional[str] = None,
                 user_avatar: Optional[str] = None):
        
        self.navigation_items = navigation_items
        self.on_navigate = on_navigate
        self.on_state_change = on_state_change
        self.current_state = initial_state
        self.show_user_profile = show_user_profile
        self.user_name = user_name or "User"
        self.user_avatar = user_avatar
        self.selected_item_id: Optional[str] = None
        self.theme = get_theme()
        
        # State-dependent configurations
        self.state_configs = {
            NavigationState.EXPANDED: {
                'width': 280,
                'show_labels': True,
                'show_user_details': True,
                'icon_size': 24,
                'padding': 20
            },
            NavigationState.COLLAPSED: {
                'width': 80,
                'show_labels': False,
                'show_user_details': False,
                'icon_size': 24,
                'padding': 16
            },
            NavigationState.MINI: {
                'width': 60,
                'show_labels': False,
                'show_user_details': False,
                'icon_size': 20,
                'padding': 12
            }
        }
    
    def build(self) -> ft.Control:
        """Build the modern sidebar."""
        config = self.state_configs[self.current_state]
        
        # Create sidebar content
        content = ft.Column([
            self._create_header(config),
            ft.Divider(height=1, color=self.theme.get_semantic_color('neutral', '200')),
            self._create_navigation_items(config),
            ft.Container(expand=True),  # Spacer
            self._create_footer(config) if self.show_user_profile else ft.Container()
        ], spacing=0, expand=True)
        
        # Create sidebar container
        sidebar = ft.Container(
            content=content,
            width=config['width'],
            height=float('inf'),
            bgcolor=self.theme.get_background_colors()['surface'],
            border=ft.border.only(right=ft.BorderSide(1, self.theme.get_semantic_color('neutral', '200'))),
            padding=ft.padding.symmetric(vertical=config['padding']),
            animate_size=ft.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return sidebar
    
    def _create_header(self, config: Dict[str, Any]) -> ft.Control:
        """Create sidebar header with logo and toggle button."""
        header_content = []
        
        if config['show_labels']:
            # Full header with logo and title
            header_content = [
                ft.Row([
                    ft.Icon(ft.Icons.ANALYTICS, size=32, color=self.theme.get_semantic_color('primary')),
                    ft.Column([
                        ft.Text("Hiel RnE", size=18, weight=ft.FontWeight.BOLD,
                               color=self.theme.get_text_colors()['primary']),
                        ft.Text("Modeler v4.0", size=12, 
                               color=self.theme.get_text_colors()['secondary'])
                    ], spacing=0, expand=True)
                ], spacing=12),
                ft.Container(height=12)
            ]
        else:
            # Compact header with just icon
            header_content = [
                ft.Container(
                    content=ft.Icon(ft.Icons.ANALYTICS, size=config['icon_size'],
                                  color=self.theme.get_semantic_color('primary')),
                    alignment=ft.alignment.center,
                    padding=ft.padding.symmetric(vertical=8)
                )
            ]
        
        # Add toggle button
        toggle_button = ft.IconButton(
            icon=ft.Icons.MENU_OPEN if self.current_state == NavigationState.EXPANDED else ft.Icons.MENU,
            icon_size=20,
            on_click=self._toggle_sidebar,
            tooltip="Toggle Sidebar",
            style=ft.ButtonStyle(
                color=self.theme.get_text_colors()['secondary'],
                bgcolor=ft.Colors.TRANSPARENT
            )
        )
        
        if config['show_labels']:
            header_content.append(
                ft.Row([ft.Container(expand=True), toggle_button], alignment=ft.MainAxisAlignment.END)
            )
        else:
            header_content.append(
                ft.Container(content=toggle_button, alignment=ft.alignment.center)
            )
        
        return ft.Container(
            content=ft.Column(header_content, spacing=8),
            padding=ft.padding.symmetric(horizontal=config['padding'])
        )
    
    def _create_navigation_items(self, config: Dict[str, Any]) -> ft.Control:
        """Create navigation items list."""
        nav_items = []
        
        for item in self.navigation_items:
            nav_item = self._create_navigation_item(item, config)
            nav_items.append(nav_item)
            
            # Add children if expanded and has children
            if (item.children and config['show_labels'] and 
                self.selected_item_id and self.selected_item_id.startswith(item.id)):
                for child in item.children:
                    child_item = self._create_navigation_item(child, config, is_child=True)
                    nav_items.append(child_item)
        
        return ft.Container(
            content=ft.Column(nav_items, spacing=4),
            padding=ft.padding.symmetric(horizontal=config['padding']),
            expand=True
        )
    
    def _create_navigation_item(self, item: NavigationItem, config: Dict[str, Any], 
                               is_child: bool = False) -> ft.Control:
        """Create individual navigation item."""
        is_selected = self.selected_item_id == item.id
        
        # Create item content
        content = []
        
        # Icon
        icon_color = (self.theme.get_semantic_color('primary') if is_selected 
                     else self.theme.get_text_colors()['secondary'])
        
        content.append(
            ft.Icon(item.icon, size=config['icon_size'], color=icon_color)
        )
        
        # Label and badge (if expanded)
        if config['show_labels']:
            label_content = [
                ft.Text(item.label, 
                       size=14, 
                       weight=ft.FontWeight.W_500 if is_selected else ft.FontWeight.W_400,
                       color=self.theme.get_text_colors()['primary'] if is_selected 
                            else self.theme.get_text_colors()['secondary'],
                       expand=True)
            ]
            
            # Add badge if present
            if item.badge:
                badge = ft.Container(
                    content=ft.Text(item.badge, size=10, color=ft.Colors.WHITE),
                    bgcolor=self.theme.get_semantic_color('primary'),
                    border_radius=10,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2)
                )
                label_content.append(badge)
            
            content.append(
                ft.Row(label_content, spacing=8, expand=True)
            )
        
        # Create clickable container
        item_container = ft.Container(
            content=ft.Row(content, spacing=12, alignment=ft.MainAxisAlignment.START),
            padding=ft.padding.symmetric(
                horizontal=16 if config['show_labels'] else 12,
                vertical=12
            ),
            margin=ft.margin.only(left=16 if is_child else 0),
            bgcolor=self.theme.get_semantic_color('primary', '50') if is_selected else ft.Colors.TRANSPARENT,
            border_radius=self.theme.tokens.radius['md'],
            ink=True,
            on_click=lambda _: self._handle_item_click(item),
            animate=ft.Animation(200, ft.AnimationCurve.EASE_OUT),
            tooltip=item.tooltip if not config['show_labels'] else None
        )
        
        # Add hover effect
        if not is_selected:
            item_container.animate_opacity = ft.Animation(200, ft.AnimationCurve.EASE_OUT)
        
        return item_container
    
    def _create_footer(self, config: Dict[str, Any]) -> ft.Control:
        """Create sidebar footer with user profile."""
        if not self.show_user_profile:
            return ft.Container()
        
        if config['show_labels']:
            # Full user profile
            avatar = ft.CircleAvatar(
                content=ft.Text(self.user_name[0].upper(), color=ft.Colors.WHITE),
                bgcolor=self.theme.get_semantic_color('primary'),
                radius=20
            ) if not self.user_avatar else ft.CircleAvatar(
                foreground_image_src=self.user_avatar,
                radius=20
            )
            
            return ft.Container(
                content=ft.Row([
                    avatar,
                    ft.Column([
                        ft.Text(self.user_name, size=14, weight=ft.FontWeight.W_500,
                               color=self.theme.get_text_colors()['primary']),
                        ft.Text("Financial Analyst", size=12,
                               color=self.theme.get_text_colors()['secondary'])
                    ], spacing=2, expand=True),
                    ft.IconButton(
                        icon=ft.Icons.MORE_VERT,
                        icon_size=16,
                        tooltip="User Menu"
                    )
                ], spacing=12),
                padding=ft.padding.symmetric(horizontal=config['padding'], vertical=12),
                bgcolor=self.theme.get_background_colors()['secondary'],
                border_radius=self.theme.tokens.radius['md'],
                margin=ft.margin.symmetric(horizontal=config['padding'])
            )
        else:
            # Compact user avatar
            avatar = ft.CircleAvatar(
                content=ft.Text(self.user_name[0].upper(), color=ft.Colors.WHITE, size=12),
                bgcolor=self.theme.get_semantic_color('primary'),
                radius=16
            )
            
            return ft.Container(
                content=avatar,
                alignment=ft.alignment.center,
                padding=ft.padding.symmetric(vertical=8)
            )
    
    def _toggle_sidebar(self, _):
        """Toggle sidebar state."""
        if self.current_state == NavigationState.EXPANDED:
            self.current_state = NavigationState.COLLAPSED
        else:
            self.current_state = NavigationState.EXPANDED
        
        if self.on_state_change:
            self.on_state_change(self.current_state)
    
    def _handle_item_click(self, item: NavigationItem):
        """Handle navigation item click."""
        if item.disabled:
            return
        
        self.selected_item_id = item.id
        
        if self.on_navigate and item.route:
            self.on_navigate(item.route)
    
    def select_item(self, item_id: str):
        """Programmatically select a navigation item."""
        self.selected_item_id = item_id
    
    def set_state(self, state: NavigationState):
        """Set sidebar state."""
        self.current_state = state
        if self.on_state_change:
            self.on_state_change(state)


class ModernBreadcrumb:
    """Modern breadcrumb navigation component."""
    
    def __init__(self,
                 items: List[Dict[str, str]],
                 on_navigate: Optional[Callable[[str], None]] = None,
                 separator: str = "/",
                 max_items: int = 4):
        
        self.items = items  # [{"label": "Home", "route": "/home"}, ...]
        self.on_navigate = on_navigate
        self.separator = separator
        self.max_items = max_items
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the breadcrumb navigation."""
        if not self.items:
            return ft.Container()
        
        # Truncate items if too many
        display_items = self.items
        if len(self.items) > self.max_items:
            display_items = [
                self.items[0],
                {"label": "...", "route": None},
                *self.items[-(self.max_items-2):]
            ]
        
        breadcrumb_items = []
        
        for i, item in enumerate(display_items):
            is_last = i == len(display_items) - 1
            is_ellipsis = item["label"] == "..."
            
            if is_ellipsis:
                breadcrumb_items.append(
                    ft.Text("...", size=14, color=self.theme.get_text_colors()['secondary'])
                )
            elif is_last:
                # Current page (not clickable)
                breadcrumb_items.append(
                    ft.Text(item["label"], size=14, weight=ft.FontWeight.W_500,
                           color=self.theme.get_text_colors()['primary'])
                )
            else:
                # Clickable breadcrumb item
                breadcrumb_items.append(
                    ft.TextButton(
                        text=item["label"],
                        style=ft.ButtonStyle(
                            color=self.theme.get_semantic_color('primary'),
                            padding=ft.padding.symmetric(horizontal=4, vertical=2)
                        ),
                        on_click=lambda _, route=item["route"]: self._handle_click(route)
                    )
                )
            
            # Add separator (except for last item)
            if not is_last:
                breadcrumb_items.append(
                    ft.Text(self.separator, size=14, 
                           color=self.theme.get_text_colors()['tertiary'])
                )
        
        return ft.Container(
            content=ft.Row(breadcrumb_items, spacing=8),
            padding=ft.padding.symmetric(horizontal=20, vertical=12),
            bgcolor=self.theme.get_background_colors()['secondary'],
            border=ft.border.only(bottom=ft.BorderSide(1, self.theme.get_semantic_color('neutral', '200')))
        )
    
    def _handle_click(self, route: Optional[str]):
        """Handle breadcrumb item click."""
        if route and self.on_navigate:
            self.on_navigate(route)


class ModernTopBar:
    """Modern top navigation bar with actions and search."""
    
    def __init__(self,
                 title: str,
                 actions: Optional[List[ft.Control]] = None,
                 show_search: bool = True,
                 on_search: Optional[Callable[[str], None]] = None,
                 user_menu: Optional[List[Dict[str, Any]]] = None):
        
        self.title = title
        self.actions = actions or []
        self.show_search = show_search
        self.on_search = on_search
        self.user_menu = user_menu or []
        self.theme = get_theme()
    
    def build(self) -> ft.Control:
        """Build the top navigation bar."""
        left_content = [
            ft.Text(self.title, size=20, weight=ft.FontWeight.W_600,
                   color=self.theme.get_text_colors()['primary'])
        ]
        
        right_content = []
        
        # Search bar
        if self.show_search:
            search_field = ft.TextField(
                hint_text="Search...",
                prefix_icon=ft.Icons.SEARCH,
                border_radius=self.theme.tokens.radius['full'],
                height=40,
                width=300,
                content_padding=ft.padding.symmetric(horizontal=16),
                on_submit=lambda e: self.on_search(e.control.value) if self.on_search else None
            )
            right_content.append(search_field)
        
        # Actions
        if self.actions:
            right_content.extend(self.actions)
        
        # User menu
        if self.user_menu:
            user_button = ft.IconButton(
                icon=ft.Icons.ACCOUNT_CIRCLE,
                icon_size=24,
                tooltip="User Menu"
            )
            right_content.append(user_button)
        
        return ft.Container(
            content=ft.Row([
                ft.Row(left_content, spacing=16),
                ft.Row(right_content, spacing=16)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.symmetric(horizontal=24, vertical=16),
            bgcolor=self.theme.get_background_colors()['surface'],
            border=ft.border.only(bottom=ft.BorderSide(1, self.theme.get_semantic_color('neutral', '200'))),
            height=72
        )


# Utility functions
def create_navigation_items() -> List[NavigationItem]:
    """Create default navigation items for the financial application."""
    return [
        NavigationItem(
            id="dashboard",
            label="Dashboard",
            icon=ft.Icons.DASHBOARD,
            route="/dashboard",
            tooltip="Main dashboard overview"
        ),
        NavigationItem(
            id="setup",
            label="Project Setup",
            icon=ft.Icons.SETTINGS,
            route="/setup",
            tooltip="Configure project parameters"
        ),
        NavigationItem(
            id="analysis",
            label="Analysis",
            icon=ft.Icons.ANALYTICS,
            route="/analysis",
            children=[
                NavigationItem(id="financial", label="Financial Model",
                             icon=ft.Icons.ACCOUNT_BALANCE, route="/analysis/financial"),
                NavigationItem(id="sensitivity", label="Sensitivity Analysis",
                             icon=ft.Icons.TUNE, route="/analysis/sensitivity"),
                NavigationItem(id="monte_carlo", label="Monte Carlo",
                             icon=ft.Icons.SCATTER_PLOT, route="/analysis/monte_carlo"),
                NavigationItem(id="scenarios", label="Scenarios",
                             icon=ft.Icons.COMPARE, route="/analysis/scenarios")
            ],
            tooltip="Financial analysis tools"
        ),
        NavigationItem(
            id="locations",
            label="Locations",
            icon=ft.Icons.LOCATION_ON,
            route="/locations",
            tooltip="Location comparison analysis"
        ),
        NavigationItem(
            id="validation",
            label="Validation",
            icon=ft.Icons.VERIFIED,
            route="/validation",
            badge="New",
            badge_variant="success",
            tooltip="Model validation and benchmarks"
        ),
        NavigationItem(
            id="ai_analysis",
            label="AI Analysis",
            icon=ft.Icons.AUTO_AWESOME,
            route="/ai-analysis",
            tooltip="AI Analysis tools"
        ),
        NavigationItem(
            id="export",
            label="Export & Reports",
            icon=ft.Icons.DOWNLOAD,
            route="/export",
            tooltip="Generate and export reports"
        ),
        NavigationItem(
            id="ai_settings",
            label="AI Settings",
            icon=ft.Icons.SMART_TOY,
            route="/ai_settings",
            badge="AI",
            badge_variant="primary",
            tooltip="Configure AI analysis providers and security settings"
        )
    ] 