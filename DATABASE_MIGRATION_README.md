# Database Migration: Location Comparisons

This document describes the database migration for adding location comparison functionality to the projects table.

## Overview

This migration adds two new columns to the `projects` table:
1. `comparison_locations` - ARRAY[String] (stored as <PERSON><PERSON><PERSON> TEXT in SQLite)
2. `location_comparisons` - JSONB (stored as <PERSON><PERSON><PERSON> TEXT in SQLite)

## Migration Details

### New Columns

| Column Name | Type | Default | Description |
|-------------|------|---------|-------------|
| `comparison_locations` | TEXT (JSON Array) | `'[]'` | Array of location names being compared |
| `location_comparisons` | TEXT (JSON Object) | `'{}'` | Detailed comparison data for each location |

### Data Structure

#### comparison_locations
```json
["Morocco", "Spain", "Italy"]
```

#### location_comparisons
```json
{
  "Morocco": {
    "npv": 1500000,
    "irr": 0.15,
    "capex_mw": 850000,
    "lcoe": 0.045,
    "payback_years": 8.5
  },
  "Spain": {
    "npv": 1200000,
    "irr": 0.12,
    "capex_mw": 900000,
    "lcoe": 0.050,
    "payback_years": 9.2
  }
}
```

## Migration Methods

### Method 1: Automatic Migration (Recommended)

The migration runs automatically when the `DataPersistenceService` is initialized:

```python
from services.persistence_service import DataPersistenceService

# This will automatically run migrations
persistence = DataPersistenceService()
```

### Method 2: Manual Migration Script

```bash
# Run migration only
python run_migration.py

# Run migration with test
RUN_TEST=true python run_migration.py

# Use Alembic (if configured)
USE_ALEMBIC=true python run_migration.py
```

### Method 3: Alembic Migration

```bash
# Install alembic if not already installed
pip install alembic>=1.12.0

# Run the migration
alembic upgrade head
```

## Testing

Test the migration with the provided test script:

```bash
python test_migration.py
```

This will:
1. Create a test database
2. Run the migration
3. Test data insertion and retrieval
4. Verify data integrity
5. Clean up test files

## Backwards Compatibility

The migration maintains backwards compatibility by:
- Using `ALTER TABLE ADD COLUMN` with default values
- Backfilling existing projects with empty arrays/objects
- Gracefully handling missing columns in old database versions

## Updated ORM Models

The `ProjectData` dataclass has been updated to include:

```python
@dataclass
class ProjectData:
    # ... existing fields ...
    comparison_locations: Optional[List[str]] = None
    location_comparisons: Optional[Dict[str, Any]] = None
```

## Database Schema Changes

### Before Migration
```sql
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    client_profile TEXT NOT NULL,
    project_assumptions TEXT NOT NULL,
    financial_results TEXT,
    created_at TIMESTAMP,
    modified_at TIMESTAMP,
    version INTEGER DEFAULT 1,
    tags TEXT DEFAULT '[]',
    description TEXT DEFAULT '',
    is_deleted BOOLEAN DEFAULT FALSE
);
```

### After Migration
```sql
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    client_profile TEXT NOT NULL,
    project_assumptions TEXT NOT NULL,
    financial_results TEXT,
    comparison_locations TEXT DEFAULT '[]',
    location_comparisons TEXT DEFAULT '{}',
    created_at TIMESTAMP,
    modified_at TIMESTAMP,
    version INTEGER DEFAULT 1,
    tags TEXT DEFAULT '[]',
    description TEXT DEFAULT '',
    is_deleted BOOLEAN DEFAULT FALSE
);
```

## Usage Examples

### Saving Project with Location Comparisons

```python
from services.persistence_service import DataPersistenceService, ProjectData

persistence = DataPersistenceService()

project = ProjectData(
    id="solar_project_001",
    name="Solar Farm Morocco",
    client_profile={"name": "Solar Corp"},
    project_assumptions={"capacity_mw": 100},
    comparison_locations=["Morocco", "Spain", "Italy"],
    location_comparisons={
        "Morocco": {"npv": 15000000, "irr": 0.15},
        "Spain": {"npv": 12000000, "irr": 0.12},
        "Italy": {"npv": 11000000, "irr": 0.11}
    }
)

persistence.save_project(project)
```

### Loading Project with Location Comparisons

```python
loaded_project = persistence.load_project("solar_project_001")

print(f"Comparison locations: {loaded_project.comparison_locations}")
print(f"Location comparisons: {loaded_project.location_comparisons}")
```

## Troubleshooting

### Common Issues

1. **Migration fails with "column already exists"**
   - This is expected and handled gracefully
   - The migration will skip existing columns

2. **Data type errors**
   - Ensure JSON data is properly formatted
   - Use empty defaults (`[]` and `{}`) for new projects

3. **Performance issues**
   - The migration adds indexes for better query performance
   - Large databases may take longer to migrate

### Recovery

If migration fails:
1. Restore from backup if available
2. Check database integrity
3. Re-run migration script
4. Contact support if issues persist

## Files Changed

- `services/persistence_service.py` - Updated with new columns and migration logic
- `alembic/versions/001_add_location_comparisons_column.py` - Alembic migration file
- `alembic.ini` - Alembic configuration
- `requirements.txt` - Added alembic dependency
- `test_migration.py` - Migration test script
- `run_migration.py` - Migration runner script

## Version History

- **v1.0** - Initial migration for location comparison functionality
- Added support for multiple location comparisons
- Backwards compatible with existing projects
- Automatic migration on service initialization
