"""
Storybook-style Stories for Comparison Charts
==============================================

Visual stories demonstrating different states and scenarios for comparison charts.
Similar to Storybook for React components.
"""

from typing import Dict, Any, List
import json
from pathlib import Path

# Mock flet for story rendering
import sys
sys.modules['flet'] = type('MockFlet', (), {
    'Container': lambda **kwargs: f"Container({kwargs})",
    'Column': lambda controls, **kwargs: f"Column({len(controls)} controls, {kwargs})",
    'Row': lambda controls, **kwargs: f"Row({len(controls)} controls, {kwargs})",
    'Text': lambda text, **kwargs: f"Text('{text}', {kwargs})",
    'Icon': lambda icon, **kwargs: f"Icon({icon}, {kwargs})",
    'Divider': lambda **kwargs: f"Divider({kwargs})",
    'alignment': type('Alignment', (), {'center': 'center'}),
    'Colors': type('Colors', (), {
        'GREY_400': '#9E9E9E',
        'GREY_600': '#757575',
        'GREY_50': '#FAFAFA',
        'WHITE': '#FFFFFF',
        'AMBER': '#FFC107',
        'BROWN_700': '#5D4037',
        'BAR_CHART_OUTLINED': 'bar_chart_outlined'
    }),
    'CrossAxisAlignment': type('CrossAxisAlignment', (), {'CENTER': 'center'}),
    'TextAlign': type('TextAlign', (), {'CENTER': 'center'}),
    'FontWeight': type('FontWeight', (), {'BOLD': 'bold'}),
    'border': type('Border', (), {'all': lambda w, c: f"border({w}, {c})"})
})()

from components.charts.comparison_charts import ComparisonCharts
from services.project_service import ProjectService


class ComparisonChartsStorybook:
    """Storybook-style story collection for comparison charts."""
    
    def __init__(self):
        self.project_service = None  # Will be mocked in stories
        self.comparison_charts = ComparisonCharts(project_service=self.project_service)
    
    def create_sample_data(self, locations: List[str]) -> Dict[str, Any]:
        """Create sample data for stories."""
        comparison_matrix = []
        
        # Sample data configurations for different locations
        location_configs = {
            'Ouarzazate': {
                'IRR_Project': 0.12,
                'IRR_Equity': 0.15,
                'NPV_Project_MEUR': 1.2,
                'LCOE_EUR_kWh': 0.045,
                'Min_DSCR': 1.25,
                'Capacity_Factor': 0.28
            },
            'Dakhla': {
                'IRR_Project': 0.14,
                'IRR_Equity': 0.17,
                'NPV_Project_MEUR': 1.5,
                'LCOE_EUR_kWh': 0.040,
                'Min_DSCR': 1.30,
                'Capacity_Factor': 0.32
            },
            'Tarfaya': {
                'IRR_Project': 0.13,
                'IRR_Equity': 0.16,
                'NPV_Project_MEUR': 1.35,
                'LCOE_EUR_kWh': 0.042,
                'Min_DSCR': 1.28,
                'Capacity_Factor': 0.30
            },
            'Noor Midelt': {
                'IRR_Project': 0.11,
                'IRR_Equity': 0.14,
                'NPV_Project_MEUR': 1.1,
                'LCOE_EUR_kWh': 0.048,
                'Min_DSCR': 1.22,
                'Capacity_Factor': 0.26
            },
            'Laâyoune': {
                'IRR_Project': 0.16,
                'IRR_Equity': 0.19,
                'NPV_Project_MEUR': 1.8,
                'LCOE_EUR_kWh': 0.038,
                'Min_DSCR': 1.35,
                'Capacity_Factor': 0.35
            }
        }
        
        for location in locations:
            config = location_configs.get(location, location_configs['Ouarzazate'])
            comparison_matrix.append({
                'Location': location,
                **config
            })
        
        # Create rankings
        sorted_by_irr = sorted(comparison_matrix, key=lambda x: x['IRR_Project'], reverse=True)
        sorted_by_lcoe = sorted(comparison_matrix, key=lambda x: x['LCOE_EUR_kWh'])
        
        rankings = {
            'best_irr_project': [
                {'rank': i+1, 'location': item['Location'], 'value': item['IRR_Project']}
                for i, item in enumerate(sorted_by_irr)
            ],
            'best_irr_equity': [
                {'rank': i+1, 'location': item['Location'], 'value': item['IRR_Equity']}
                for i, item in enumerate(sorted_by_irr)
            ],
            'lowest_lcoe': [
                {'rank': i+1, 'location': item['Location'], 'value': item['LCOE_EUR_kWh']}
                for i, item in enumerate(sorted_by_lcoe)
            ]
        }
        
        return {
            'locations': {loc: {'kpis': {}} for loc in locations},
            'analysis': {
                'comparison_matrix': comparison_matrix,
                'rankings': rankings,
                'recommendations': {
                    'best_overall': {
                        'location': sorted_by_irr[0]['Location'],
                        'score': 0.85,
                        'reasons': ['Highest IRR', 'Best capacity factor']
                    },
                    'best_for_returns': sorted_by_irr[0]['Location'],
                    'best_for_lcoe': sorted_by_lcoe[0]['Location'],
                    'best_for_risk': sorted_by_irr[-1]['Location']
                }
            }
        }
    
    def story_two_locations_bar_chart(self) -> Dict[str, Any]:
        """Story: Two locations comparison with bar chart."""
        locations = ['Ouarzazate', 'Dakhla']
        comparison_data = self.create_sample_data(locations)
        
        chart = self.comparison_charts.create_location_comparison_chart(
            comparison_results=comparison_data
        )
        
        return {
            'title': 'Two Locations - Bar Chart',
            'description': 'Basic comparison between two locations using bar chart',
            'args': {
                'locations': locations,
                'chart_type': 'bar'
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_three_locations_radar_chart(self) -> Dict[str, Any]:
        """Story: Three locations comparison with radar chart."""
        locations = ['Ouarzazate', 'Dakhla', 'Tarfaya']
        comparison_data = self.create_sample_data(locations)
        
        chart = self.comparison_charts.create_location_comparison_radar(
            comparison_results=comparison_data
        )
        
        return {
            'title': 'Three Locations - Radar Chart',
            'description': 'Multi-metric comparison using radar chart visualization',
            'args': {
                'locations': locations,
                'chart_type': 'radar',
                'metrics': ['IRR_Project', 'IRR_Equity', 'NPV_Project_MEUR', 'Min_DSCR', 'Capacity_Factor']
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_five_locations_unified_comparison(self) -> Dict[str, Any]:
        """Story: Five locations unified comparison."""
        locations = ['Ouarzazate', 'Dakhla', 'Tarfaya', 'Noor Midelt', 'Laâyoune']
        comparison_data = self.create_sample_data(locations)
        
        chart = self.comparison_charts.create_unified_location_comparison(
            comparison_results=comparison_data,
            chart_type='bar',
            include_rankings=True
        )
        
        return {
            'title': 'Five Locations - Unified Comparison',
            'description': 'Comprehensive comparison with multiple locations including rankings',
            'args': {
                'locations': locations,
                'chart_type': 'bar',
                'include_rankings': True
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_no_data_state(self) -> Dict[str, Any]:
        """Story: No data state."""
        chart = self.comparison_charts.create_location_comparison_chart()
        
        return {
            'title': 'No Data State',
            'description': 'How the chart appears when no data is available',
            'args': {
                'comparison_results': None
            },
            'result': str(chart),
            'data': None
        }
    
    def story_empty_comparison_matrix(self) -> Dict[str, Any]:
        """Story: Empty comparison matrix."""
        comparison_data = {
            'locations': {},
            'analysis': {
                'comparison_matrix': [],
                'rankings': {},
                'recommendations': {}
            }
        }
        
        chart = self.comparison_charts.create_location_comparison_chart(
            comparison_results=comparison_data
        )
        
        return {
            'title': 'Empty Comparison Matrix',
            'description': 'Chart behavior when comparison matrix is empty',
            'args': {
                'comparison_matrix': []
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_rankings_only(self) -> Dict[str, Any]:
        """Story: Rankings display only."""
        locations = ['Ouarzazate', 'Dakhla', 'Tarfaya']
        comparison_data = self.create_sample_data(locations)
        
        chart = self.comparison_charts.create_location_ranking_chart(comparison_data)
        
        return {
            'title': 'Rankings Only',
            'description': 'Display only the rankings component',
            'args': {
                'locations': locations,
                'show_rankings': True
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_all_chart_types(self) -> Dict[str, Any]:
        """Story: All chart types comparison."""
        locations = ['Ouarzazate', 'Dakhla', 'Tarfaya']
        comparison_data = self.create_sample_data(locations)
        
        chart_types = ['bar', 'radar', 'heatmap']
        results = {}
        
        for chart_type in chart_types:
            chart = self.comparison_charts.create_unified_location_comparison(
                comparison_results=comparison_data,
                chart_type=chart_type,
                include_rankings=False
            )
            results[chart_type] = str(chart)
        
        return {
            'title': 'All Chart Types',
            'description': 'Comparison of all available chart types',
            'args': {
                'locations': locations,
                'chart_types': chart_types
            },
            'result': results,
            'data': comparison_data
        }
    
    def story_dashboard_integration(self) -> Dict[str, Any]:
        """Story: Dashboard integration scenario."""
        locations = ['Ouarzazate', 'Dakhla']
        comparison_data = self.create_sample_data(locations)
        
        # Simulate dashboard usage with project_id
        chart = self.comparison_charts.create_location_comparison_radar(
            project_id='dashboard_project_123',
            comparison_results=comparison_data,  # Fallback data
            metrics=['IRR_Project', 'Min_DSCR', 'Capacity_Factor']
        )
        
        return {
            'title': 'Dashboard Integration',
            'description': 'How charts appear in the dashboard context',
            'args': {
                'project_id': 'dashboard_project_123',
                'context': 'dashboard',
                'chart_type': 'radar'
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def story_dedicated_comparison_screen(self) -> Dict[str, Any]:
        """Story: Dedicated comparison screen scenario."""
        locations = ['Ouarzazate', 'Dakhla', 'Tarfaya', 'Noor Midelt']
        comparison_data = self.create_sample_data(locations)
        
        # Simulate dedicated comparison screen
        chart = self.comparison_charts.create_unified_location_comparison(
            comparison_results=comparison_data,
            chart_type='bar',
            include_rankings=True
        )
        
        return {
            'title': 'Dedicated Comparison Screen',
            'description': 'Full comparison screen with all features',
            'args': {
                'locations': locations,
                'context': 'comparison_screen',
                'chart_type': 'bar',
                'include_rankings': True
            },
            'result': str(chart),
            'data': comparison_data
        }
    
    def generate_all_stories(self) -> Dict[str, Any]:
        """Generate all stories for the storybook."""
        stories = {
            'two_locations_bar_chart': self.story_two_locations_bar_chart(),
            'three_locations_radar_chart': self.story_three_locations_radar_chart(),
            'five_locations_unified_comparison': self.story_five_locations_unified_comparison(),
            'no_data_state': self.story_no_data_state(),
            'empty_comparison_matrix': self.story_empty_comparison_matrix(),
            'rankings_only': self.story_rankings_only(),
            'all_chart_types': self.story_all_chart_types(),
            'dashboard_integration': self.story_dashboard_integration(),
            'dedicated_comparison_screen': self.story_dedicated_comparison_screen()
        }
        
        return {
            'title': 'Comparison Charts Storybook',
            'description': 'Visual stories for location comparison charts',
            'stories': stories
        }
    
    def save_stories(self, output_path: str = None):
        """Save stories to JSON file."""
        if output_path is None:
            output_path = Path(__file__).parent / 'snapshots' / 'comparison_charts_stories.json'
        
        stories = self.generate_all_stories()
        
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, 'w') as f:
            json.dump(stories, f, indent=2, default=str)
        
        return output_path


if __name__ == '__main__':
    storybook = ComparisonChartsStorybook()
    output_file = storybook.save_stories()
    print(f"Stories saved to: {output_file}")
    
    # Print summary
    stories = storybook.generate_all_stories()
    print(f"\nGenerated {len(stories['stories'])} stories:")
    for story_name, story_data in stories['stories'].items():
        print(f"  - {story_data['title']}: {story_data['description']}")
