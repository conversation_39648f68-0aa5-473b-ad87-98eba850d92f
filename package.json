{"name": "hiel-rne-modeler-tests", "version": "1.0.0", "description": "E2E tests for Hiel RnE Modeler application", "main": "cypress.config.js", "scripts": {"cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox", "test:e2e": "cypress run --spec 'cypress/e2e/**/*'", "test:accessibility": "cypress run --spec 'cypress/e2e/accessibility_location_selector.cy.js'", "test:location-comparison": "cypress run --spec 'cypress/e2e/create_project_location_comparison.cy.js'"}, "keywords": ["cypress", "e2e", "testing", "accessibility", "flet", "solar", "morocco"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"cypress": "^13.6.0", "cypress-axe": "^1.5.0", "cypress-real-events": "^1.11.0", "cypress-keyboard-plugin": "^1.1.0"}, "dependencies": {"axe-core": "^4.8.3"}}