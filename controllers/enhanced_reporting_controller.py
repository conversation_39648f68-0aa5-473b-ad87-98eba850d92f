"""
Enhanced Reporting Controller
=============================

Controller that manages the integration between the UI and the enhanced reporting system.
Provides high-level interface for report generation with AI analysis.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
import json

from services.enhanced_reporting_integration import EnhancedReportingIntegration, ReportingConfig, ReportingMode, ReportingResult
from models.client_profile import ClientProfile
from models.enhanced_project_assumptions import EnhancedProjectAssumptions


class EnhancedReportingController:
    """Controller for enhanced reporting functionality."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize integration service
        self.integration_service = None
        self.is_initialized = False
        
        # Configuration
        self.config_path = Path('config/enhanced_reporting_config.json')
        self.default_output_directory = Path('output/enhanced_reports')
        self.default_output_directory.mkdir(parents=True, exist_ok=True)
        
        # Initialize service
        self._initialize_service()
    
    def _initialize_service(self):
        """Initialize the enhanced reporting integration service."""
        
        try:
            self.integration_service = EnhancedReportingIntegration(self.config_path)
            self.is_initialized = True
            self.logger.info("Enhanced reporting controller initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced reporting service: {e}")
            self.is_initialized = False
    
    async def generate_enhanced_report(self,
                                     client_profile: ClientProfile,
                                     assumptions: EnhancedProjectAssumptions,
                                     output_directory: Optional[Path] = None,
                                     report_config: Optional[ReportingConfig] = None) -> ReportingResult:
        """Generate enhanced report with AI analysis."""
        
        if not self.is_initialized:
            self._initialize_service()
        
        if not self.is_initialized:
            return ReportingResult(
                success=False,
                report_id="failed_init",
                generated_files={},
                processing_time=0.0,
                errors=["Enhanced reporting service not initialized"]
            )
        
        output_dir = output_directory or self.default_output_directory
        output_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            result = await self.integration_service.generate_comprehensive_report(
                client_profile,
                assumptions,
                output_dir,
                report_config
            )
            
            self.logger.info(f"Enhanced report generated: {result.report_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Enhanced report generation failed: {e}")
            return ReportingResult(
                success=False,
                report_id="failed_generation",
                generated_files={},
                processing_time=0.0,
                errors=[f"Report generation failed: {str(e)}"]
            )
    
    async def generate_ai_powered_report(self,
                                       client_profile: ClientProfile,
                                       assumptions: EnhancedProjectAssumptions,
                                       ai_config: Dict[str, Any],
                                       output_directory: Optional[Path] = None) -> ReportingResult:
        """Generate AI-powered report with custom AI configuration."""
        
        # Update AI configuration
        self.update_ai_configuration(ai_config)
        
        # Create AI-powered configuration
        config = ReportingConfig(
            mode=ReportingMode.AI_POWERED,
            include_ai_analysis=True,
            include_professional_styling=True,
            include_security_features=True,
            include_charts=True,
            include_validation=True,
            parallel_processing=True,
            export_formats=['PDF', 'HTML', 'DOCX'],
            ai_config=ai_config
        )
        
        return await self.generate_enhanced_report(
            client_profile,
            assumptions,
            output_directory,
            config
        )
    
    async def generate_professional_report(self,
                                         client_profile: ClientProfile,
                                         assumptions: EnhancedProjectAssumptions,
                                         branding_config: Optional[Dict[str, Any]] = None,
                                         output_directory: Optional[Path] = None) -> ReportingResult:
        """Generate professional report with custom branding."""
        
        config = ReportingConfig(
            mode=ReportingMode.PROFESSIONAL,
            include_ai_analysis=True,
            include_professional_styling=True,
            include_security_features=True,
            include_charts=True,
            include_validation=True,
            parallel_processing=True,
            export_formats=['PDF', 'HTML', 'DOCX', 'PPTX'],
            pdf_config=branding_config
        )
        
        return await self.generate_enhanced_report(
            client_profile,
            assumptions,
            output_directory,
            config
        )
    
    async def generate_sample_report(self, output_directory: Optional[Path] = None) -> ReportingResult:
        """Generate sample report for testing."""
        
        if not self.is_initialized:
            self._initialize_service()
        
        if not self.is_initialized:
            return ReportingResult(
                success=False,
                report_id="failed_init",
                generated_files={},
                processing_time=0.0,
                errors=["Enhanced reporting service not initialized"]
            )
        
        output_dir = output_directory or self.default_output_directory
        
        try:
            result = await self.integration_service.generate_sample_report(output_dir)
            self.logger.info(f"Sample report generated: {result.report_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Sample report generation failed: {e}")
            return ReportingResult(
                success=False,
                report_id="failed_sample",
                generated_files={},
                processing_time=0.0,
                errors=[f"Sample report generation failed: {str(e)}"]
            )
    
    def update_ai_configuration(self, ai_config: Dict[str, Any]):
        """Update AI configuration."""
        
        if not self.is_initialized:
            self._initialize_service()
        
        if self.is_initialized:
            try:
                self.integration_service.update_ai_configuration(ai_config)
                self.logger.info("AI configuration updated")
            except Exception as e:
                self.logger.error(f"Failed to update AI configuration: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all enhanced reporting services."""
        
        if not self.is_initialized:
            return {
                'initialized': False,
                'error': 'Enhanced reporting service not initialized'
            }
        
        try:
            status = self.integration_service.get_service_status()
            status['initialized'] = True
            status['controller_version'] = '2.0'
            return status
            
        except Exception as e:
            return {
                'initialized': False,
                'error': f"Failed to get service status: {str(e)}"
            }
    
    def get_generation_history(self) -> List[ReportingResult]:
        """Get report generation history."""
        
        if not self.is_initialized:
            return []
        
        try:
            return self.integration_service.get_generation_history()
        except Exception as e:
            self.logger.error(f"Failed to get generation history: {e}")
            return []
    
    def get_available_export_formats(self) -> List[str]:
        """Get available export formats."""
        return ['PDF', 'HTML', 'DOCX', 'PPTX']
    
    def get_available_reporting_modes(self) -> List[str]:
        """Get available reporting modes."""
        return [mode.value for mode in ReportingMode]
    
    def create_reporting_config(self,
                               mode: str = 'enhanced',
                               include_ai: bool = True,
                               include_professional_styling: bool = True,
                               include_security: bool = True,
                               export_formats: Optional[List[str]] = None,
                               ai_config: Optional[Dict[str, Any]] = None) -> ReportingConfig:
        """Create reporting configuration."""
        
        return ReportingConfig(
            mode=ReportingMode(mode),
            include_ai_analysis=include_ai,
            include_professional_styling=include_professional_styling,
            include_security_features=include_security,
            include_charts=True,
            include_validation=True,
            parallel_processing=True,
            export_formats=export_formats or ['PDF', 'HTML', 'DOCX'],
            ai_config=ai_config
        )
    
    def save_configuration(self, config: ReportingConfig):
        """Save configuration to file."""
        
        if not self.is_initialized:
            self._initialize_service()
        
        if self.is_initialized:
            try:
                self.integration_service.save_configuration(self.config_path)
                self.logger.info("Configuration saved successfully")
            except Exception as e:
                self.logger.error(f"Failed to save configuration: {e}")
    
    async def test_ai_connection(self, ai_config: Dict[str, Any]) -> Dict[str, Any]:
        """Test AI connection with provided configuration."""
        
        try:
            # Temporarily update AI configuration
            original_config = self.integration_service.config.ai_config if self.is_initialized else None
            
            if self.is_initialized:
                self.integration_service.update_ai_configuration(ai_config)
            
            # Test connection (simplified)
            test_result = {
                'success': True,
                'provider': ai_config.get('provider', 'unknown'),
                'model': ai_config.get('model_id', 'unknown'),
                'timestamp': datetime.now().isoformat(),
                'message': 'Connection test successful'
            }
            
            # Restore original configuration
            if self.is_initialized and original_config:
                self.integration_service.update_ai_configuration(original_config)
            
            return test_result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'message': 'Connection test failed'
            }
    
    def get_default_ai_config(self) -> Dict[str, Any]:
        """Get default AI configuration."""
        
        return {
            'provider': 'openai',
            'api_url': 'https://api.openai.com/v1/chat/completions',
            'api_key': '',
            'model_id': 'gpt-4',
            'system_prompt': '''You are a professional financial analyst specializing in renewable energy projects.
Analyze the provided financial data and generate comprehensive insights.

Your analysis should include:
1. Executive summary of financial performance
2. Key strengths and weaknesses
3. Risk assessment with specific recommendations
4. Market positioning analysis
5. Investment recommendation with rationale
6. Strategic next steps

Format your response as structured content with clear sections.
Be precise, professional, and focus on actionable insights.
Avoid speculation and base all conclusions on the provided data.
Use industry-standard financial terminology and metrics.''',
            'max_tokens': 4000,
            'temperature': 0.3,
            'timeout': 30
        }
    
    def get_default_pdf_config(self) -> Dict[str, Any]:
        """Get default PDF configuration."""
        
        return {
            'page_size': 'A4',
            'margins': {
                'top': 2.5,
                'bottom': 2.5,
                'left': 2.0,
                'right': 2.0
            },
            'font_family': 'Helvetica',
            'font_size': 10,
            'company_name': 'Professional Financial Analysis',
            'primary_color': '#2E86AB',
            'secondary_color': '#A23B72',
            'accent_color': '#F18F01',
            'footer_text': 'Confidential Financial Analysis Report'
        }
    
    def validate_report_inputs(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Validate report inputs before generation."""
        
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Validate client profile
        if not client_profile.company_name:
            validation_result['errors'].append("Company name is required")
            validation_result['valid'] = False
        
        if not client_profile.project_name:
            validation_result['errors'].append("Project name is required")
            validation_result['valid'] = False
        
        # Validate assumptions
        if assumptions.capacity_mw <= 0:
            validation_result['errors'].append("Project capacity must be positive")
            validation_result['valid'] = False
        
        if assumptions.capex_meur <= 0:
            validation_result['errors'].append("CAPEX must be positive")
            validation_result['valid'] = False
        
        if assumptions.project_life_years <= 0:
            validation_result['errors'].append("Project life must be positive")
            validation_result['valid'] = False
        
        if not (0 < assumptions.equity_percentage <= 1):
            validation_result['errors'].append("Equity percentage must be between 0 and 1")
            validation_result['valid'] = False
        
        # Warnings
        if assumptions.discount_rate > 0.15:
            validation_result['warnings'].append("Discount rate appears high (>15%)")
        
        if assumptions.debt_interest_rate > 0.10:
            validation_result['warnings'].append("Debt interest rate appears high (>10%)")
        
        return validation_result
    
    def get_report_preview_data(self, client_profile: ClientProfile, assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Get preview data for report generation."""
        
        return {
            'client_info': {
                'company_name': client_profile.company_name,
                'project_name': client_profile.project_name,
                'location': client_profile.project_location,
                'consultant': client_profile.consultant
            },
            'project_overview': {
                'capacity': f"{assumptions.capacity_mw} MW",
                'technology': assumptions.technology_type,
                'project_life': f"{assumptions.project_life_years} years",
                'capex': f"€{assumptions.capex_meur:.1f}M",
                'opex': f"€{assumptions.opex_meur_per_year:.1f}M/year"
            },
            'financial_structure': {
                'equity_percentage': f"{assumptions.equity_percentage:.1%}",
                'debt_percentage': f"{1-assumptions.equity_percentage:.1%}",
                'debt_interest': f"{assumptions.debt_interest_rate:.2%}",
                'discount_rate': f"{assumptions.discount_rate:.2%}"
            }
        }