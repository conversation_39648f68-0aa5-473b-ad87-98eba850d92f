"""
Application Controller
======================

Main application controller coordinating between views and services.
"""

import flet as ft
import logging
import asyncio
import time
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
from datetime import datetime

# State Management
from app.app_state import AppState
from models.ui_state import UIState, TabState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

# Services
from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from services.location_service import LocationComparisonService
from services.report_service import ReportGenerationService

# Views
from views.project_setup_view import ProjectSetupView
from views.dashboard_view import DashboardView
from views.location_comparison_view import LocationComparisonView
from views.financial_model_view import FinancialModelView
from views.validation_view import ValidationView
from views.ai_analysis_view import AIAnalysisView

# UI Components
# from components.ui.modern_navigation import ModernSidebar, ModernTopBar


class AppController:
    """Main application controller coordinating between views and services."""
    
    def __init__(self, page: ft.Page):
        """Initialize the AppController with the given page."""
        self.page = page
        self.logger = logging.getLogger(__name__)
        
        # Initialize state management
        self.app_state = AppState()
        self.ui_state = UIState()
        
        # Initialize services
        self._initialize_services()
        
        # Initialize UI components
        self._initialize_ui_components()
        
        # Initialize views
        self._initialize_views()
        
        # Setup page layout
        self._setup_page_layout()
        
        # Initial navigation
        self.navigate_to_tab(TabState.PROJECT_SETUP)
    
    def _initialize_services(self):
        """Initialize all required services."""
        try:
            self.financial_service = FinancialModelService()
            self.validation_service = ValidationService()
            self.export_service = ExportService()
            self.location_service = LocationComparisonService()
            self.report_service = ReportGenerationService()
            self.logger.info("Services initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {e}")
            self.show_error("Failed to initialize application services")
    
    def _initialize_ui_components(self):
        """Initialize UI components."""
        try:
            # Create simple sidebar for now
            self.sidebar = self._create_simple_sidebar()
            
            # Create simple topbar
            self.topbar = self._create_simple_topbar()
            
            self.logger.info("UI components initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize UI components: {e}")
            self.show_error("Failed to initialize UI components")
    
    def _create_simple_sidebar(self):
        """Create a simple sidebar navigation."""
        navigation_items = [
            ft.NavigationRailDestination(
                icon=ft.Icons.SETTINGS_OUTLINED,
                selected_icon=ft.Icons.SETTINGS,
                label="Project Setup"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.DASHBOARD_OUTLINED,
                selected_icon=ft.Icons.DASHBOARD,
                label="Dashboard"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.MAP_OUTLINED,
                selected_icon=ft.Icons.MAP,
                label="Location Comparison"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.CALCULATE_OUTLINED,
                selected_icon=ft.Icons.CALCULATE,
                label="Financial Model"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.VERIFIED_OUTLINED,
                selected_icon=ft.Icons.VERIFIED,
                label="Validation"
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.SMART_TOY_OUTLINED,
                selected_icon=ft.Icons.SMART_TOY,
                label="AI Analysis"
            )
        ]
        
        return ft.NavigationRail(
            destinations=navigation_items,
            on_change=self._handle_navigation,
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=200,
            min_extended_width=250
        )
    
    def _create_simple_topbar(self):
        """Create a simple topbar."""
        return ft.AppBar(
            title=ft.Text("Hiel RnE Modeler v3.0"),
            actions=[
                ft.IconButton(
                    icon=ft.Icons.DOWNLOAD,
                    tooltip="Export",
                    on_click=lambda e: self._handle_export("excel")
                ),
                ft.IconButton(
                    icon=ft.Icons.SETTINGS,
                    tooltip="Settings",
                    on_click=lambda e: self._handle_settings()
                )
            ]
        )
    
    def _initialize_views(self):
        """Initialize all views."""
        try:
            self.views = {
                TabState.PROJECT_SETUP: ProjectSetupView(self.page),
                TabState.DASHBOARD: DashboardView(self.page),
                TabState.LOCATION_COMPARISON: LocationComparisonView(self.page),
                TabState.FINANCIAL_MODEL: FinancialModelView(self.page),
                TabState.VALIDATION: ValidationView(self.page),
                TabState.AI_ANALYSIS: AIAnalysisView(self.page)
            }
            
            self.logger.info("Views initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize views: {e}")
            self.show_error("Failed to initialize application views")
    
    def _setup_page_layout(self):
        """Setup the main page layout."""
        try:
            # Create main container
            self.main_container = ft.Container(
                content=ft.Column([
                    # Top bar
                    self.topbar,
                    
                    # Main content area
                    ft.Row([
                        # Sidebar
                        self.sidebar,
                        
                        # Content area
                        ft.Container(
                            content=ft.Column([
                                # Content will be updated by navigate_to_tab
                            ]),
                            expand=True,
                            padding=20
                        )
                    ], expand=True)
                ], expand=True),
                expand=True
            )
            
            self.page.add(self.main_container)
            self.logger.info("Page layout setup completed")
        except Exception as e:
            self.logger.error(f"Failed to setup page layout: {e}")
            self.show_error("Failed to setup application layout")
    
    def navigate_to_tab(self, tab_state: TabState):
        """Navigate to the specified tab."""
        try:
            self.ui_state.current_tab = tab_state
            
            # Update sidebar selection
            tab_index_map = {
                TabState.PROJECT_SETUP: 0,
                TabState.DASHBOARD: 1,
                TabState.LOCATION_COMPARISON: 2,
                TabState.FINANCIAL_MODEL: 3,
                TabState.VALIDATION: 4,
                TabState.AI_ANALYSIS: 5
            }
            
            if tab_state in tab_index_map:
                self.sidebar.selected_index = tab_index_map[tab_state]
                self.sidebar.update()
            
            # Get the view for this tab
            if tab_state in self.views:
                view = self.views[tab_state]
                
                # Update content area
                content_container = self.main_container.content.controls[1].controls[1]
                content_container.content = view.get_content()
                content_container.update()
            
            self.logger.info(f"Navigated to tab: {tab_state}")
        except Exception as e:
            self.logger.error(f"Failed to navigate to tab {tab_state}: {e}")
            self.show_error(f"Failed to navigate to {tab_state}")
    
    def _handle_navigation(self, e):
        """Handle navigation requests from UI components."""
        tab_map = {
            0: TabState.PROJECT_SETUP,
            1: TabState.DASHBOARD,
            2: TabState.LOCATION_COMPARISON,
            3: TabState.FINANCIAL_MODEL,
            4: TabState.VALIDATION,
            5: TabState.AI_ANALYSIS
        }
        
        if e.control.selected_index in tab_map:
            self.navigate_to_tab(tab_map[e.control.selected_index])
    
    def _handle_export(self, export_type: str):
        """Handle export requests."""
        try:
            # Implement export logic
            self.show_success(f"Export {export_type} started")
        except Exception as e:
            self.logger.error(f"Failed to handle export: {e}")
            self.show_error("Export failed")
    
    def _handle_settings(self):
        """Handle settings requests."""
        try:
            # Implement settings logic
            self.show_success("Settings opened")
        except Exception as e:
            self.logger.error(f"Failed to handle settings: {e}")
            self.show_error("Failed to open settings")
    
    def handle_data_change(self, data_type: str, data: Any):
        """Handle data changes from views."""
        try:
            if data_type == "client_profile":
                self.app_state.client_profile = data
            elif data_type == "project_assumptions":
                self.app_state.project_assumptions = data
            
            self.logger.info(f"Data updated: {data_type}")
        except Exception as e:
            self.logger.error(f"Failed to handle data change: {e}")
            self.show_error("Failed to update data")
    
    def handle_action_request(self, action: str, params: Dict[str, Any] = None):
        """Handle action requests from views."""
        try:
            if action == "run_financial_model":
                self._run_financial_model()
            elif action == "run_validation":
                self._run_validation()
            elif action == "run_location_comparison":
                self._run_location_comparison()
            
            self.logger.info(f"Action handled: {action}")
        except Exception as e:
            self.logger.error(f"Failed to handle action {action}: {e}")
            self.show_error(f"Failed to execute {action}")
    
    def _run_financial_model(self):
        """Run the financial model."""
        try:
            # Run financial model
            results = self.financial_service.calculate_financial_model(
                self.app_state.client_profile,
                self.app_state.project_assumptions
            )
            
            self.app_state.financial_results = results
            self.show_success("Financial model calculated successfully")
        except Exception as e:
            self.logger.error(f"Failed to run financial model: {e}")
            self.show_error("Failed to run financial model")
    
    def _run_validation(self):
        """Run model validation."""
        try:
            # Run validation
            results = self.validation_service.validate_model(
                self.app_state.financial_results
            )
            
            self.app_state.validation_results = results
            self.show_success("Model validation completed")
        except Exception as e:
            self.logger.error(f"Failed to run validation: {e}")
            self.show_error("Failed to run validation")
    
    def _run_location_comparison(self):
        """Run location comparison."""
        try:
            # Run location comparison
            results = self.location_service.compare_locations(
                self.app_state.project_assumptions
            )
            
            self.app_state.location_results = results
            self.show_success("Location comparison completed")
        except Exception as e:
            self.logger.error(f"Failed to run location comparison: {e}")
            self.show_error("Failed to run location comparison")
    
    def show_success(self, message: str):
        """Show success message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.GREEN_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show success message: {e}")
    
    def show_error(self, message: str):
        """Show error message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.RED_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show error message: {e}")
    
    def show_warning(self, message: str):
        """Show warning message."""
        try:
            snack_bar = ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.Colors.ORANGE_400
            )
            self.page.overlay.append(snack_bar)
            snack_bar.open = True
            self.page.update()
        except Exception as e:
            self.logger.error(f"Failed to show warning message: {e}")