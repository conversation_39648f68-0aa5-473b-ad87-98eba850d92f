"""
Analytics Task
==============

Celery task for updating project analytics.
"""

from celery import shared_task
import logging
from models.project_analytics import ProjectAnalytics


logger = logging.getLogger(__name__)


@shared_task(bind=True)
def update_project_analytics(self, project_id: str, analytics_data: dict):
    """Update project analytics as a background task."""
    
    logger.info(f"Updating analytics for project: {project_id}")
    
    try:
        # Load or create analytics
        analytics = ProjectAnalytics(project_id=project_id)
        
        # Update with new data
        if 'location_comparisons' in analytics_data:
            analytics.update_location_comparisons(analytics_data['location_comparisons'])
        
        # Set status
        analytics.set_comparison_status(analytics_data.get('status', 'completed'))
        
        logger.info(f"Analytics updated for project: {project_id}")
        return analytics.get_preliminary_data()
        
    except Exception as e:
        logger.error(f"Error updating analytics for project: {project_id}: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
