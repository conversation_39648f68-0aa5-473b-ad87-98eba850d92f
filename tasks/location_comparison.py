"""
Location Comparison Task
========================

Celery task for running location comparisons asynchronously.
"""

from celery import shared_task
import logging
from services.location_service import LocationComparisonService
from services.persistence_service import DataPersistenceService
from models.project_analytics import ProjectAnalytics
from models.project_assumptions import EnhancedProjectAssumptions


logger = logging.getLogger(__name__)


@shared_task(bind=True)
def run_location_comparison(self, project_id: str, comparison_locations: list):
    """Run location comparison analysis as a background task."""
    
    logger.info(f"Starting location comparison for project: {project_id} with locations {comparison_locations}")
    
    try:
        # Initialize services
        location_service = LocationComparisonService()
        persistence_service = DataPersistenceService()
        
        # Load project data
        project = persistence_service.load_project(project_id)
        if not project:
            raise ValueError(f"Project {project_id} not found")
        
        # Load or create analytics
        analytics = persistence_service.load_project_analytics(project_id)
        if not analytics:
            analytics = ProjectAnalytics(project_id=project_id)
        
        analytics.set_comparison_status("running")
        persistence_service.save_project_analytics(analytics)
        
        # Convert project assumptions to the right format
        assumptions = EnhancedProjectAssumptions.from_dict(project.project_assumptions)
        
        # Perform location comparison
        comparison_results = location_service.compare_locations(
            base_assumptions=assumptions,
            location_names=comparison_locations
        )
        
        # Store results in analytics
        analytics.update_location_comparisons(comparison_results)
        
        # Save analytics to database
        persistence_service.save_project_analytics(analytics)
        
        logger.info(f"Location comparison completed for project: {project_id}")
        return analytics.get_preliminary_data()
        
    except Exception as e:
        # Handle task failure
        try:
            analytics.set_comparison_status("failed", metadata={'error': str(e)})
            persistence_service.save_project_analytics(analytics)
        except:
            # If we can't save analytics, just log it
            logger.error(f"Failed to save analytics status for project {project_id}")
        
        logger.error(f"Error running location comparison for project: {project_id}: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
